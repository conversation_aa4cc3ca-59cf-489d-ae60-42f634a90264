
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Novice;
using System;
using GameFramework.Event;
using Game.Hotfix.Config;
using DG.Tweening;

namespace Game.Hotfix
{
    /// <summary>
    /// 竞技场页签类型
    /// </summary>
    public enum PeakArenaTabType
    {
        /// <summary>
        /// 新兵训练
        /// </summary>
        NewSoldier = 0,

        /// <summary>
        /// 巅峰竞技场
        /// </summary>
        two = 1,

        /// <summary>
        /// 333
        /// </summary>
        three = 2,

        /// <summary>
        /// 444
        /// </summary>
        four = 3
    }

    public partial class UIPeakArenaForm : UGuiFormEx
    {
        private List<NoviceRank> rankDataList = new List<NoviceRank>();
        public List<UIToggle> toggleList;
        // 当前选中的页签索引
        private int clickTogIndex = -1;
        private int oldClickTogIndex = 0;
        // 当前页签的滑动列表数据
        private List<ArenaScrollData> currentScrollDataList = new List<ArenaScrollData>();
        public int TopNum = 3;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
        }
        public void ClickTab()
        {
            // 加载当前页签的数据

            foreach (var item in toggleList)
            {
                Transform selectNode = item.transform.Find("select");
                if (selectNode != null)
                    selectNode.gameObject.SetActive(item.toggleType == clickTogIndex);
            }
            LoadScrollDataByTabIndex();
            if (currentScrollDataList.Count <= 0)
            {
                Debug.LogError("currentScrollDataList.Count <= 0");
                return;
            }

            // 刷新顶部三名玩家
            RefreshTopPlayers();

            OnRefreshPanel();
            //刷新倒计时
            InitCountdownTimer();
            // 设置TableView的数据
            GameObject obj = m_goItem;
            m_TableViewD.GetCellCount = (tableView) => { return Math.Max(currentScrollDataList.Count - TopNum, 0); };
            m_TableViewD.GetCellSize = (tableView, index) =>
            {
                // 根据当前选中的页签类型返回不同的单元格高度
                switch ((PeakArenaTabType)clickTogIndex)
                {
                    case PeakArenaTabType.NewSoldier:
                        return new Vector2(1000, 155);
                    case PeakArenaTabType.two:
                        if (GameEntry.LogicData.PeakRankData.IsTabLabel(index))
                        {
                            return new Vector2(1000, 80);
                        }
                        return new Vector2(1000, 155);
                    case PeakArenaTabType.three:
                        return new Vector2(1000, 155);
                    case PeakArenaTabType.four:
                        return new Vector2(1000, 155);
                    default:
                        return new Vector2(1000, 155);
                }
            };
            m_TableViewD.UpdateCell = (tableView, index) =>
            {
                if (clickTogIndex == (int)PeakArenaTabType.two)
                {
                    m_TableViewD.OnCellHeightChanged(index, m_TableViewD.GetCellSize(tableView, index).y);
                }


                if (tableView == null)
                    return new Mosframe.CCTableViewCell();

                Mosframe.CCTableViewCell cell = tableView.GetReusableCell();
                if (cell == null)
                {
                    GameObject go = GameObject.Instantiate(obj);
                    cell = go.AddComponent<Mosframe.CCTableViewCell>();
                    go.SetActive(true);
                }

                // 如果有数据，更新cell的内容
                if (index < currentScrollDataList.Count - TopNum)
                {
                    // if (clickTogIndex == (int)PeakArenaTabType.two)
                    // {
                    //     UpdateCellContent(cell.gameObject, currentScrollDataList[index]);
                    // }
                    //else
                    //{
                    UpdateCellContent(cell.gameObject, currentScrollDataList[index + TopNum]);
                    //}
                }
                return cell;
            };
            m_TableViewD.ReloadData(true);
        }


        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            ArenaManager.Instance.RequestArenaData(1, (data) =>
            {
                //ArenaOpenInfo
                if (data != null)
                {
                    Debug.Log("[1] c");
                    m_tog1.gameObject.SetActive(true);
                    if (clickTogIndex < 0)
                    {
                        clickTogIndex = 0;
                        ClickTab();
                        ScrollToMyRank();
                    }
                }
                else
                {
                    Debug.Log("[1] 获取竞技场数据失败");
                    m_tog1.gameObject.SetActive(false);
                }
            });
            ArenaManager.Instance.RequestArenaData(2, (data) =>
            {
                //ArenaOpenInfo
                if (data != null)
                {
                    Debug.Log("[2] 获取竞技场数据成功");
                    m_tog2.gameObject.SetActive(true);
                    //clickTogIndex = 1;
                    if (clickTogIndex < 0)
                    {
                        clickTogIndex = 1;
                        ClickTab();
                        ScrollToMyRank();
                    }
                }
                else
                {
                    Debug.Log("[2] 获取竞技场数据失败");
                    m_tog2.gameObject.SetActive(false);
                }
            });


            // 确保toggleList被正确初始化
            toggleList = new List<UIToggle> { m_tog1, m_tog2, m_tog3, m_tog4 };
            foreach (var item in toggleList)
            {
                item.onValueChanged.RemoveAllListeners();
                item.onValueChanged.AddListener((isOn) =>
                {
                    if (isOn)
                    {
                        m_TableViewD.gameObject.SetActive(true);
                        oldClickTogIndex = clickTogIndex;
                        clickTogIndex = item.toggleType;
                        ClickTab();
                        if (oldClickTogIndex != clickTogIndex)
                        {
                            ScrollToMyRank();
                        }
                    }
                });
            }

            //如果是巅峰竞技场结束
            m_txtRuleDes.text = ToolScriptExtend.GetLang(1100428);
            RefreshEndRewardList();
            //如果是巅峰竞技场结束

            // 订阅新兵训练营对手刷新事件
            GameEntry.Event.Subscribe(NoviceCompRefreshEventArgs.EventId, OnNoviceCompRefresh);
            // 订阅巅峰竞技场刷新事件
            GameEntry.Event.Subscribe(PeakRankRefreshEventArgs.EventId, OnPeakRankRefresh);
            
            // 订阅战斗完成事件
            GameEntry.Event.Subscribe(On5V5BattleBackEventArgs.EventId, OnBattleBackFromPlunder);
            
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            // 移除倒计时定时器
            Timers.Instance.Remove("PeakArenaCountdown");
            clickTogIndex = -1;
            // 取消订阅事件
            GameEntry.Event.Unsubscribe(NoviceCompRefreshEventArgs.EventId, OnNoviceCompRefresh);
            GameEntry.Event.Unsubscribe(PeakRankRefreshEventArgs.EventId, OnPeakRankRefresh);
            GameEntry.Event.Unsubscribe(On5V5BattleBackEventArgs.EventId, OnBattleBackFromPlunder);
            base.OnClose(isShutdown, userData);
        }
        private void OnBattleBackFromPlunder(object sender, GameEventArgs e)
        {
            // 当收到对手刷新事件时，刷新当前页签数据
            if (clickTogIndex == (int)PeakArenaTabType.NewSoldier)
            {
                // 只有在新兵训练页签时才刷新数据
                ClickTab();
            }
        }
        private void OnPeakRankRefresh(object sender, GameEventArgs e)
        {
            // 当收到巅峰竞技场刷新事件时，刷新当前页签数据
            if (clickTogIndex == (int)PeakArenaTabType.two)
            {
                // 只有在巅峰竞技场页签时才刷新数据
                Invoke("OnPeakRankRefresh2", 0.3f);
            }
        }
        public void OnPeakRankRefresh2()
        {
            ClickTab();
            ScrollToMyRank();
        }
        private void OnNoviceCompRefresh(object sender, GameEventArgs e)
        {
            // 当收到对手刷新事件时，刷新当前页签数据
            if (clickTogIndex == (int)PeakArenaTabType.NewSoldier)
            {
                // 只有在新兵训练页签时才刷新数据
                ClickTab();
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnRecordsClick()
        {
            Debug.Log("查看战斗记录");
            //if (clickTogIndex == (int)PeakArenaTabType.NewSoldier)
            //{
            GameEntry.UI.OpenUIForm(EnumUIForm.UINovicReportForm, (PeakArenaTabType)clickTogIndex);
            //}
            // 打开战斗记录界面
        }
        private void OnBtnTipClick()
        {
            if(clickTogIndex == (int)PeakArenaTabType.NewSoldier)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm,23);  
            }else if(clickTogIndex == (int)PeakArenaTabType.two)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm,24);  
            }
        }
        private void OnBtnRewardsClick()
        {
            Debug.Log("查看奖励");
            if (clickTogIndex == (int)PeakArenaTabType.NewSoldier)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UINovicRewardForm);
            }
            if (clickTogIndex == (int)PeakArenaTabType.two)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIPeakRewardForm);
            }
            // 打开奖励界面
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
        private void OnBtnShopClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIGeneralShopForm, storetype.storetype_honor);
        }

        private void OnBtnChallengeClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIArenaChallengeForm);
        }
        private void OnBtnAddSoldierTimeClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIChallengeBuyForm);
        }
        private void OnBtnDefenseClick()
        {
            UITeamFormParam param = new()
            {
                Index = 0,
                TeamFormType = UITeamFormType.NoviceTrainingDefend
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
            //
            // if (clickTogIndex == (int)PeakArenaTabType.two)
            // {
            //     m_goTwoPanelEnd.SetActive(!m_goTwoPanelEnd.activeSelf);
            //     m_TableViewD.gameObject.SetActive(!m_goTwoPanelEnd.activeSelf);
            // }
        }

        public void LoadScrollDataByTabIndex()
        {
            currentScrollDataList.Clear();

            switch ((PeakArenaTabType)clickTogIndex)
            {
                case PeakArenaTabType.NewSoldier:
                    // 获取新兵训练营数据
                    if (GameEntry.LogicData.NoviceTrainingData != null)
                    {
                        currentScrollDataList = GameEntry.LogicData.NoviceTrainingData.GetNewSoldierData();
                    }
                    break;
                case PeakArenaTabType.two:
                    // 第二个页签的数据加载逻辑
                    if (GameEntry.LogicData.PeakRankData != null)
                    {
                        currentScrollDataList = GameEntry.LogicData.PeakRankData.GetPeakRankScrollData();
                    }
                    break;
                case PeakArenaTabType.three:
                    // 第三个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.four:
                    // 第四个页签的数据加载逻辑
                    break;
            }
        }

        private void UpdateCellContent(GameObject cellObj, ArenaScrollData Data)
        {
            var data = Data;
            if (cellObj == null || data == null)
            {
                Debug.LogError("cellObj or data is null");
                return;
            }
            GameObject Title = cellObj.transform.Find("Title").gameObject;
            GameObject Plyaer = cellObj.transform.Find("Player").gameObject;
            GameObject PlayerEnd = cellObj.transform.Find("PlayerEnd").gameObject;
            Title.SetActive(false);
            Plyaer.SetActive(false);
            PlayerEnd.SetActive(false);
            if (data.isTitle)
            {
                Title.SetActive(true);
                var config = GameEntry.LogicData.PeakRankData.GetRankGroupConfig(Math.Abs(data.Rank));
                if (config != null)
                {
                    // 设置分组标题文本
                    var rankTxt = Title.transform.Find("rankTxt")?.GetComponent<Text>();
                    if (rankTxt != null)
                    {
                        // 显示排名范围
                        rankTxt.text = $"{config.rank_min}-{config.rank_max}";
                    }
                }
                return;
            }
            else
            {

            }
            Plyaer.SetActive(true);
            
            // 获取并设置名称
            var txtName = cellObj.transform.Find("Player/playerName")?.GetComponent<Text>();

            string colorStr = data.IsCurrentPlayer ? "#34313c" : "#AE8056";
            var bg = Plyaer.transform.Find("bg").GetComponent<Image>();
            if (data.IsCurrentPlayer)
            {               
                // 当前玩家
                bg.SetImage("Sprite/ui_jingjichang/win2_small_dikuang2_2ziji.png");
            }
            else
            {
                bg.SetImage("Sprite/ui_jingjichang/win2_small_dikuang2_2yellow.png");
            }
            if (txtName != null) txtName.text = $"<color={colorStr}>{data.Name}</color>";
            // 获取并设置排名
            var txtRank = cellObj.transform.Find("Player/rank")?.GetComponent<Text>();
            if (txtRank != null) txtRank.text = data.Rank.ToString();


            // 获取并设置等级
            var txtLevel = cellObj.transform.Find("Player/level")?.GetComponent<Text>();
            if (txtLevel != null) txtLevel.text = "Lv." + data.Level;

            // 获取并设置战力
            var txtPower = cellObj.transform.Find("Player/fight")?.GetComponent<Text>();
            if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(data.Power);

            // 设置头像
            var imgHead = cellObj.transform.Find("Player/playerIcon")?.GetComponent<Image>();
            if (imgHead != null && !string.IsNullOrEmpty(data.HeadPath))
            {
                imgHead.SetImage(data.HeadPath);
            }

            // 设置挑战按钮
            var btnChallenge = cellObj.transform.Find("Player/btn")?.GetComponent<Button>();
            if (btnChallenge != null)
            {
                btnChallenge.onClick.RemoveAllListeners();
                btnChallenge.onClick.AddListener(() => OnChallengePlayer(data));
            }
            switch ((PeakArenaTabType)clickTogIndex)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营数据
                    btnChallenge.gameObject.SetActive(false);
                    break;
                case PeakArenaTabType.two:
                    // 第二个页签的数据加载逻辑
                    btnChallenge.onClick.RemoveAllListeners();
                    if (GameEntry.LogicData.PeakRankData.CanChallenge(data.Rank))
                    {
                        btnChallenge.onClick.AddListener(() => OnChallengePlayer(data));
                        btnChallenge.gameObject.SetActive(true);
                    }
                    else
                    {
                        btnChallenge.gameObject.SetActive(false);
                    }
                    break;
                case PeakArenaTabType.three:
                    // 第三个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.four:
                    // 第四个页签的数据加载逻辑
                    break;
            }
        }

        private void OnChallengePlayer(ArenaScrollData data)
        {
            Debug.Log($"挑战玩家: {data.Name}, 排名: {data.Rank}");
            switch ((PeakArenaTabType)clickTogIndex)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营数据
                    break;
                case PeakArenaTabType.two:
                    // 第二个页签的数据加载逻辑
                    GameEntry.LogicData.PeakRankData.RequestRankIntervalInfo(data.Rank, data.Rank,(resp,PlayerInfo) =>
                    {
                        if (resp != null && resp.Teams != null && resp.Teams.Count > 0)
                        {
                            // 将 QueryTeamHero 转换为 TeamHero
                            List<Battle.TeamHero> defHeros = new();
                            foreach (var queryHero in resp.Teams[0].Heroes)
                            {
                                Battle.TeamHero teamHero = new()
                                {
                                    Code = queryHero.HeroId,
                                    Pos = queryHero.Pos + BattleDefine.AttackTeamMaxPos,
                                    Level = queryHero.Level,
                                    StarStage = queryHero.StarStage,
                                    Power = queryHero.Power
                                };
                                defHeros.Add(teamHero);
                            }
                            // 进入战斗场景
                            GameEntry.LogicData.Battle5v5Data.GoBattlePeakRank(defHeros, data.RoleId, (uint)data.Rank);
                            // 关闭当前界面
                            Close();
                            //OnRefreshPanel();
                        }
                    });
                    break;
                case PeakArenaTabType.three:
                    // 第三个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.four:
                    // 第四个页签的数据加载逻辑
                    break;
            }
        }

        private void RefreshTopPlayers()
        {
            // 确保有足够的数据
            if (currentScrollDataList == null || currentScrollDataList.Count < TopNum)
            {
                Debug.LogWarning("Not enough data to display top players");
                return;
            }

            // 刷新前三名玩家信息
            for (int i = 0; i < TopNum; i++)
            {
                GameObject playerObj = null;
                switch (i)
                {
                    case 0:
                        playerObj = m_goPlayer1;
                        break;
                    case 1:
                        playerObj = m_goPlayer2;
                        break;
                    case 2:
                        playerObj = m_goPlayer3;
                        break;
                }

                if (playerObj == null)
                    continue;

                ArenaScrollData data = currentScrollDataList[i];
                // 设置玩家名称
                var soldierName = playerObj.transform.Find("soldierName")?.GetComponent<Text>();
                // 设置战力
                var txtPower = playerObj.transform.Find("powerNode/powertxt")?.GetComponent<Text>();
                var powerNode = playerObj.transform.Find("powerNode").gameObject;
                //页签2玩家名字
                var playerName = playerObj.transform.Find("playerName")?.GetComponent<Text>();
                var union = playerObj.transform.Find("union")?.GetComponent<Text>();
                var caidai = playerObj.transform.Find("caidai")?.gameObject;
                var btn = playerObj.transform.Find("btn")?.GetComponent<Button>();
                powerNode.SetActive(false);
                caidai.SetActive(false);
                btn.gameObject.SetActive(false);
                switch ((PeakArenaTabType)clickTogIndex)
                {
                    case PeakArenaTabType.NewSoldier:
                        // 新兵训练营Top3
                        if (soldierName != null) soldierName.text = data.Name;
                        if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(data.Power);
                        if (playerName != null) playerName.text = "";
                        if (union != null) union.text = "";
                        powerNode.SetActive(true);
                        break;
                    case PeakArenaTabType.two:
                        // 第二个页签的数据加载逻辑
                        if (soldierName != null) soldierName.text = "";
                        if (txtPower != null) txtPower.text = "";
                        if (playerName != null) playerName.text = data.Name;
                        if (union != null) union.text = "#" + data.server_id + "  " + "[Union]" + data.UnionName;
                        caidai.SetActive(true);
                        btn.onClick.RemoveAllListeners();
                        //int personalRank = (int)GameEntry.LogicData.PeakRankData.GetPersonalRank();
                        if (GameEntry.LogicData.PeakRankData.CanChallenge(data.Rank))
                        {
                            btn.onClick.RemoveAllListeners();
                            btn.onClick.AddListener(() => OnChallengePlayer(data));
                            btn.gameObject.SetActive(true);
                        }
                        else
                        {
                            btn.gameObject.SetActive(false);
                        }

                        break;
                    case PeakArenaTabType.three:
                        // 第三个页签的数据加载逻辑
                        break;
                    case PeakArenaTabType.four:
                        // 第四个页签的数据加载逻辑
                        break;
                }
                // 设置头像
                var imgHead = playerObj.transform.Find("playerIcon")?.GetComponent<Image>();
                if (imgHead != null && !string.IsNullOrEmpty(data.HeadPath))
                {
                    imgHead.SetImage(data.HeadPath);
                }

                // // 设置排名图标
                // var imgRank = playerObj.transform.Find("Image_1")?.GetComponent<Image>();
                // if (imgRank != null)
                // {
                //     // 根据排名设置不同的图标
                //     string rankIconPath = $"UI/PeakArena/rank_{i+1}";
                //     imgRank.SetImage(rankIconPath);
                // }
            }
        }
        public void OnRefreshRed()
        {
            m_goNovicRed.SetActive(GameEntry.LogicData.NoviceTrainingData.GetDrawAchieveListCount() > 0 && clickTogIndex == (int)PeakArenaTabType.NewSoldier);
            m_txtNovicRed.text = GameEntry.LogicData.NoviceTrainingData.GetDrawAchieveListCount().ToString();
            Transform red = toggleList[0].transform.Find("red");
            red?.transform.gameObject.SetActive(GameEntry.LogicData.NoviceTrainingData.GetDrawAchieveListCount() > 0);   
        }
        private void OnRefreshPanel()
        {
            switch ((PeakArenaTabType)clickTogIndex)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营数据.
                    m_goTwoPanelEnd.SetActive(false);
                    m_btnShop.gameObject.SetActive(true);
                    m_goShopBg.SetActive(true);
                    m_imgBg.SetImage("Sprite/ui_jingjichang/jingjichang_win_big1_2.png");
                    m_txtSoliderTitle.text = ToolScriptExtend.GetLang(1100359);
                    m_txtTwoTitle.text = "";
                    m_btnAddSoldierTime.gameObject.SetActive(true);
                    m_btnChallenge.gameObject.SetActive(true);
                    RectTransform rect = m_txtChallenges.GetComponent<RectTransform>();
                    rect.anchoredPosition = new Vector2(-156.8f, 133.7f);

                    RectTransform rectPlayer1 = m_goPlayer1.GetComponent<RectTransform>();
                    rectPlayer1.anchoredPosition = new Vector2(0, -568.5f);
                    RectTransform rectPlayer2 = m_goPlayer2.GetComponent<RectTransform>();
                    rectPlayer2.anchoredPosition = new Vector2(-293, -609f);
                    RectTransform rectPlayer3 = m_goPlayer3.GetComponent<RectTransform>();
                    rectPlayer3.anchoredPosition = new Vector2(299, -608f);
                    m_goPlayer1.transform.localScale = new Vector3(1f, 1f, 1);
                    m_goPlayer2.transform.localScale = new Vector3(1f, 1f, 1);
                    m_goPlayer3.transform.localScale = new Vector3(1f, 1f, 1);
                    GameEntry.LogicData.NoviceTrainingData.RequestPersonInfo((personInfo) =>
                    {
                        if (personInfo != null)
                        {
                            // 获取最大挑战次数和已使用次数
                            //int maxChallengeTimes = GameEntry.LogicData.NoviceTrainingData.GetMaxChallenges(); // 默认最大挑战次数，可以从配置表获取
                            //int usedTimes = (int)personInfo.UsedFightTimes;
                            //int remainingTimes = maxChallengeTimes - usedTimes;

                            // 更新UI显示
                            if (m_txtChallenges != null)
                            {
                                string txtDes = ToolScriptExtend.GetLang(1100356);
                                m_txtChallenges.text = txtDes + GameEntry.LogicData.NoviceTrainingData.GetMaxChallenges();
                            }

                            // 更新当前排名
                            int currentRank = (int)GameEntry.LogicData.NoviceTrainingData.GetPersonalRank();
                            Debug.Log($"当前排名: {currentRank}");

                            // 刷新自己的数据
                            RefreshMyself(currentRank);
                            OnRefreshRed();
                        }
                    });
                    m_goMyself.SetActive(true);
                    break;

                case PeakArenaTabType.two:
                    // 巅峰竞技场数据
                    m_btnShop.gameObject.SetActive(false);
                    m_goShopBg.SetActive(false);
                    m_btnAddSoldierTime.gameObject.SetActive(false);
                    m_btnChallenge.gameObject.SetActive(false);
                    m_imgBg.SetImage("Sprite/ui_jingjichang/jingjichang_win_big1_3.png");
                    m_txtSoliderTitle.text = "";
                    m_txtTwoTitle.text = "#1";
                    RectTransform rect2 = m_txtChallenges.GetComponent<RectTransform>();
                    rect2.anchoredPosition = new Vector2(-113.9f, 37f);

                    RectTransform rectPlayer11 = m_goPlayer1.GetComponent<RectTransform>();
                    rectPlayer11.anchoredPosition = new Vector2(0, -514f);
                    RectTransform rectPlayer22 = m_goPlayer2.GetComponent<RectTransform>();
                    rectPlayer22.anchoredPosition = new Vector2(-293, -554.5f);
                    RectTransform rectPlayer33 = m_goPlayer3.GetComponent<RectTransform>();
                    rectPlayer33.anchoredPosition = new Vector2(299, -553.5f);
                    m_goPlayer1.transform.localScale = new Vector3(0.9f, 0.9f, 1);
                    m_goPlayer2.transform.localScale = new Vector3(0.9f, 0.9f, 1);
                    m_goPlayer3.transform.localScale = new Vector3(0.9f, 0.9f, 1);
                    //GameEntry.LogicData.PeakRankData.RequestPersonInfo((personInfo) =>
                    //{
                    //if (personInfo != null)
                    {
                        // 获取最大挑战次数和已使用次数
                        int maxChallengeTimes = GameEntry.LogicData.PeakRankData.GetMaxChallenges();
                        int remainingTimes = GameEntry.LogicData.PeakRankData.GetRemainingChallenges();

                        // 更新UI显示
                        if (m_txtChallenges != null)
                        {
                            string txtDes = ToolScriptExtend.GetLang(1100356);
                            m_txtChallenges.text = txtDes + remainingTimes;
                        }
                    }
                    //});
                    m_goMyself.SetActive(false);
                    break;

                case PeakArenaTabType.three:
                    // 第三个页签的数据刷新逻辑
                    // ...
                    break;

                case PeakArenaTabType.four:
                    // 第四个页签的数据刷新逻辑
                    // ...
                    break;
            }
            OnRefreshRed();
        }

        // 抽取刷新自己数据的方法
        private void RefreshMyself(int currentRank)
        {
            // 设置排名
            var txtRank = m_goMyself.transform.Find("rank")?.GetComponent<Text>();
            if (txtRank != null)
            {
                string rankText = currentRank > 0 ? currentRank.ToString() : ToolScriptExtend.GetLang(1100355);
                txtRank.text = rankText;
            }

            // 设置玩家名称
            var txtName = m_goMyself.transform.Find("playerName")?.GetComponent<Text>();
            if (txtName != null) txtName.text = GameEntry.RoleData.Name;

            // 设置等级
            var txtLevel = m_goMyself.transform.Find("level")?.GetComponent<Text>();
            if (txtLevel != null) txtLevel.text = "Lv.1"; // GameEntry.RoleData.;

            // 设置战力
            var txtPower = m_goMyself.transform.Find("fight")?.GetComponent<Text>();
            if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(GameEntry.RoleData.Power);

            // 设置头像
            var imgHead = m_goMyself.transform.Find("playerIcon")?.GetComponent<Image>();
            if (imgHead != null)
            {
                string headPath = GameEntry.RoleData.HeadSystemAvatar.ToString();
                imgHead.SetImage(headPath);
            }
        }

        private void InitCountdownTimer()
        {
            // 获取竞技场活动结束时间
            int arenaType = clickTogIndex + 1; // 转换为ArenaManager中的类型
            ArenaManager.Instance.RequestArenaData(arenaType, (arenaInfo) =>
            {
                if (arenaInfo != null)
                {
                    // 获取活动结束时间
                    long endTime = arenaInfo.CloseTime;
                    long currentTime = (long)TimeComponent.Now;
                    long remainingTime = endTime - currentTime;

                    if (remainingTime <= 0)
                    {
                        if (m_txtTime != null)
                            m_txtTime.text = "00:00:00";//ToolScriptExtend.GetLang(1100370); // "活动已结束"
                        return;
                    }

                    // 创建定时器，每秒更新倒计时
                    Timers.Instance.Add("PeakArenaCountdown", 1f, (param) =>
                    {
                        if (m_txtTime != null)
                        {
                            long currentRemaining = endTime - (long)TimeComponent.Now;
                            if (currentRemaining <= 0)
                            {
                                m_txtTime.text = "00:00:00";//ToolScriptExtend.GetLang(1100370); // "活动已结束"
                                Timers.Instance.Remove("PeakArenaCountdown");
                            }
                            else
                            {
                                m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)currentRemaining);
                            }
                        }
                    }, -1); // -1表示无限循环
                }
                else
                {
                    if (m_txtTime != null)
                        m_txtTime.text = "00:00:00";//ToolScriptExtend.GetLang(1100370); // "活动已结束"
                }
            });
        }

        /// <summary>
        /// 滚动到玩家自己的排名位置
        /// </summary>
        private void ScrollToMyRank()
        {
            switch ((PeakArenaTabType)clickTogIndex)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营
                    GameEntry.LogicData.NoviceTrainingData.RequestPersonInfo((personInfo) =>
                    {
                        if (personInfo != null)
                        {
                            int myRank = (int)personInfo.RankTop;
                            if (myRank > 0 && myRank > TopNum)
                            {
                                // 计算在TableView中的索引位置
                                int index = myRank - TopNum - 1; // -1是因为索引从0开始
                                // 滚动到该位置
                                ScrollToIndex(index);
                            }
                        }
                    });
                    break;

                case PeakArenaTabType.two:
                    // 巅峰竞技场
                    if (GameEntry.LogicData.PeakRankData != null && GameEntry.LogicData.PeakRankData.RankInfo != null)
                    {
                        int myRank = (int)GameEntry.LogicData.PeakRankData.GetPersonalRank();
                        if (myRank > 0 && myRank > TopNum)
                        {
                            // 查找玩家在列表中的位置
                            int index = -1;
                            int titleCount = 0;
                            for (int i = TopNum; i < currentScrollDataList.Count; i++)
                            {
                                if (currentScrollDataList[i].isTitle)
                                {
                                    titleCount++;
                                }
                                if (currentScrollDataList[i].Rank == myRank)
                                {
                                    index = i - TopNum - titleCount;
                                    break;
                                }
                            }

                            if (index >= 0)
                            {
                                // 滚动到该位置
                                ScrollToIndex(index);
                            }
                        }
                    }
                    break;

                case PeakArenaTabType.three:
                case PeakArenaTabType.four:
                    // 其他页签的处理逻辑
                    break;
            }
        }

        /// <summary>
        /// 滚动TableView到指定索引位置
        /// </summary>
        /// <param name="index">目标索引</param>
        private void ScrollToIndex(int index)
        {
            if (m_TableViewD != null && index >= 0)
            {
                // 计算总项数
                int totalItems = currentScrollDataList.Count - TopNum;
                if (index >= totalItems)
                    index = totalItems - 1;


                // 如果是其他类型，使用通用方法
                // 计算需要滚动的位置
                float cellHeight = 155f;//m_TableViewD.GetCellSize(m_TableViewD, index).y;
                float scrollPosition = cellHeight * index;

                // 获取ScrollRect组件
                ScrollRect scrollRect = m_TableViewD.GetComponent<ScrollRect>();
                if (scrollRect != null)
                {
                    // 计算归一化位置
                    float contentHeight = scrollRect.content.rect.height;
                    float viewportHeight = ((RectTransform)scrollRect.transform).rect.height;

                    if (contentHeight > viewportHeight)
                    {
                        float normalizedPosition = 1f - (scrollPosition / (contentHeight - viewportHeight));
                        normalizedPosition = Mathf.Clamp01(normalizedPosition);

                        // 使用DOTween平滑滚动
                        DOTween.To(() => scrollRect.verticalNormalizedPosition,
                            x => scrollRect.verticalNormalizedPosition = x,
                            normalizedPosition, 1f);
                    }
                }

            }
        }

        private void RefreshEndRewardList()
        {
            // 获取竞技场排名奖励配置
            List<arena_compete_rank_reward> rewardList = GameEntry.LDLTable.GetTable<arena_compete_rank_reward>();
            if (rewardList == null || rewardList.Count == 0)
            {
                Debug.LogError("[PeakArena] 获取奖励配置失败");
                return;
            }
            // 按照排名范围排序
            rewardList.Sort((a, b) => a.rank_min.CompareTo(b.rank_min));

            for (int i = 0; i < m_goEndRewardList.transform.childCount; i++)
            {
                var data = rewardList[i];
                if (data == null)
                {
                    Debug.LogError("[PeakArena] 奖励配置为空");
                    continue;
                }

                // 更新UI显示
                var go = m_goEndRewardList.transform.GetChild(i).gameObject;
                var txtRank = go.transform.Find("Player/rank")?.GetComponent<Text>();
                if (txtRank != null)
                {
                    if (data.rank_max <= 3)
                    {
                        txtRank.text = data.rank_min+"";
                    }
                    else
                    {
                       txtRank.text = data.rank_min + "-" + data.rank_max; 
                    }
                    
                }
                var playerName = go.transform.Find("Player/playerName")?.GetComponent<Text>();
                if (playerName != null)
                {
                    var itemId = data.reward[0].item_id;
                    var ItemName = ToolScriptExtend.GetItemName(itemId);
                    playerName.text = ItemName;
                }

            }
            // 使用DOTween平滑滚动到顶部
            ScrollRect scrollRect = m_scrollviewRule.GetComponent<ScrollRect>();
            // 立即滚动到顶部
            scrollRect.verticalNormalizedPosition = 1f;
        }
    }
}

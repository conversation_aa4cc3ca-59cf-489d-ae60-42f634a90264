%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-5239673776891933249
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: huochexiang
  m_Shader: {fileID: 4800000, guid: 767e660b49a03bc4484fe0b9ebda1d02, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RIMLIGHTINGDIR_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 2183793ea1f88e145af7ad89f997123b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 744bb9473ff63df4297f9302cf1e82a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: 2183793ea1f88e145af7ad89f997123b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 2183793ea1f88e145af7ad89f997123b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 51449d36b52dacf4985866ad0db50f69, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalRGMap:
        m_Texture: {fileID: 2800000, guid: 744bb9473ff63df4297f9302cf1e82a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: a76917e7631806844af9e74b8fe97851, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecCube0:
        m_Texture: {fileID: 8900000, guid: 549f680d252199a4a953ad57810f66d1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _CubeIntensity: 1
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DistanceAttenuation: 1.48
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.14142135
    - _GlossyReflections: 1
    - _Metallic: 1
    - _Mode: 0
    - _OcclusionStrength: 0.725
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimDir: 1
    - _RimDown: 0.544
    - _RimIntensity: 1
    - _RimLeft: 0.409
    - _RimLighting: 0
    - _RimLightingCam: 0
    - _RimOuterThickness: 0.1
    - _RimPower: 1
    - _RimRight: 0.631
    - _RimUp: 0.477
    - _Smoothness: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _UVSec: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _AuxiliaryLightColor: {r: 0.5, g: 0.475, b: 0.49166667, a: 1}
    - _AuxiliaryLightDirection: {r: -0, g: -0.7107996, b: 0.7033948, a: 0}
    - _BaseColor: {r: 0.7921569, g: 0.7921569, b: 0.7921569, a: 1}
    - _Color: {r: 0.7921569, g: 0.7921569, b: 0.7921569, a: 1}
    - _CustomLightColor: {r: 2.3003964, g: 2.3003964, b: 2.3003964, a: 1}
    - _CustomLightDirection: {r: 0.14876816, g: 0.9247454, b: -0.35030556, a: 0}
    - _EmissionColor: {r: 0.764151, g: 0.764151, b: 0.764151, a: 1}
    - _RimInnerColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimOuterColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimView: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1

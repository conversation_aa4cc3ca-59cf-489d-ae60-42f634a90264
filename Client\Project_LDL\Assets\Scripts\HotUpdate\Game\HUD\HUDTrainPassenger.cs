using System.Collections.Generic;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDTrainPassenger : HUDItemTickAble
    {
        public GameObject container;
        public UIText m_txtTime;
        public List<Transform> bubbleList = new();

        void Start()
        {

        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
            GameEntry.Event.Subscribe(TrainPassengerEventArgs.EventId, OnTrainPassengerEventArgs);

            RefreshBubble();
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            GameEntry.Event.Unsubscribe(TrainPassengerEventArgs.EventId, OnTrainPassengerEventArgs);
        }

        private void OnTrainPassengerEventArgs(object sender, GameEventArgs e)
        {
            RefreshBubble();
        }

        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);

            if (GameEntry.TradeTruckData.myTrain == null) return;
            if (GameEntry.TradeTruckData.myTrain.Train == null) return;

            long remainTime = GameEntry.TradeTruckData.myTrain.Train.PrepareTime - (long)TimeComponent.Now;
            if (remainTime < 0)
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays(0);
            }
            else
            {
                m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
            }
        }

        protected override Vector3 GetOffset()
        {
            return new Vector3(0f, 3f, -7f);
        }

        void RefreshBubble()
        {
            bool isUnlock = GameEntry.TradeTruckData.IsUnlockTrade();
            bool isJoin = GameEntry.LogicData.UnionData.IsJoinUnion();
            container.SetActive(isUnlock && isJoin);

            for (int i = 0; i < bubbleList.Count; i++)
            {
                Transform bubble = bubbleList[i];
                UIButton btnBg = bubble.Find("bg").GetComponent<UIButton>();
                Transform head = bubble.Find("bg/head");
                UIImage imgHead = bubble.Find("bg/head/imgHead").GetComponent<UIImage>();
                Transform empty = bubble.Find("bg/empty");
                Transform hat = bubble.Find("bg/hat");
                Transform border = bubble.Find("bg/border");
                UIText txtName = bubble.Find("bg/border/txtName").GetComponent<UIText>();

                btnBg.onClick.RemoveAllListeners();
                btnBg.onClick.AddListener(() =>
                {
                    TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
                    ColorLog.Pink("火车状态", trainStatus);
                    if (trainStatus == TrainStatus.LineUp || trainStatus == TrainStatus.Prepare)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainStationPlatformForm);
                    }
                });

                if (GameEntry.TradeTruckData.myTrain != null)
                {
                    if (i < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                    {
                        Trade.TradeBoxcar tradeBoxcar = GameEntry.TradeTruckData.myTrain.Boxcar[i];
                        bool hasPassenger = tradeBoxcar.Passengers.Count > 0;
                        head.gameObject.SetActive(hasPassenger);
                        empty.gameObject.SetActive(!hasPassenger);
                        border.gameObject.SetActive(hasPassenger);

                        if (hasPassenger && i == 0)
                        {
                            Trade.TradePassenger tradePassenger = tradeBoxcar.Passengers[0];
                            GameEntry.RoleData.RequestRoleQueryLocalSingle(tradePassenger.RoleId, (roleBrief) =>
                            {
                                ColorLog.Pink("查询列车长信息", roleBrief);
                                if (roleBrief != null)
                                {
                                    txtName.text = roleBrief.Name;
                                }
                            });
                        }
                    }
                }

                hat.gameObject.SetActive(i == 0);

                if (i > 0)
                {
                    bubble.gameObject.SetActive(!GameEntry.TradeTruckData.IsLineUpTrain());

                    head.gameObject.SetActive(false);
                    empty.gameObject.SetActive(true);
                    border.gameObject.SetActive(false);
                }

                if (i == bubbleList.Count - 1)
                {
                    Trade.TradeVipPassenger specialGuest = GameEntry.TradeTruckData.GetTradeVipPassenger();
                    bool hasSpecialGuest = false;
                    if (specialGuest != null)
                    {
                        hasSpecialGuest = specialGuest.RoleId != 0;
                    }

                    bubble.gameObject.SetActive(hasSpecialGuest);
                    head.gameObject.SetActive(hasSpecialGuest);
                    empty.gameObject.SetActive(!hasSpecialGuest);
                    border.gameObject.SetActive(hasSpecialGuest);
                }
            }
        }
    }
}

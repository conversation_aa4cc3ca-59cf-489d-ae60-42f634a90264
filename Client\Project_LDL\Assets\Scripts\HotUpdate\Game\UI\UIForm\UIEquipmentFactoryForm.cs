using System.Collections.Generic;
using UnityEngine;
using Game.Hotfix.Config;
using GameFramework.Event;
using DG.Tweening;

namespace Game.Hotfix
{
    public partial class UIEquipmentFactoryForm : UGuiFormEx
    {
        #region 变量

        readonly Dictionary<int, string> qualityColor = new()
        {
            { 1, "#dedede" },
            { 2, "#3effaf" },
            { 3, "#6becfb" },
            { 4, "#f496fa" },
            { 5, "#ffc676" },
        };

        enum DismantleQualityFilter
        {
            Green = 2,    // 优秀及以下
            Blue = 3,     // 优质及以下
            Purple = 4,   // 稀有及以下
        }

        readonly List<UIToggle> partToggles = new();

        DismantleQualityFilter dismantleQualityFilter = DismantleQualityFilter.Green;

        equipposition curEquipPosition = equipposition.equipposition_weapon;  // 当前选中的装备部位页签
        readonly List<UIItemModule> producibleEquipment = new();              // 可制造的装备列表

        UIItemModule curSelect;         // 当前选中的可制造装备
        UIItemModule makeMaterial;      // 当前选中的可制造装备的所需材料
        UIItemModule curSelectCompose;  // 当前选中的合成材料
        UIItemModule curSelectResolve;  // 当前选中的分解材料
        UIItemModule composeResult;     // 合成结果
        UIItemModule resolveSlot;       // 分解槽
        readonly List<UIItemModule> composeSlot = new();    // 合成槽
        readonly List<UIItemModule> resolveResult = new();  // 分解结果
        readonly List<UIItemModule> composeItem = new();    // 合成材料列表
        readonly List<UIItemModule> resolveItem = new();    // 分解材料列表

        readonly List<int> selectedDismantle = new();  // 选中拆解装备列表

        List<GameObject> effectComposeList = new();
        List<GameObject> effectResolveList = new();
        GameObject effectCompose1;
        GameObject effectCompose2;
        GameObject effectCompose3;
        GameObject effectResolve;

        bool canClickCompose = true;
        bool canClickResolve = true;

        Sequence sequence;

        const string EquipmentComposeEffectDelay = "EquipmentComposeEffectDelay";
        const string EquipmentResolveEffectDelay = "EquipmentResolveEffectDelay";

        #endregion

        #region 生命周期

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitToggle();
            InitDropdown();
            GenerateMakeItem();
            GenerateDismantleItem();
            GenerateComposeItem();
            GenerateResolveItem();

            CanvasGroup canvasGroup = m_goImageEffect.GetComponent<CanvasGroup>();
            canvasGroup.DOFade(0.4f, 1f).SetLoops(-1, LoopType.Yoyo);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            HideDefault();
            ShowDefault();
            SelectDefault();
            CheckMaking();
            SortAndRefreshProducibleEquipment();

            canClickCompose = true;
            canClickResolve = true;

            GameEntry.Event.Subscribe(EquipmentChangeEventArgs.EventId, OnEquipmentChange);
            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(EquipmentChangeEventArgs.EventId, OnEquipmentChange);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);

            Timers.Instance.Remove("EquipmentStartMaking");
            Timers.Instance.Remove("EquipmentResetComposeInfo");
            Timers.Instance.Remove("EquipmentResetResolveInfo");
            Timers.Instance.Remove(EquipmentComposeEffectDelay);
            Timers.Instance.Remove(EquipmentResolveEffectDelay);

            HideMaterialEffect();
            KillBtnCollectAnim();
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            SetParticleSystemSortingOrder(m_goMakeEffect, Depth);
            SetParticleSystemSortingOrder(m_btnCollect.gameObject, Depth);
            SetParticleSystemSortingOrder(m_goFinishEffect, Depth);

            for (int i = 0; i < 4; i++)
            {
                Transform parent = m_transComposeSlot.GetChild(i);
                GameObject effectCompose = parent.Find("battle_zhuangbei_huangsekuang").gameObject;
                SetParticleSystemSortingOrder(effectCompose, Depth);
                effectComposeList.Add(effectCompose);
            }

            effectCompose1 = m_transComposeResult.Find("battle_zhuangbei_huangsekuang_1").gameObject;
            SetParticleSystemSortingOrder(effectCompose1, Depth);
            effectCompose2 = m_transComposeResult.Find("battle_zhuangbei_zisekuang_3").gameObject;
            SetParticleSystemSortingOrder(effectCompose2, Depth);
            effectCompose3 = m_transComposeResult.Find("battle_zhuangbei_lansekuang_2").gameObject;
            SetParticleSystemSortingOrder(effectCompose3, Depth);

            effectResolve = m_transResolveSlot.Find("battle_zhuangbei_huangsekuang").gameObject;
            SetParticleSystemSortingOrder(effectResolve, Depth);

            for (int i = 0; i < 4; i++)
            {
                Transform parent = m_transResolveResult.GetChild(i);
                GameObject effectResolve1 = parent.Find("battle_zhuangbei_huangsekuang_1").gameObject;
                GameObject effectResolve2 = parent.Find("battle_zhuangbei_zisekuang_3").gameObject;
                GameObject effectResolve3 = parent.Find("battle_zhuangbei_lansekuang_2").gameObject;
                SetParticleSystemSortingOrder(effectResolve1, Depth);
                SetParticleSystemSortingOrder(effectResolve2, Depth);
                SetParticleSystemSortingOrder(effectResolve3, Depth);
                effectResolveList.Add(effectResolve1);
                effectResolveList.Add(effectResolve2);
                effectResolveList.Add(effectResolve3);
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        #endregion

        #region 事件回调

        void OnEquipmentChange(object sender, GameEventArgs e)
        {
            RefreshDismantleList();
        }

        void OnItemChange(object sender, GameEventArgs e)
        {
            SwitchEquipmentPart(curEquipPosition, false);
            RefreshMakeInfo();
            RefreshComposeItem();
            RefreshResolveItem();
            CheckHasMaterial();
        }

        #endregion

        #region 按钮点击事件

        private void OnBtnLockClick()
        {

        }

        private void OnBtnMakeClick()
        {
            StartMaking();
        }

        private void OnBtnCollectClick()
        {
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(2401);
            if (buildingModule == null) return;

            uint buildId = (uint)buildingModule.BuildingId;
            QueueModule equipmentQueueModule = GameEntry.LogicData.QueueData.GetEquipmentQueueModule(buildId);
            if (equipmentQueueModule is EquipmentQueue equipmentQueue)
            {
                int equipmentCode = (int)equipmentQueue.EquipmentCode;
                int equipmentNum = (int)equipmentQueue.EquipmentNum;
                buildingModule.EquipmentQueueFinishReq(() =>
                {
                    List<reward> rewards = new()
                    {
                        new reward()
                        {
                            item_id = (itemid)equipmentCode,
                            num = equipmentNum
                        }
                    };
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);

                    GameEntry.EquipmentData.MakingEquipmentID = 0;

                    SwitchEquipmentPart(curEquipPosition);

                    m_btnMake.gameObject.SetActive(true);
                    m_btnSpeedUp.gameObject.SetActive(false);
                    m_btnCollect.gameObject.SetActive(false);
                });
            }
        }

        private void OnBtnSpeedUpClick()
        {
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(2401);
            if (buildingModule == null) return;
            itemsubtype itemSubType = itemsubtype.itemsubtype_generalspeedup;
            itemid equipmentID = GameEntry.EquipmentData.MakingEquipmentID;
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSpeedUpForm, new OpenSpeedUpParam(buildingModule, itemSubType,equipmentID));
        }

        private void OnBtnDropdownClick()
        {
            m_goDropdownOption.SetActive(!m_goDropdownOption.activeSelf);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnDismantleClick()
        {
            if (selectedDismantle.Count == 0) return;

            string str = string.Empty;
            List<uint> uints = new();
            foreach (var item in selectedDismantle)
            {
                uints.Add((uint)item);
                str += item.ToString() + ",";
            }

            ColorLog.Pink("装备拆解", str);
            GameEntry.EquipmentData.RequestEquipmentResolve(uints, (result)=>
            {
                ColorLog.Pink("装备拆解回调", result);

                List<reward> rewards = new();
                foreach (var item in result.Rewards)
                {
                    rewards.Add(new reward()
                    {
                        item_id = (itemid)item.Code,
                        num = item.Amount
                    });
                }
                GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);

                selectedDismantle.Clear();
                RefreshDismantleInfo();
            });
        }

        private void OnBtnComposeClick()
        {
            ComposeMaterial(0);
        }

        private void OnBtnComposeMaxClick()
        {
            ComposeMaterial(1);
        }

        private void OnBtnResolveClick()
        {
            ResolveMaterial(0);
        }

        private void OnBtnResolveMaxClick()
        {
            ResolveMaterial(1);
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnMaskClick()
        {
            m_goDropdownOption.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化页签
        /// </summary>
        void InitToggle()
        {
            partToggles.Add(m_togWeapon);
            partToggles.Add(m_togArmour);
            partToggles.Add(m_togChip);
            partToggles.Add(m_togRadar);

            m_togMake.onValueChanged.AddListener((isOn)=>
            {
                m_goMake.SetActive(isOn);
                HideMaterialEffect();
            });

            m_togDismantle.onValueChanged.AddListener((isOn)=>
            {
                m_goDismantle.SetActive(isOn);
                RefreshDismantleList();
                HideMaterialEffect();
            });

            m_togCompose.onValueChanged.AddListener((isOn)=>
            {
                m_goCompose.SetActive(isOn);
                HideMaterialEffect();
            });

            m_togResolve.onValueChanged.AddListener((isOn)=>
            {
                m_goResolve.SetActive(isOn);
                HideMaterialEffect();
            });

            m_togWeapon.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    SwitchEquipmentPart(equipposition.equipposition_weapon);
                }
            });

            m_togArmour.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    SwitchEquipmentPart(equipposition.equipposition_armor);
                }
            });

            m_togChip.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    SwitchEquipmentPart(equipposition.equipposition_chip);
                }
            });

            m_togRadar.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    SwitchEquipmentPart(equipposition.equipposition_radar);
                }
            });
        }

        /// <summary>
        /// 默认隐藏
        /// </summary>
        void HideDefault()
        {
            m_goMake.SetActive(false);
            m_goDismantle.SetActive(false);
            m_goCompose.SetActive(false);
            m_goResolve.SetActive(false);
            m_goDropdownOption.SetActive(false);
            m_goFinishEffect.SetActive(false);

            for (int i = 0; i < composeSlot.Count; i++)
            {
                UIItemModule item = composeSlot[i];
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < resolveResult.Count; i++)
            {
                UIItemModule item = resolveResult[i];
                item.gameObject.SetActive(false);
            }

            if (composeResult != null)
            {
                composeResult.gameObject.SetActive(false);
            }

            if (resolveSlot != null)
            {
                resolveSlot.gameObject.SetActive(false);
            }

            if (curSelectCompose)
            {
                curSelectCompose.IsSelect(false);
            }

            SelectComposeMaterial(null);
            SelectResolveMaterial(null);

            m_txtComposeSlot.gameObject.SetActive(false);
            m_txtResolveSlot.gameObject.SetActive(false);

            m_txtComposeResult.gameObject.SetActive(false);
            m_txtResolveResult.gameObject.SetActive(false);

            m_btnCompose.SetButtonGray(true);
            m_btnCompose.isEnable = false;

            m_btnResolve.SetButtonGray(true);
            m_btnResolve.isEnable = false;

            m_scrollviewDismantleResult.gameObject.SetActive(false);
            m_txtNoDismantle.gameObject.SetActive(false);
            m_txtNoCompose.gameObject.SetActive(false);
            m_txtNoResolve.gameObject.SetActive(false);

            m_transMaking.gameObject.SetActive(false);
            m_goMakeEffect.SetActive(false);

            m_btnComposeMax.gameObject.SetActive(false);
            m_btnResolveMax.gameObject.SetActive(false);
        }

        /// <summary>
        /// 默认显示
        /// </summary>
        void ShowDefault()
        {
            m_txtDismantleTitle.text = ToolScriptExtend.GetLang(1025);
            m_txtNoDismantle.text = ToolScriptExtend.GetLang(1027);
            m_txtComposeTitle.text = ToolScriptExtend.GetLang(1033);
            m_txtNoCompose.text = ToolScriptExtend.GetLang(1034);
            m_txtResolveTitle.text = ToolScriptExtend.GetLang(1036);
            m_txtNoResolve.text = ToolScriptExtend.GetLang(1034);

            m_txtComposeTitle.gameObject.SetActive(true);
            m_txtResolveTitle.gameObject.SetActive(true);
            m_txtDismantleTitle.gameObject.SetActive(true);
            m_transMakeCost.gameObject.SetActive(true);

            m_scrollviewDismantleResult.gameObject.SetActive(selectedDismantle.Count > 0);
            m_txtDismantleTitle.gameObject.SetActive(selectedDismantle.Count == 0);

            List<EquipmentModule> list = GameEntry.EquipmentData.GetNotWearing();
            m_txtNoDismantle.gameObject.SetActive(list.Count == 0);

            CheckHasMaterial();
        }

        /// <summary>
        /// 默认选中
        /// </summary>
        void SelectDefault()
        {
            // 默认选中制造页签
            m_togMake.isOn = true;
            m_goMake.SetActive(true);

            // 默认选中武器页签
            curEquipPosition = equipposition.equipposition_weapon;
        }

        /// <summary>
        /// 隐藏材料合成/分解特效
        /// </summary>
        void HideMaterialEffect()
        {
            for (int i = 0; i < effectComposeList.Count; i++)
            {
                effectComposeList[i].SetActive(false);
            }
            
            effectCompose1.SetActive(false);
            effectCompose2.SetActive(false);
            effectCompose3.SetActive(false);

            effectResolve.SetActive(false);

            for (int i = 0; i < effectResolveList.Count; i++)
            {
                effectResolveList[i].SetActive(false);
            }
        }

        /// <summary>
        /// 检查是否有材料
        /// </summary>
        void CheckHasMaterial()
        {
            bool hasMaterial = false;
            List<equipment_materials> list = GameEntry.EquipmentData.GetEquipmentMaterialsConfig();
            for (int i = 0; i < list.Count; i++)
            {
                long count = GameEntry.LogicData.BagData.GetAmountById(list[i].item_id);
                if (count > 0)
                {
                    hasMaterial = true;
                    break;
                }
            }
            m_txtNoCompose.gameObject.SetActive(!hasMaterial);
            m_txtNoResolve.gameObject.SetActive(!hasMaterial);
        }

        #endregion

        #region 装备制造

        /// <summary>
        /// 创建可制造的装备
        /// </summary>
        void GenerateMakeItem()
        {
            List<equipment_config> list = GameEntry.EquipmentData.GetProducibleEquipmentConfig(curEquipPosition);
            for (int i = 0; i < list.Count; i++)
            {
                GameObject obj = new();
                obj.AddComponent<RectTransform>();
                obj.transform.SetParent(m_transContentMake, false);
                obj.transform.localScale = new Vector3(0.85f, 0.85f, 1f);
                int temp = i;
                BagManager.CreatItem(obj.transform, list[temp].id, 1, (item) =>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.IsShowCount(false);
                    item.SwitchQualityIcon(true);
                    item.SetClick(()=>
                    {
                        SelectProducibleEquipment(item);
                    });
                    producibleEquipment.Add(item);

                    // 生成结束后，默认选中第一个
                    if (temp == list.Count - 1)
                    {
                        SwitchEquipmentPart(curEquipPosition);
                        SelectProducibleEquipment(producibleEquipment[0]);
                    }
                });
            }
            BagManager.CreatItem(m_transMakeCost, itemid.itemid_1, 1, (item) =>
            {
                item.transform.localScale = new Vector3(0.6f, 0.6f, 1f);
                item.GetComponent<UIButton>().useTween = false;
                item.IsShowCount(false);
                item.SwitchQualityIcon(true);
                item.SetClick(()=>
                {
                    ShowItemTip(item, false, true);
                });
                makeMaterial = item;
                RefreshMakeInfo();
            });
        }

        /// <summary>
        /// 切换装备部位
        /// </summary>
        /// <param name="targetPart">目标部位</param>
        void SwitchEquipmentPart(equipposition targetPart, bool isSelectFirst = true)
        {
            curEquipPosition = targetPart;
            List<equipment_config> list = GameEntry.EquipmentData.GetProducibleEquipmentConfig(curEquipPosition);
            SortProducibleEquipment(list);
            for (int i = 0; i < list.Count; i++)
            {
                if (i < producibleEquipment.Count && producibleEquipment[i])
                {
                    producibleEquipment[i].ItemId = list[i].id;
                    producibleEquipment[i].itemModule.ItemId = list[i].id;
                    producibleEquipment[i].InitConfigData();
                    producibleEquipment[i].DisplayInfo();
                }
            }

            if (isSelectFirst)
            {
                SelectProducibleEquipment(producibleEquipment[0]);
            }
        }

        /// <summary>
        /// 排序并刷新可制造装备列表
        /// </summary>
        void SortAndRefreshProducibleEquipment()
        {
            List<equipment_config> list = GameEntry.EquipmentData.GetProducibleEquipmentConfig(curEquipPosition);
            SortProducibleEquipment(list);
            for (int i = 0; i < list.Count; i++)
            {
                if (i < producibleEquipment.Count && producibleEquipment[i])
                {
                    producibleEquipment[i].ItemId = list[i].id;
                    producibleEquipment[i].itemModule.ItemId = list[i].id;
                    producibleEquipment[i].InitConfigData();
                    producibleEquipment[i].DisplayInfo();
                }
            }
            RefreshProducibleEquipment();
            RefreshMakeInfo();
        }

        /// <summary>
        /// 选中可制造的装备
        /// </summary>
        /// <param name="item">装备</param>
        void SelectProducibleEquipment(UIItemModule item)
        {
            if (curSelect)
            {
                curSelect.IsSelect(false);
            }

            if (item == null) return;
            
            curSelect = item;
            curSelect.IsSelect(true);
            RefreshMakeInfo();
        }

        /// <summary>
        /// 可制造的装备排序
        /// </summary>
        /// <param name="list">可制造的装备配置列表</param>
        void SortProducibleEquipment(List<equipment_config> list)
        {
            list.Sort((a, b) =>
            {
                bool aIsUnlock = GameEntry.EquipmentData.IsEquipmentUnlock(a.id, out string aBuildName, out int aBuildLevel);
                bool bIsUnlock = GameEntry.EquipmentData.IsEquipmentUnlock(b.id, out string bBuildName, out int bBuildLevel);

                int aMaking = a.id == GameEntry.EquipmentData.MakingEquipmentID && aIsUnlock ? 10000 : 0;
                int bMaking = b.id == GameEntry.EquipmentData.MakingEquipmentID && bIsUnlock ? 10000 : 0;

                itemid aCostID = a.cost_materials[0].item_id;
                long aCostNum = a.cost_materials[0].num;
                long aCount = GameEntry.LogicData.BagData.GetAmountById(aCostID);
                int aCanMake = aCount >= aCostNum && aIsUnlock ? 1000 : 0;

                itemid bCostID = b.cost_materials[0].item_id;
                long bCostNum = b.cost_materials[0].num;
                long bCount = GameEntry.LogicData.BagData.GetAmountById(bCostID);
                int bCanMake = bCount >= bCostNum && bIsUnlock ? 1000 : 0;

                int aIsUnlockWeight = aIsUnlock ? 100 : 0;
                int bIsUnlockWeight = bIsUnlock ? 100 : 0;

                int aQuality = (int)a.equipment_quality;
                int bQuality = (int)b.equipment_quality;

                int aWeight = aMaking + aCanMake + aIsUnlockWeight + aQuality;
                int bWeight = bMaking + bCanMake + bIsUnlockWeight + bQuality;

                return bWeight.CompareTo(aWeight);
            });
        }

        /// <summary>
        /// 刷新制造信息
        /// </summary>
        void RefreshMakeInfo()
        {
            if (curSelect == null) return;

            item_config config = curSelect.GetItemConfig();
            equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(config.id);

            bool isUnlock = GameEntry.EquipmentData.IsEquipmentUnlock(config.id, out string buildName, out int buildLevel);
            bool isMaking = curSelect.ItemId == GameEntry.EquipmentData.MakingEquipmentID;
            m_transMaking.gameObject.SetActive(isMaking);
            m_transMakeCost.gameObject.SetActive(!isMaking);
            m_goMakeEffect.SetActive(isMaking);
            m_btnMake.gameObject.SetActive(!isMaking);
            m_btnSpeedUp.gameObject.SetActive(isMaking);
            m_btnCollect.gameObject.SetActive(false);
            m_btnLock.gameObject.SetActive(false);

            float remainTime = GetProduceRemainTime();
            bool isFinished = remainTime <= 0;
            if (isMaking && isFinished)
            {
                m_transMaking.gameObject.SetActive(true);
                m_transMakeCost.gameObject.SetActive(false);
                m_btnMake.gameObject.SetActive(false);
                m_btnSpeedUp.gameObject.SetActive(false);
                m_goMakeEffect.SetActive(false);
                m_btnCollect.gameObject.SetActive(true);
                SetBtnCollectAnim();

                m_txtMakingTime.text = ToolScriptExtend.GetLang(1356);
                m_sliderMaking.value = 1f;
            }

            m_txtMakeName.text = ToolScriptExtend.GetLang(config.name);
            SetTextColor(m_txtMakeName, (int)config.quality);
            m_txtMakeTime.text = TimeHelper.FormatGameTimeWithDays(equipmentConfig.produce_time);
            m_imgMakeIcon.SetImage(config.icon, false);

            itemid costID = equipmentConfig.cost_materials[0].item_id;
            long costNum = equipmentConfig.cost_materials[0].num;
            long count = GameEntry.LogicData.BagData.GetAmountById(costID);
            string countStr = ToolScriptExtend.FormatNumberWithUnit(count);
            string colorHex = count >= costNum ? "ffffff" : "ff0000";
            m_txtMakeMaterial.text = $"<color=#{colorHex}>{countStr}</color>/{costNum}";

            itemid costCoinID = equipmentConfig.cost_coin.item_id;
            long costCoinCount = GameEntry.LogicData.BagData.GetAmountById(costCoinID);
            long costCoinNum = equipmentConfig.cost_coin.num;
            string costCoinColorHex = costCoinCount >= costCoinNum ? "ffffff" : "ff0000";
            string costNumStr = ToolScriptExtend.FormatNumberWithUnit(equipmentConfig.cost_coin.num);
            m_txtMakeCost.text = $"<color=#{costCoinColorHex}>{costNumStr}</color>";

            bool hasMaking = GameEntry.EquipmentData.MakingEquipmentID != 0;
            if (hasMaking)
            {
                RefreshBtnMakeGrey(true);
            }
            else
            {
                RefreshBtnMakeGrey(false);
            }

            if (!isUnlock)
            {
                m_btnSpeedUp.gameObject.SetActive(false);
                m_goMakeEffect.SetActive(false);
                m_btnCollect.gameObject.SetActive(false);
                m_btnMake.gameObject.SetActive(false);
                m_btnLock.gameObject.SetActive(true);
                m_txtLockTip.text = ToolScriptExtend.GetLangFormat(1357, buildName, buildLevel.ToString());
            }

            if (makeMaterial)
            {
                makeMaterial.ItemId = costID;
                makeMaterial.itemModule.ItemId = costID;
                makeMaterial.InitConfigData();
                makeMaterial.DisplayInfo();
            }

            equipment_level levelConfig = GameEntry.EquipmentData.GetEquipmentLevelConfig(config.id, 0);
            m_txtPower.text = levelConfig.power.ToString();

            Dictionary<int, List<equip_attributes>> attrDict = GameEntry.EquipmentData.GetBasicAndExtraProperty(config.id);

            foreach (Transform item in m_transContentProperty)
            {
                item.gameObject.SetActive(false);
            }

            int index = 0;
            foreach (var attrItem in attrDict)
            {
                for (int i = 0; i < attrItem.Value.Count; i++)
                {
                    equip_attributes attr = attrItem.Value[i];
                    bool isBasic = attrItem.Key == 0;

                    GameObject item = null;
                    if (index < m_transContentProperty.childCount)
                    {
                        item = m_transContentProperty.GetChild(index).gameObject;
                        item.SetActive(true);
                    }
                    else
                    {
                        item = Instantiate(m_goPropertyItem, m_transContentProperty);
                        item.SetActive(true);
                    }

                    GameObject bg1 = item.transform.Find("bg1").gameObject;
                    GameObject bg2 = item.transform.Find("bg2").gameObject;

                    GameObject active = item.transform.Find("Active").gameObject;
                    UIText txtNameActive = item.transform.Find("Active/Name").GetComponent<UIText>();
                    UIText txtValueActive = item.transform.Find("Active/Value").GetComponent<UIText>();

                    GameObject lockState = item.transform.Find("Lock").gameObject;
                    UIText txtNameLock = item.transform.Find("Lock/TextGroup/Name").GetComponent<UIText>();
                    UIText txtValueLock = item.transform.Find("Lock/TextGroup/Value").GetComponent<UIText>();
                    UIText txtLevelLock = item.transform.Find("Lock/Bg/Level").GetComponent<UIText>();

                    bg1.SetActive(isBasic);
                    active.SetActive(isBasic);
                    txtNameActive.text = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);
                    txtValueActive.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";

                    bg2.SetActive(!isBasic);
                    lockState.SetActive(!isBasic);
                    txtNameLock.text = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);
                    txtValueLock.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                    txtLevelLock.text = $"Lv.{attrItem.Key}";

                    index++;
                }
            }

            RefreshPartMakingIcon();
            RefreshProducibleEquipment();
        }

        /// <summary>
        /// 刷新部位制造图标
        /// </summary>
        void RefreshPartMakingIcon()
        {
            if (GameEntry.EquipmentData.MakingEquipmentID != 0)
            {
                equipment_config makingConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(GameEntry.EquipmentData.MakingEquipmentID);
                for (int i = 0; i < partToggles.Count; i++)
                {
                    GameObject making = partToggles[i].transform.Find("Making").gameObject;
                    making.SetActive((int)makingConfig.position == i + 1);
                }
            }
        }

        /// <summary>
        /// 刷新可制造的装备
        /// </summary>
        void RefreshProducibleEquipment()
        {
            for (int i = 0; i < producibleEquipment.Count; i++)
            {
                UIItemModule uiItemModule = producibleEquipment[i];
                bool isUnlock = GameEntry.EquipmentData.IsEquipmentUnlock(uiItemModule.ItemId, out string buildName, out int buildLevel);

                if (!isUnlock)
                {
                    uiItemModule.equipmentLock.SetActive(true);
                    uiItemModule.equipmentMaking.SetActive(false);
                    uiItemModule.equipmentMakingIcon.gameObject.SetActive(false);
                    uiItemModule.equipmentMakingFinish.SetActive(false);
                    uiItemModule.equipmentCanMake.SetActive(false);
                    continue;
                }

                uiItemModule.equipmentLock.SetActive(false);
                bool isMaking = uiItemModule.ItemId == GameEntry.EquipmentData.MakingEquipmentID;
                float remainTime = GetProduceRemainTime();
                bool isFinished = remainTime <= 0;

                equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(uiItemModule.ItemId);
                itemid costID = equipmentConfig.cost_materials[0].item_id;
                long costNum = equipmentConfig.cost_materials[0].num;
                long count = GameEntry.LogicData.BagData.GetAmountById(costID);
                bool canMake = count >= costNum;

                uiItemModule.equipmentMakingIcon.gameObject.SetActive(true);
                uiItemModule.equipmentMakingFinish.SetActive(false);
                uiItemModule.equipmentCanMake.SetActive(canMake);

                if (isMaking)
                {
                    uiItemModule.equipmentMaking.SetActive(true);
                    uiItemModule.equipmentCanMake.SetActive(false);
                    uiItemModule.SetEquipmentMakingIconAnim();

                    if (isFinished)
                    {
                        uiItemModule.equipmentMakingIcon.gameObject.SetActive(false);
                        uiItemModule.equipmentMakingFinish.SetActive(true);
                        Transform parent = uiItemModule.equipmentMakingFinish.transform;
                        Transform effect = parent.Find("finishEffect");
                        if (effect == null)
                        {
                            effect = Instantiate(m_goFinishEffect.transform, parent);
                            effect.name = "finishEffect";
                            effect.gameObject.SetActive(true);

                            RectTransform effectRect = effect.GetComponent<RectTransform>();
                            effectRect.anchoredPosition = new Vector2(0f, -10f);
                            effect.transform.localScale = new Vector3(0.77f, 0.77f, 1f);
                        }
                        else
                        {
                            effect.gameObject.SetActive(true);
                        }
                    }
                }
                else
                {
                    uiItemModule.equipmentMaking.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 刷新制造按钮置灰状态
        /// </summary>
        /// <param name="isGrey">是否置灰</param>
        void RefreshBtnMakeGrey(bool isGrey)
        {
            m_btnMake.SetButtonGray(isGrey);
            m_txtMakeTime.transform.GetChild(0).GetComponent<UIImage>().SetImageGray(isGrey);
            m_txtMakeCost.transform.GetChild(0).GetComponent<UIImage>().SetImageGray(isGrey);
        }

        /// <summary>
        /// 开始制造
        /// </summary>
        void StartMaking()
        {
            if (curSelect == null) return;

            item_config config = curSelect.GetItemConfig();
            bool isUnlock = GameEntry.EquipmentData.IsEquipmentUnlock(config.id, out string buildName, out int buildLevel);

            if (!isUnlock)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1362)
                });
                return;
            }

            bool hasMaking = GameEntry.EquipmentData.MakingEquipmentID != 0;
            if (hasMaking)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1363)
                });
                return;
            }

            equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(config.id);
            int needTime = equipmentConfig.produce_time;

            itemid costID = equipmentConfig.cost_materials[0].item_id;
            long costNum = equipmentConfig.cost_materials[0].num;
            long count = GameEntry.LogicData.BagData.GetAmountById(costID);
            
            if (count < costNum)
            {
                itemid needItemId = costID;
                long needNum = costNum;

                ItemModule itemModule = new(needItemId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(needNum));
                return;
            }
            
            itemid costCoinID = equipmentConfig.cost_coin.item_id;
            long costCoinNum = equipmentConfig.cost_coin.num;
            long costCoinCount = GameEntry.LogicData.BagData.GetAmountById(costCoinID);
            
            if (costCoinCount < costCoinNum)
            {
                itemid needItemId = costCoinID;
                long needNum = costCoinNum;

                ItemModule itemModule = new(needItemId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(needNum));
                return;
            }

            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(2401);
            if (buildingModule == null) return;

            ColorLog.Pink("装备制造", config.id, "建筑", buildingModule.BuildingId);
            GameEntry.EquipmentData.RequestEquipmentProduce(config.id, (uint)buildingModule.BuildingId, (result) =>
            {
                ColorLog.Pink("装备制造回调", result);
                GameEntry.EquipmentData.MakingEquipmentID = config.id;
                StartMakingTimer();
            });

            m_txtMakingTime.text = TimeHelper.FormatGameTimeWithDays(equipmentConfig.produce_time);
            m_sliderMaking.value = 0;

            RefreshPartMakingIcon();
            SwitchEquipmentPart(curEquipPosition);
        }

        /// <summary>
        /// 检查是否正在制造中
        /// </summary>
        void CheckMaking()
        {
            float remainTime = GetProduceRemainTime();
            GameEntry.EquipmentData.MakingEquipmentID = GetProduceEquipmentID();
            if (remainTime > 0 && GameEntry.EquipmentData.MakingEquipmentID != 0)
            {
                StartMakingTimer();
                m_btnMake.gameObject.SetActive(true);
                m_btnCollect.gameObject.SetActive(false);
                if (curSelect != null)
                {
                    bool isMaking = curSelect.ItemId == GameEntry.EquipmentData.MakingEquipmentID;
                    if (isMaking)
                    {
                        m_btnMake.gameObject.SetActive(false);
                    }
                }
            }
            else if (remainTime <= 0 && GameEntry.EquipmentData.MakingEquipmentID != 0)
            {
                m_btnMake.gameObject.SetActive(false);
                m_btnSpeedUp.gameObject.SetActive(false);
                m_btnCollect.gameObject.SetActive(true);
                SetBtnCollectAnim();
                m_transMaking.gameObject.SetActive(true);
                m_transMakeCost.gameObject.SetActive(false);
                m_goMakeEffect.SetActive(false);

                m_sliderMaking.value = 1f;
                m_txtMakingTime.text = ToolScriptExtend.GetLang(1356);
            }
        }

        /// <summary>
        /// 开启制造计时器
        /// </summary>
        void StartMakingTimer()
        {
            m_transMaking.gameObject.SetActive(true);
            m_transMakeCost.gameObject.SetActive(false);
            m_goMakeEffect.SetActive(true);

            if (GameEntry.EquipmentData.MakingEquipmentID == 0) return;
            equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(GameEntry.EquipmentData.MakingEquipmentID);
            int needTime = equipmentConfig.produce_time;

            float remainTime = GetProduceRemainTime();
            m_txtMakingTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
            m_sliderMaking.value = 1 - (float)remainTime / needTime;
            
            Timers.Instance.Add("EquipmentStartMaking", 0.1f, (param) =>
            {
                float remainTime = GetProduceRemainTime();
                m_txtMakingTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
                m_sliderMaking.value = 1 - (float)remainTime / needTime;

                if (remainTime <= 0)
                {
                    Timers.Instance.Remove("EquipmentStartMaking");
                    m_btnMake.gameObject.SetActive(false);
                    m_btnSpeedUp.gameObject.SetActive(false);
                    m_btnCollect.gameObject.SetActive(true);
                    SetBtnCollectAnim();
                    m_transMaking.gameObject.SetActive(true);
                    m_transMakeCost.gameObject.SetActive(false);
                    m_goMakeEffect.SetActive(false);

                    m_txtMakingTime.text = ToolScriptExtend.GetLang(1356);
                    m_sliderMaking.value = 1f;

                    RefreshProducibleEquipment();
                }
            }, -1);
        }

        /// <summary>
        /// 获取制造剩余时间
        /// </summary>
        /// <returns>剩余时间</returns>
        float GetProduceRemainTime()
        {
            float remainTime = 0f;
            EquipmentQueue equipmentQueueModule = GameEntry.LogicData.QueueData.GetEquipmentQueueModule(2401);
            if (equipmentQueueModule != null)
            {
                equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID((itemid)equipmentQueueModule.EquipmentCode);
                if (equipmentConfig != null)
                {
                    remainTime = Mathf.Min(equipmentQueueModule.GetRemainTime(), equipmentConfig.produce_time);
                }
            }
            else
            {
                equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(GameEntry.EquipmentData.MakingEquipmentID);
                if (equipmentConfig != null)
                {
                    remainTime = equipmentConfig.produce_time;
                }
            }
            return remainTime;
        }

        /// <summary>
        /// 获取制造装备 ID
        /// </summary>
        /// <returns>装备 ID</returns>
        itemid GetProduceEquipmentID()
        {
            EquipmentQueue equipmentQueueModule = GameEntry.LogicData.QueueData.GetEquipmentQueueModule(2401);
            if (equipmentQueueModule == null) return 0;
            return (itemid)equipmentQueueModule.EquipmentCode;
        }

        /// <summary>
        /// 设置收取按钮动画
        /// </summary>
        void SetBtnCollectAnim()
        {
            KillBtnCollectAnim();
            sequence = DOTween.Sequence();
            float duration = 0.15f;
            sequence.Append(m_btnCollect.transform.DOScale(0.9f, duration));
            sequence.Append(m_btnCollect.transform.DOScale(1f, duration));
            sequence.Append(m_btnCollect.transform.DOScale(0.9f, duration));
            sequence.Append(m_btnCollect.transform.DOScale(1f, duration));
            sequence.AppendInterval(1f);
            sequence.SetLoops(-1, LoopType.Restart);
        }

        // 清理收取按钮动画
        void KillBtnCollectAnim()
        {
            if (sequence != null)
            {
                sequence.Kill();
                sequence = null;
                m_btnCollect.transform.DOKill();
                m_btnCollect.transform.localScale = Vector3.one;
            }
        }
    
        #endregion

        #region 装备拆解

        /// <summary>
        /// 初始化下拉框
        /// </summary>
        void InitDropdown()
        {
            foreach (Transform item in m_transDropdownOption)
            {
                UIToggle toggle = item.GetComponent<UIToggle>();
                toggle.onValueChanged.AddListener((isOn)=>
                {
                    m_goDropdownOption.SetActive(false);
                    m_btnMask.gameObject.SetActive(false);

                    UIText label = item.GetChild(2).GetComponent<UIText>();
                    string colorStr = isOn ? "#000000" : "#7A7780";
                    ColorUtility.TryParseHtmlString(colorStr, out Color color);
                    label.color = color;

                    if (isOn)
                    {
                        dismantleQualityFilter = (DismantleQualityFilter)toggle.toggleType;
                        SelectDismantleEquipmentByQuality();
                        m_TableViewV.ReloadData();
                    }
                });
            }
        }

        /// <summary>
        /// 创建可拆解的装备
        /// </summary>
        void GenerateDismantleItem()
        {
            SelectDismantleEquipmentByQuality();
            m_TableViewV.GetItemCount = () =>
            {
                List<EquipmentModule> list = GameEntry.EquipmentData.GetNotWearing();
                return Mathf.CeilToInt(list.Count / 5f);
            };
            m_TableViewV.GetItemGo = () => m_goDismantleGroup;
            m_TableViewV.UpdateItemCell = UpdateDismantleInfoView;
            m_TableViewV.InitTableViewByIndex(0, 12);
        }

        /// <summary>
        /// 刷新拆解装备列表
        /// </summary>
        /// <param name="index">第几行</param>
        /// <param name="obj">装备组节点</param>
        void UpdateDismantleInfoView(int index, GameObject obj)
        {
            List<EquipmentModule> list = GameEntry.EquipmentData.GetNotWearing();

            int childCount = 0;
            foreach (Transform child in obj.transform)
            {
                if (child.childCount > 0)
                {
                    child.GetChild(0).gameObject.SetActive(false);
                    childCount++;
                }
            }

            for (int i = 0; i < 5; i++)
            {
                if (childCount <= i)
                {
                    GameObject objParent = null;
                    if (obj.transform.childCount <= i)
                    {
                        objParent = new();
                        RectTransform rect = objParent.AddComponent<RectTransform>();
                        rect.sizeDelta = new Vector2(190f, 190f);
                        objParent.transform.SetParent(obj.transform, false);
                        objParent.transform.localScale = new Vector3(0.82f, 0.82f, 1f);
                    }
                    else
                    {
                        objParent = obj.transform.GetChild(i).gameObject;
                    }
                    
                    int temp = i;
                    int indexTemp = index * 5 + temp;
                    if (indexTemp > list.Count - 1) continue;
                    EquipmentModule equipment = list[indexTemp];
                    BagManager.CreatItem(objParent.transform, equipment.code, 1, (item) =>
                    {
                        item.UID = equipment.id;
                        item.itemModule.Count = equipment.id;
                        item.InitConfigData();
                        item.DisplayInfo();
                        item.GetComponent<UIButton>().useTween = false;
                        item.IsShowCount(false);
                        item.SwitchQualityIcon(true);
                        item.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                        item.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                        item.txtEquipmentLevel.transform.localScale = new Vector3(1.2f, 1.2f, 1f);
                        item.txtEquipmentLevel.gameObject.SetActive(equipment.EquipmentQuality != (int)quality.quality_green);
                        item.SetClick(()=>
                        {
                            SelectDismantleEquipment(item);
                        });

                        if (selectedDismantle.Contains(item.UID))
                        {
                            item.SelectEquipment(true);
                        }
                        else
                        {
                            item.SelectEquipment(false);
                        }
                    });
                }
                else
                {
                    int temp = i;
                    int indexTemp = index * 5 + temp;
                    if (indexTemp > list.Count - 1) continue;
                    EquipmentModule equipment = list[indexTemp];
                    UIItemModule item = obj.transform.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    item.UID = equipment.id;
                    item.ItemId = equipment.code;
                    item.itemModule.ItemId = equipment.code;
                    item.itemModule.Count = equipment.id;
                    item.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                    item.txtEquipmentLevel.gameObject.SetActive(equipment.EquipmentQuality != (int)quality.quality_green);
                    item.InitConfigData();
                    item.DisplayInfo();
                    item.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                    item.gameObject.SetActive(true);

                    if (selectedDismantle.Contains(item.UID))
                    {
                        item.SelectEquipment(true);
                    }
                    else
                    {
                        item.SelectEquipment(false);
                    }
                }
            }
        }

        /// <summary>
        /// 选中可拆解的装备
        /// </summary>
        /// <param name="item">装备</param>
        void SelectDismantleEquipment(UIItemModule item)
        {
            if (item == null) return;

            if (selectedDismantle.Contains(item.UID))
            {
                item.SelectEquipment(false);
                selectedDismantle.Remove(item.UID);
            }
            else
            {
                item.SelectEquipment(true);
                selectedDismantle.Add(item.UID);
            }

            RefreshDismantleInfo();
        }

        /// <summary>
        /// 根据品质筛选可拆解的装备
        /// </summary>
        void SelectDismantleEquipmentByQuality()
        {
            List<EquipmentModule> list = GameEntry.EquipmentData.GetNotWearing();
            selectedDismantle.Clear();

            for (int i = 0; i < list.Count; i++)
            {
                EquipmentModule equipment = list[i];
                if (equipment.EquipmentQuality <= (int)dismantleQualityFilter)
                {
                    selectedDismantle.Add(equipment.id);
                }
            }

            RefreshDismantleInfo();
        }

        /// <summary>
        /// 刷新拆解信息
        /// </summary>
        void RefreshDismantleInfo()
        {
            if (selectedDismantle.Count == 0)
            {
                m_scrollviewDismantleResult.gameObject.SetActive(false);
                m_txtDismantleTitle.gameObject.SetActive(true);
                return;
            }

            m_scrollviewDismantleResult.gameObject.SetActive(true);
            m_txtDismantleTitle.gameObject.SetActive(false);

            Dictionary<itemid, long> dismantleResult = GetDismantleResult();

            foreach (Transform item in m_transContentDismantleResult)
            {
                if (item.gameObject.name.Equals("Top"))
                {
                    continue;
                }
                item.gameObject.SetActive(false);
            }

            List<itemid> keys = new(dismantleResult.Keys);
            List<long> values = new(dismantleResult.Values);
            for (int i = 0; i < dismantleResult.Count; i++)
            {
                if (i < m_transContentDismantleResult.childCount - 1)
                {
                    Transform parent = m_transContentDismantleResult.GetChild(i);
                    UIItemModule item = parent.GetChild(0).GetComponent<UIItemModule>();
                    item.ItemId = keys[i];
                    item.itemModule.ItemId = keys[i];
                    item.itemModule.Count = values[i];
                    item.InitConfigData();
                    item.DisplayInfo();
                    parent.gameObject.SetActive(true);
                }
                else
                {
                    GameObject obj = new();
                    obj.AddComponent<RectTransform>();
                    obj.transform.SetParent(m_transContentDismantleResult, false);
                    obj.transform.localScale = new Vector3(0.8f, 0.8f, 1f);
                    int index = i;
                    BagManager.CreatItem(obj.transform, keys[i], values[i], (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SwitchQualityIcon(true);
                        item.SetClick(()=>
                        {

                        });
                    });
                    Transform top = m_transContentDismantleResult.Find("Top");
                    top.SetAsLastSibling();
                }
            }
        }

        /// <summary>
        /// 刷新拆解装备列表
        /// </summary>
        void RefreshDismantleList()
        {
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0, 12);
            }
            else
            {
                m_TableViewV.ReloadData();
            }

            List<EquipmentModule> list = GameEntry.EquipmentData.GetNotWearing();
            m_txtNoDismantle.gameObject.SetActive(list.Count == 0);
        }

        /// <summary>
        /// 获取拆解结果
        /// </summary>
        Dictionary<itemid, long> GetDismantleResult()
        {
            Dictionary<itemid, long> temp = new();

            for (int i = 0; i < selectedDismantle.Count; i++)
            {
                EquipmentModule equipment = GameEntry.EquipmentData.GetEquipmentByID(selectedDismantle[i]);
                equipment_level levelConfig = GameEntry.EquipmentData.GetEquipmentLevelConfig(equipment.code, equipment.equipment_level);
                equipment_promotion promotionConfig = GameEntry.EquipmentData.GetPromoteLevelConfig(equipment.code, equipment.promotion_level);
                
                if (levelConfig == null) continue;

                for (int j = 0; j < levelConfig.break_reward.Count; j++)
                {
                    if (temp.ContainsKey(levelConfig.break_reward[j].item_id))
                    {
                        temp[levelConfig.break_reward[j].item_id] += levelConfig.break_reward[j].num;
                    }
                    else
                    {
                        temp.Add(levelConfig.break_reward[j].item_id, levelConfig.break_reward[j].num);
                    }
                }

                if (promotionConfig == null) continue;

                for (int j = 0; j < promotionConfig.break_reward.Count; j++)
                {
                    if (temp.ContainsKey(promotionConfig.break_reward[j].item_id))
                    {
                        temp[promotionConfig.break_reward[j].item_id] += promotionConfig.break_reward[j].num;
                    }
                    else
                    {
                        temp.Add(promotionConfig.break_reward[j].item_id, promotionConfig.break_reward[j].num);
                    }
                }
            }

            return temp;
        }

        #endregion

        #region 材料合成

        /// <summary>
        /// 创建可合成的材料
        /// </summary>
        void GenerateComposeItem()
        {
            List<equipment_materials> list = GameEntry.EquipmentData.GetEquipmentMaterialsConfig();
            for (int i = 0; i < list.Count; i++)
            {
                GameObject obj = new();
                obj.AddComponent<RectTransform>();
                obj.transform.SetParent(m_transContentCompose, false);
                obj.transform.localScale = new Vector3(0.85f, 0.85f, 1f);
                int temp = i;
                long count = GameEntry.LogicData.BagData.GetAmountById(list[temp].item_id);
                BagManager.CreatItem(obj.transform, list[temp].item_id, count, (item) =>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.SwitchQualityIcon(true);
                    item.SetClick(()=>
                    {
                        Timers.Instance.Remove("EquipmentResetComposeInfo");
                        Timers.Instance.Remove(EquipmentComposeEffectDelay);
                        SelectComposeMaterial(item);
                    });
                    item.transform.parent.gameObject.SetActive(item.Count > 0);
                    composeItem.Add(item);
                });
            }

            for (int i = 0; i < 4; i++)
            {
                GameObject obj = new();
                obj.AddComponent<RectTransform>();
                Transform parent = m_transComposeSlot.GetChild(i);
                obj.transform.SetParent(parent, false);
                obj.transform.localScale = new Vector3(0.86f, 0.86f, 1f);
                int temp = i;
                BagManager.CreatItem(obj.transform, list[temp].item_id, 1, (item) =>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.IsShowCount(false);
                    item.SwitchQualityIcon(true);
                    item.SetClick(()=>
                    {
                        ShowItemTip(item);
                    });
                    item.gameObject.SetActive(false);
                    composeSlot.Add(item);
                });
            }

            GameObject objSlot = new();
            objSlot.AddComponent<RectTransform>();
            objSlot.transform.SetParent(m_transComposeResult, false);
            objSlot.transform.localScale = new Vector3(0.9f, 0.9f, 1f);
            BagManager.CreatItem(objSlot.transform, list[0].item_id, 1, (item) =>
            {
                item.GetComponent<UIButton>().useTween = false;
                item.IsShowCount(false);
                item.SwitchQualityIcon(true);
                item.SetClick(()=>
                {
                    ShowItemTip(item);
                });
                item.gameObject.SetActive(false);
                composeResult = item;
            });
        }

        /// <summary>
        /// 刷新合成材料列表
        /// </summary>
        void RefreshComposeItem()
        {
            for (int i = 0; i < composeItem.Count; i++)
            {
                composeItem[i].itemModule.Count = GameEntry.LogicData.BagData.GetAmountById(composeItem[i].ItemId);
                composeItem[i].DisplayInfo();
                composeItem[i].transform.parent.gameObject.SetActive(composeItem[i].itemModule.Count > 0);
            }

            if (curSelectCompose == null) return;

            item_config config = curSelectCompose.GetItemConfig();

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetComposeTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                long count = GameEntry.LogicData.BagData.GetAmountById(config.id);
                if (count < targetMaterial.synth_num.num)
                {
                    SelectComposeMaterial(null);
                }
                
                long remainCount = count / targetMaterial.synth_num.num;
                if (remainCount >= 5)
                {
                    m_btnComposeMax.gameObject.SetActive(true);
                    m_txtComposeMax.text = remainCount == 5 ? remainCount.ToString() : "Max";
                }
                else
                {
                    m_btnComposeMax.gameObject.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 选中合成材料
        /// </summary>
        /// <param name="item">合成材料</param>
        void SelectComposeMaterial(UIItemModule item)
        {
            if (item == null) return;

            item_config config = item.GetItemConfig();
            equipment_materials targetMaterial = GameEntry.EquipmentData.GetComposeTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                long count = GameEntry.LogicData.BagData.GetAmountById(config.id);
                if (count < targetMaterial.synth_num.num)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1364)
                    });
                    return;
                }
            }

            if (curSelectCompose)
            {
                curSelectCompose.IsSelect(false);

                if (curSelectCompose != item)
                {
                    m_btnComposeMax.gameObject.SetActive(false);
                }
            }

            if (item == null) return;
            
            curSelectCompose = item;
            curSelectCompose.IsSelect(true);
            RefreshComposeInfo();
        }

        /// <summary>
        /// 刷新合成材料信息
        /// </summary>
        void RefreshComposeInfo()
        {
            if (curSelectCompose == null) return;
            item_config config = curSelectCompose.GetItemConfig();

            for (int i = 0; i < composeSlot.Count; i++)
            {
                UIItemModule item = composeSlot[i];
                item.ItemId = config.id;
                item.itemModule.ItemId = config.id;
                item.InitConfigData();
                item.DisplayInfo();
                item.gameObject.SetActive(true);
            }
            m_txtComposeSlot.text = curSelectCompose.Name;
            SetTextColor(m_txtComposeSlot, curSelectCompose.Quality);
            m_txtComposeSlot.gameObject.SetActive(true);
            m_txtComposeTitle.gameObject.SetActive(false);
            m_txtComposeResult.gameObject.SetActive(true);

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetComposeTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                composeResult.ItemId = targetMaterial.item_id;
                composeResult.itemModule.ItemId = targetMaterial.item_id;
                composeResult.InitConfigData();
                composeResult.DisplayInfo();
                m_txtComposeResult.text = composeResult.Name;
                SetTextColor(m_txtComposeResult, composeResult.Quality);
                composeResult.gameObject.SetActive(true);
                m_btnCompose.SetButtonGray(false);
                m_btnCompose.isEnable = true;
            }
            else
            {
                m_txtComposeTitle.gameObject.SetActive(true);
                m_txtComposeTitle.text = ToolScriptExtend.GetLang(1358);
                composeResult.gameObject.SetActive(false);
                m_txtComposeResult.gameObject.SetActive(false);
                m_btnCompose.SetButtonGray(true);
                m_btnCompose.isEnable = false;
            }
        }

        /// <summary>
        /// 重置合成界面
        /// </summary>
        void ResetComposeInfo()
        {
            Timers.Instance.Add("EquipmentResetComposeInfo", 0.7f, (param) =>
            {
                for (int i = 0; i < composeSlot.Count; i++)
                {
                    UIItemModule item = composeSlot[i];
                    item.gameObject.SetActive(false);
                }

                if (composeResult != null)
                {
                    composeResult.gameObject.SetActive(false);
                }

                m_txtComposeTitle.text = ToolScriptExtend.GetLang(1033);
                m_txtComposeTitle.gameObject.SetActive(true);
                m_txtComposeResult.gameObject.SetActive(false);
                m_txtComposeSlot.gameObject.SetActive(false);
            }, 1);

            m_btnCompose.SetButtonGray(true);
            m_btnCompose.isEnable = false;

            if (curSelectCompose)
            {
                curSelectCompose.IsSelect(false);
            }

            curSelectCompose = null;
        }

        /// <summary>
        /// 合成材料
        /// </summary>
        /// <param name="type">类型 0:单个 1:一键</param>
        void ComposeMaterial(uint type)
        {
            if (!canClickCompose) return;

            if (curSelectCompose == null) return;

            item_config config = curSelectCompose.GetItemConfig();

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetComposeTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                bool canCompose = GameEntry.EquipmentData.CanMaterialCompose(targetMaterial.item_id, out string buildName, out int buildLevel);
                if (!canCompose)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLangFormat(1100123, buildName, buildLevel.ToString())
                    });
                    return;
                }

                long count = GameEntry.LogicData.BagData.GetAmountById(config.id);
                if (count < targetMaterial.synth_num.num)
                {
                    return;
                }

                ColorLog.Pink("材料合成", targetMaterial.item_id);
                canClickCompose = false;
                GameEntry.EquipmentData.RequestEquipmentMaterialSynthesis(targetMaterial.item_id, type, (result)=>
                {
                    canClickCompose = true;
                    ColorLog.Pink("材料合成回调", result);
                    string name = composeResult.Name;
                    long num = 1;

                    if (result?.Rewards.Count > 0)
                    {
                        num = result.Rewards[0].Amount;
                    }

                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLangFormat(1360, name, num.ToString())
                    });
                    RefreshComposeItem();

                    for (int i = 0; i < effectComposeList.Count; i++)
                    {
                        effectComposeList[i].SetActive(false);
                        effectComposeList[i].SetActive(true);
                    }
                    Timers.Instance.Remove(EquipmentComposeEffectDelay);
                    Timers.Instance.Add(EquipmentComposeEffectDelay, 0.5f, (param) =>
                    {
                        item_config resultConfig = ToolScriptExtend.GetItemConfig(targetMaterial.item_id);
                        if (resultConfig != null)
                        {
                            switch (resultConfig.quality)
                            {
                                case quality.quality_blue:
                                    effectCompose3.SetActive(false);
                                    effectCompose3.SetActive(true);
                                    break;
                                case quality.quality_purple:
                                    effectCompose2.SetActive(false);
                                    effectCompose2.SetActive(true);
                                    break;
                                case quality.quality_orange:
                                    effectCompose1.SetActive(false);
                                    effectCompose1.SetActive(true);
                                    break;
                            }
                        }
                    }, 1);

                    long count = GameEntry.LogicData.BagData.GetAmountById(config.id);
                    if (count < targetMaterial.synth_num.num)
                    {
                        ResetComposeInfo();
                    }
                });
            }
        }

        #endregion

        #region 材料分解

        /// <summary>
        /// 创建可分解的材料
        /// </summary>
        void GenerateResolveItem()
        {
            List<equipment_materials> list = GameEntry.EquipmentData.GetEquipmentMaterialsConfig();
            for (int i = 0; i < list.Count; i++)
            {
                GameObject obj = new();
                obj.AddComponent<RectTransform>();
                obj.transform.SetParent(m_transContentResolve, false);
                obj.transform.localScale = new Vector3(0.85f, 0.85f, 1f);
                int temp = i;
                long count = GameEntry.LogicData.BagData.GetAmountById(list[temp].item_id);
                BagManager.CreatItem(obj.transform, list[temp].item_id, count, (item) =>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.SwitchQualityIcon(true);
                    item.SetClick(()=>
                    {
                        Timers.Instance.Remove("EquipmentResetResolveInfo");
                        Timers.Instance.Remove(EquipmentResolveEffectDelay);
                        SelectResolveMaterial(item);
                    });
                    item.transform.parent.gameObject.SetActive(item.Count > 0);
                    resolveItem.Add(item);
                });
            }

            GameObject objSlot = new();
            RectTransform rectSlot = objSlot.AddComponent<RectTransform>();
            objSlot.transform.SetParent(m_transResolveSlot, false);
            objSlot.transform.localScale = new Vector3(0.88f, 0.88f, 1f);
            rectSlot.anchoredPosition = new Vector2(0f, -5f);
            BagManager.CreatItem(objSlot.transform, list[0].item_id, 1, (item) =>
            {
                item.GetComponent<UIButton>().useTween = false;
                item.IsShowCount(false);
                item.SwitchQualityIcon(true);
                item.SetClick(()=>
                {
                    ShowItemTip(item);
                });
                item.gameObject.SetActive(false);
                resolveSlot = item;
            });

            for (int i = 0; i < 4; i++)
            {
                GameObject obj = new();
                obj.AddComponent<RectTransform>();
                Transform parent = m_transResolveResult.GetChild(i);
                obj.transform.SetParent(parent, false);
                obj.transform.localScale = new Vector3(0.86f, 0.86f, 1f);
                int temp = i;
                BagManager.CreatItem(obj.transform, list[temp].item_id, 1, (item) =>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.IsShowCount(false);
                    item.SwitchQualityIcon(true);
                    item.SetClick(()=>
                    {
                        ShowItemTip(item);
                    });
                    item.gameObject.SetActive(false);
                    resolveResult.Add(item);
                });
            }
        }

        /// <summary>
        /// 刷新分解材料列表
        /// </summary>
        void RefreshResolveItem()
        {
            for (int i = 0; i < resolveItem.Count; i++)
            {
                resolveItem[i].itemModule.Count = GameEntry.LogicData.BagData.GetAmountById(resolveItem[i].ItemId);
                resolveItem[i].DisplayInfo();
                resolveItem[i].transform.parent.gameObject.SetActive(resolveItem[i].itemModule.Count > 0);
            }

            if (curSelectResolve == null) return;

            item_config config = curSelectResolve.GetItemConfig();

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetResolveTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                long count = GameEntry.LogicData.BagData.GetAmountById(config.id);
                if (count == 0)
                {
                    SelectResolveMaterial(null);
                }

                if (count >= 5)
                {
                    m_btnResolveMax.gameObject.SetActive(true);
                    m_txtResolveMax.text = count == 5 ? count.ToString() : "Max";
                }
                else
                {
                    m_btnResolveMax.gameObject.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 选中分解材料
        /// </summary>
        /// <param name="item">分解材料</param>
        void SelectResolveMaterial(UIItemModule item)
        {
            if (curSelectResolve)
            {
                curSelectResolve.IsSelect(false);

                if (curSelectResolve != item)
                {
                    m_btnResolveMax.gameObject.SetActive(false);
                }
            }

            if (item == null) return;
            
            curSelectResolve = item;
            curSelectResolve.IsSelect(true);
            RefreshResolveInfo();
        }

        /// <summary>
        /// 刷新分解材料信息
        /// </summary>
        void RefreshResolveInfo()
        {
            if (curSelectResolve == null) return;
            item_config config = curSelectResolve.GetItemConfig();

            resolveSlot.ItemId = config.id;
            resolveSlot.itemModule.ItemId = config.id;
            resolveSlot.InitConfigData();
            resolveSlot.DisplayInfo();
            resolveSlot.gameObject.SetActive(true);

            m_txtResolveSlot.text = curSelectResolve.Name;
            SetTextColor(m_txtResolveSlot, curSelectResolve.Quality);
            m_txtResolveSlot.gameObject.SetActive(true);

            m_txtResolveResult.gameObject.SetActive(true);
            m_txtResolveTitle.gameObject.SetActive(false);

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetResolveTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                for (int i = 0; i < resolveResult.Count; i++)
                {
                    UIItemModule item = resolveResult[i];
                    item.ItemId = targetMaterial.item_id;
                    item.itemModule.ItemId = targetMaterial.item_id;
                    item.InitConfigData();
                    item.DisplayInfo();
                    item.gameObject.SetActive(true);

                    m_txtResolveResult.text = item.Name;
                    SetTextColor(m_txtResolveResult, item.Quality);
                }

                m_btnResolve.SetButtonGray(false);
                m_btnResolve.isEnable = true;
            }
            else
            {
                for (int i = 0; i < resolveResult.Count; i++)
                {
                    UIItemModule item = resolveResult[i];
                    item.gameObject.SetActive(false);
                }

                m_txtResolveTitle.text = ToolScriptExtend.GetLang(1359);
                m_txtResolveTitle.gameObject.SetActive(true);
                m_txtResolveResult.gameObject.SetActive(false);
                m_btnResolve.SetButtonGray(true);
                m_btnResolve.isEnable = false;
            }
        }

        /// <summary>
        /// 重置分解界面
        /// </summary>
        void ResetResolveInfo()
        {
            Timers.Instance.Add("EquipmentResetResolveInfo", 0.7f, (param) =>
            {
                for (int i = 0; i < resolveResult.Count; i++)
                {
                    UIItemModule item = resolveResult[i];
                    item.gameObject.SetActive(false);
                }

                if (resolveSlot != null)
                {
                    resolveSlot.gameObject.SetActive(false);
                }

                m_txtResolveTitle.text = ToolScriptExtend.GetLang(1036);
                m_txtResolveTitle.gameObject.SetActive(true);
                m_txtResolveResult.gameObject.SetActive(false);
                m_txtResolveSlot.gameObject.SetActive(false);
            }, 1);

            m_btnResolve.SetButtonGray(true);
            m_btnResolve.isEnable = false;

            SelectResolveMaterial(null);

            curSelectResolve = null;
        }

        /// <summary>
        /// 分解材料
        /// </summary>
        /// <param name="type">类型 0:单个 1:一键</param>
        void ResolveMaterial(uint type)
        {
            if (!canClickResolve) return;

            if (curSelectResolve == null) return;

            item_config config = curSelectResolve.GetItemConfig();

            equipment_materials targetMaterial = GameEntry.EquipmentData.GetResolveTargetMaterial(config.id);
            if (targetMaterial != null)
            {
                UIItemModule item = resolveResult[0];
                if (item == null) return;

                ColorLog.Pink("材料分解", config.id);
                canClickResolve = false;
                GameEntry.EquipmentData.RequestEquipmentMaterialResolve(config.id, type, (result)=>
                {
                    canClickResolve = true;
                    ColorLog.Pink("材料分解回调", result);
                    equipment_materials curMaterial = GameEntry.EquipmentData.GetEquipmentMaterial(config.id);

                    string name = item.Name;
                    long num = curMaterial.break_item.num;

                    if (result?.Rewards.Count > 0)
                    {
                        num = result.Rewards[0].Amount;
                    }

                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLangFormat(1361, name, num.ToString())
                    });
                    RefreshResolveItem();

                    effectResolve.SetActive(false);
                    effectResolve.SetActive(true);

                    Timers.Instance.Remove(EquipmentResolveEffectDelay);
                    Timers.Instance.Add(EquipmentResolveEffectDelay, 0.5f, (param) =>
                    {
                        item_config resultConfig = ToolScriptExtend.GetItemConfig(targetMaterial.item_id);
                        if (resultConfig != null)
                        {
                            string keyword = string.Empty;
                            switch (resultConfig.quality)
                            {
                                case quality.quality_blue:
                                    keyword = "lansekuang";
                                    break;
                                case quality.quality_purple:
                                    keyword = "zisekuang";
                                    break;
                                case quality.quality_orange:
                                    keyword = "huangsekuang";
                                    break;
                            }

                            for (int i = 0; i < effectResolveList.Count; i++)
                            {
                                if (keyword != string.Empty && effectResolveList[i].name.Contains(keyword))
                                {
                                    effectResolveList[i].SetActive(false);
                                    effectResolveList[i].SetActive(true);
                                }
                            }
                        }
                    }, 1);

                    long count = GameEntry.LogicData.BagData.GetAmountById(curMaterial.item_id);
                    if (count <= 0)
                    {
                        ResetResolveInfo();
                    }
                });
            }
        }

        #endregion

        /// <summary>
        /// 根据品质设置文本颜色
        /// </summary>
        /// <param name="uiText">文本组件</param>
        /// <param name="quality">品质</param>
        void SetTextColor(UIText uiText, int quality)
        {
            if (!qualityColor.ContainsKey(quality)) return;
            ColorUtility.TryParseHtmlString(qualityColor[quality], out Color color);
            uiText.color = color;
        }

        /// <summary>
        /// 显示物品提示
        /// </summary>
        /// <param name="item">物品</param>
        /// <param name="isShowCount">是否显示数量</param>
        /// <param name="isCheckEnough">是否检查数量不足</param>
        void ShowItemTip(UIItemModule item, bool isShowCount = true, bool isCheckEnough = false)
        {
            if (isCheckEnough)
            {
                if (curSelect != null)
                {
                    item_config config = curSelect.GetItemConfig();
                    equipment_config equipmentConfig = GameEntry.EquipmentData.GetEquipmentConfigByID(config.id);
                    itemid costID = equipmentConfig.cost_materials[0].item_id;
                    long costNum = equipmentConfig.cost_materials[0].num;
                    long count = GameEntry.LogicData.BagData.GetAmountById(costID);

                    if (count < costNum)
                    {
                        itemid needItemId = costID;
                        long needNum = costNum;

                        ItemModule itemModule = new(needItemId);
                        GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(needNum));
                        return;
                    }
                }
            }

            item.OpenTips();

            if (!isShowCount)
            {
                UIItemTextTip itemTextTip = GameEntry.UI.GetUIForm(EnumUIForm.UIItemTextTip) as UIItemTextTip;
                if (itemTextTip)
                {
                    itemTextTip.ShowCountText(false);
                }
                else
                {
                    Timers.Instance.Add("EquipmentUIItemTextTip", 0.02f, (param) =>
                    {
                        UIItemTextTip itemTextTip = GameEntry.UI.GetUIForm(EnumUIForm.UIItemTextTip) as UIItemTextTip;
                        if (itemTextTip)
                        {
                            itemTextTip.ShowCountText(false);
                        }
                    }, 1);
                }
            }
        }
    }
}

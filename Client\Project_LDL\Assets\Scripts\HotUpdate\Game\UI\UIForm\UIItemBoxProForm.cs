using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using Game.Hotfix.Config;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
namespace Game.Hotfix
{
    public partial class UIItemBoxProForm : UGuiFormEx
    {
        public itemid showId;
        public Dictionary<int,List<ItemModule>> configDataDic = new();
        public SortedDictionary<int,List<ItemModule>> sortedDic = new();
        public int AllWeight;
        public SortedDictionary<int,float> rationDic = new();
        float subRation = 0f;
        int MoreQuality = 0;
        float maxRation = 0f;

        private Transform m_contentTransform;
        private List<GameObject> m_createdItems = new();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            // 获取ScrollView的Content节点
            m_contentTransform = m_transContent.transform;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            showId = (itemid)userData;
            List<item_randomreward> configData = GameEntry.LDLTable.GetTable<item_randomreward>();
            foreach (var item in configData)
            {
                if (item.item_id==showId)
                {
                    item_config data = GameEntry.LDLTable.GetTableById<item_config>((int)item.item_produce[0].item_id);                 
                    int Quality = (int)data.quality;

                    if(!configDataDic.ContainsKey(Quality))
                    {
                        configDataDic.Add(Quality,new List<ItemModule>());
                    }
                    AllWeight += item.weight;

                    // 创建ItemModule对象
                    ItemModule itemModule = new ItemModule();
                    itemModule.SetData(item.item_produce[0].item_id, item.item_produce[0].num);
                    configDataDic[Quality].Add(itemModule);
                }
               
            }
            var index = 0;
            for (int i = 1; i < 6 ; i++)
            {
                if(configDataDic.ContainsKey(i))
                {
                    if(index == 0){
                        index = i;
                    }
                    string ration = GetQualityTotalPro(i);

                    // configDataDic现在已经是ItemModule列表，直接使用
                    sortedDic.Add(i, configDataDic[i]);
                }
            }
           
            if(rationDic.Count==sortedDic.Count){              
                foreach (var item in rationDic)
                {
                    
                    
                    if(item.Value>maxRation){
                        maxRation = item.Value;
                        MoreQuality = item.Key;  //index           
                    }
                    // else
                    // {
                    //     subRation += item.Value; 
                    // }
                    // index +=1;
                }                
            }
            foreach (var item in rationDic)
            {
                if(item.Key != MoreQuality){
                    subRation += item.Value;
                }
            }
            //Debug.LogError(configDataDic.Count);
            RefreshItemList();
        }

        public long GetCount(ItemModule module){
            List<item_randomreward> configData = GameEntry.LDLTable.GetTable<item_randomreward>();
            foreach (var item in configData)
            {
                if(item.item_id == showId){
                    if(item.item_produce[0].item_id==module.ItemId && item.item_produce[0].num == module.Count){
                        return item.item_produce[0].num;
                    }
                }
            }
            return 1;
        }


        public string GetQualityTotalPro(int Quality){
            List<item_randomreward> configData = GameEntry.LDLTable.GetTable<item_randomreward>();
            float quality_weight = 0;
            if(configDataDic.ContainsKey((int)Quality)){
                foreach (var itemModule in configDataDic[(int)Quality])
                {
                    quality_weight += GetSingleWeight(itemModule.ItemId);
                }
            }
            else
            {
               return ""; 
            }
            //return Math.Round((float)quality_weight/AllWeight*100).ToString()+"%";
            float value = (float)Math.Truncate((float)quality_weight/AllWeight*1000);
            if(!rationDic.ContainsKey(Quality)){
                rationDic[Quality] = value;
            }
            return (Math.Truncate((float)quality_weight/AllWeight*1000)*0.1).ToString();
        }
        public int GetSingleWeight(itemid id){
            List<item_randomreward> tempData = GameEntry.LDLTable.GetTable<item_randomreward>();
            foreach (var item in tempData)
            {
                if(item.item_id == showId && item.item_produce[0].item_id==id){
                    return item.weight;
                }
            }
            return 0;
        }
    
        public float GetSingleWeightF(ItemModule ItemModule){
            List<item_randomreward> tempData = GameEntry.LDLTable.GetTable<item_randomreward>();
            foreach (var item in tempData)
            {
                if(item.item_id == showId && item.item_produce[0].item_id==ItemModule.ItemId && ItemModule.Count == item.item_produce[0].num){
                    return item.weight;
                }
            }
            return 0;
        }


        private void RefreshItemList()
        {
            ClearItemList();

            int index = 0;
            foreach (var kvp in sortedDic.Reverse())
            {
                CreateQualityGroup(kvp.Key, kvp.Value, index);
                index++;
            }

          
        }

        private void ClearItemList()
        {
            foreach (var item in m_createdItems)
            {
                if (item != null)
                {
                    Destroy(item);
                }
            }
            m_createdItems.Clear();
        }

        private void CreateQualityGroup(int qualityKey, List<ItemModule> itemList, int index)
        {
            // 实例化一个新的DetailItem
            GameObject groupObj = Instantiate(m_goDetailItem, m_contentTransform);
            m_createdItems.Add(groupObj);
            groupObj.SetActive(true);
            quality Quality = quality.quality_nil;

            // 清空main节点下的所有子物体
            Transform mainTransform = groupObj.transform.Find("main");
            for (int i = mainTransform.childCount - 1; i >= 0; i--)
            {
                DestroyImmediate(mainTransform.GetChild(i).gameObject);
            }

            // 创建物品
            foreach (var itemModule in itemList)
            {
                item_config data = GameEntry.LDLTable.GetTableById<item_config>(itemModule.ItemId);
                Quality = data.quality;

                BagManager.CreatItem(mainTransform, data.id, itemModule.Count, (ItemModule) =>
                {
                    ItemModule.SetClick(() =>
                    {
                        ItemModule.OpenTips();
                    });
                    ItemModule.GetComponent<UIButton>().useTween = false;
                    ItemModule.SetScale(0.7f);
                    ItemModule.ShowPro((float)GetSingleWeightF(ItemModule.itemModule) / AllWeight * 100);

                    //如果是最后一个，刷新布局
                    if (index == sortedDic.Count - 1)
                    {
                        LayoutRebuilder.ForceRebuildLayoutImmediate(groupObj.GetComponent<RectTransform>());
                        // 强制重建布局
                        LayoutRebuilder.ForceRebuildLayoutImmediate(m_contentTransform.GetComponent<RectTransform>());
                    }
                });
            }

            // 设置标题和比例
            UIText title = groupObj.transform.Find("title").GetComponent<UIText>();
            UIText ratio = groupObj.transform.Find("ratio").GetComponent<UIText>();

            SetQualityTitle(Quality, title);
            SetQualityRatio(Quality, ratio);
            
        }

        private void SetQualityTitle(quality Quality, UIText title)
        {
            if(Quality == Config.quality.quality_white){
                title.text = ToolScriptExtend.GetLang(1100064);
            }
            else if(Quality == Config.quality.quality_green){
                title.text = ToolScriptExtend.GetLang(1100065);
            }
             else if(Quality == Config.quality.quality_blue){
                title.text = ToolScriptExtend.GetLang(1100066);
            }
             else if(Quality == Config.quality.quality_purple){
                title.text = ToolScriptExtend.GetLang(1100067);
            }
             else if(Quality == Config.quality.quality_orange){
                title.text = ToolScriptExtend.GetLang(1100068);
            }
            else if(Quality == Config.quality.quality_red){
                title.text = ToolScriptExtend.GetLang(1100069);
            }
        }

        private void SetQualityRatio(quality Quality, UIText ratio)
        {
            ratio.text = GetQualityTotalPro((int)Quality)+"%";
            if(MoreQuality==(int)Quality){
                ratio.text = ((1000-subRation)*0.1).ToString()+"%";
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            ClearItemList();

            configDataDic.Clear();
            sortedDic.Clear();
            rationDic.Clear();
            AllWeight = 0;
            subRation = 0f;
            MoreQuality = 0;
            maxRation = 0f;
        }

        public void InitShowData(){
            item_config item_Config = GameEntry.LDLTable.GetTableById<item_config>(showId);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {

        }

        private void OnBtnCloseClick()
        {
            Close();
        }
    }
}

using System;
using System.Collections;
using System.Collections.Generic;
using Common;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public enum TabViewType
    {
        Nil,
        PreView,
        All,
        Deploy
    }

    public enum SurvivorsType
    {
        All,
        Legendary,
        Epic,
        Others
    }

    //派遣筛选
    public enum FilterDispatch
    {
        All,
        IsDispatch,
        NotDispatch,
    }    
    
    public enum FilterAcive
    {
        All,
        IsActive,
        NotActive,
    }
    
    public enum FilterUpStar
    {
        All,
        CanUp,
    }
    
    public partial class UIBuildingSurvivorForm : UGuiFormEx
    {
        private List<SurvivorMoudle> m_SurvivorList;
        private TabViewType m_CurSelectViewType = TabViewType.All;
        private List<survivor_list> m_SurvivorListCfgList = new List<survivor_list>();
        private SurvivorsType m_SelectSurvivorsQuality;
        private RectTransform m_DispatchBuildListRect;
        private RectTransform m_AllDispatchBuildListRect;

        private float m_TimeElapsed = 0;
        private float m_TimeInterval = 1;
        private bool m_IsClickQuickDispatch = false;

        private FilterDispatch m_FliterDispatch = FilterDispatch.All;
        private FilterAcive m_FliterAcive = FilterAcive.All;
        private FilterUpStar m_FliterUpStar = FilterUpStar.All;

        private bool m_IsOpenFilter = false;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitToggle();
            InitTableViewSurvivorList();
            m_DispatchBuildListRect = m_scrollviewAttrList.content;
            m_AllDispatchBuildListRect = m_scrollviewDispatchList.content;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_SurvivorList = GameEntry.LogicData.SurvivorData.SurvivorMoudleList;

            // todo 检查红点页签默认打开红点页签
            m_CurSelectViewType = TabViewType.All;
            m_SelectSurvivorsQuality = SurvivorsType.All;
            m_togAll.isOn = true;
            m_togSubAll.isOn = true;

            m_FliterDispatch = FilterDispatch.All;
            m_FliterAcive = FilterAcive.All;
            m_FliterUpStar = FilterUpStar.All;
            
            //SwitchViewByType();
            OnInitRedPoint();
            if (!GameEntry.Event.Check(RedPointEventArgs.EventId, OnRedDotChange)) 
                GameEntry.Event.Subscribe(RedPointEventArgs.EventId, OnRedDotChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_SurvivorList = null;
            m_CurSelectViewType = TabViewType.PreView;
            if (!GameEntry.Event.Check(RedPointEventArgs.EventId, OnRedDotChange))
                GameEntry.Event.Unsubscribe(RedPointEventArgs.EventId, OnRedDotChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            RefreshUIDispatchParams refreshParams = userData as RefreshUIDispatchParams;
            if (refreshParams == null)
            {
                return;
            }

            if (refreshParams.m_RefreshType == UISurvivorRefreshType.DispatchRefresh)
            {
                RefreshDeploy();
            }
            else if (refreshParams.m_RefreshType == UISurvivorRefreshType.UpStarRefresh || refreshParams.m_RefreshType == UISurvivorRefreshType.ActiveRefresh)
            {
                if (m_CurSelectViewType == TabViewType.All)
                {
                    ResetAllGroup();
                }
                else if (m_CurSelectViewType == TabViewType.Deploy)
                {
                    RefreshDeploy();
                }
                
            }
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            m_TimeElapsed += elapseSeconds;

            if (m_TimeElapsed > m_TimeInterval)
            {
                m_TimeElapsed -= m_TimeInterval;
            }

            if (m_IsClickQuickDispatch)
            {
                m_IsClickQuickDispatch = false;
            }
        }
        
        private void OnInitRedPoint()
        {
            var orange_redState = RedPointManager.Instance.IsRed(EnumRed.Survivor_UpStar_Orange.ToString());
            m_imgRedOrange.gameObject.SetActive(orange_redState);
            
            var purple_redState = RedPointManager.Instance.IsRed(EnumRed.Survivor_UpStar_Purple.ToString());
            m_imgRedPurple.gameObject.SetActive(purple_redState);
            
            m_imgTabAllRed.gameObject.SetActive(orange_redState || purple_redState);
        }
        
        private void OnRedDotChange(object sender, GameEventArgs e)
        {
            RedPointEventArgs args = (RedPointEventArgs)e;
            Dictionary<string, bool> dic = args.RedPointDic;

            if (dic != null)
            {
                var redId = EnumRed.Survivor_UpStar_Orange.ToString();
                var orangeRedState = false;
                var purpleRedState = false;
                if (dic.ContainsKey(redId))
                {
                    orangeRedState = RedPointManager.Instance.IsRed(redId);
                    m_imgRedOrange.gameObject.SetActive(orangeRedState);
                }

                redId = EnumRed.Survivor_UpStar_Purple.ToString();
                if (dic.ContainsKey(redId))
                {
                    purpleRedState = RedPointManager.Instance.IsRed(redId);
                    m_imgRedPurple.gameObject.SetActive(purpleRedState); 
                }
                
                m_imgTabAllRed.gameObject.SetActive(orangeRedState || purpleRedState);
            }
        }

        void InitToggle()
        {
            m_togPreView.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_CurSelectViewType = TabViewType.PreView;
                    SwitchViewByType();
                }
            });
            m_togAll.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_CurSelectViewType = TabViewType.All;
                    SwitchViewByType();
                }
            });
            m_togDeploy.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_CurSelectViewType = TabViewType.Deploy;
                    SwitchViewByType();
                }
            });      
            
            m_togSubAll.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_SelectSurvivorsQuality = SurvivorsType.All;
                    ResetAllGroup();
                }
            });
            
            m_togSubLegendary.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_SelectSurvivorsQuality = SurvivorsType.Legendary;
                    ResetAllGroup();
                }
            });
            
            m_togSubEpic.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_SelectSurvivorsQuality = SurvivorsType.Epic;
                    ResetAllGroup();
                }
            });
            
            m_togSubOthers.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_SelectSurvivorsQuality = SurvivorsType.Others;
                    ResetAllGroup();
                }
            });
        }

        private void SwitchViewByType()
        {
            m_goOverViewGroup.gameObject.SetActive(m_CurSelectViewType == TabViewType.PreView);
            m_goAllGroup.gameObject.SetActive(m_CurSelectViewType == TabViewType.All);
            m_goDeployGroup.gameObject.SetActive(m_CurSelectViewType == TabViewType.Deploy);

            if (m_CurSelectViewType == TabViewType.PreView)
            {
                ResetOverViewGroup();
            }
            else if (m_CurSelectViewType == TabViewType.All)
            {
                RefreshFilterGroup();   
                ResetAllGroup();
            }
            else if (m_CurSelectViewType == TabViewType.Deploy)
            {
                RefreshDeploy();
            }
        }

        #region 预览部分

        private void ResetOverViewGroup()
        {
            int allSurviviorCount = GameEntry.LogicData.SurvivorData.GetAllSurviviorCount();
            m_txtTotal.text = $"{m_SurvivorList.Count}/{allSurviviorCount}";
            m_sliderTotal.value = (float)m_SurvivorList.Count / allSurviviorCount;
            
            int allLendaryCount = GameEntry.LogicData.SurvivorData.GetTotalSurviviorCfgCountByType(quality.quality_orange);
            int curLendaryCount = GameEntry.LogicData.SurvivorData.GetSurviviorMoudleCountByType(quality.quality_orange);
            m_txtLegendary.text = $"{curLendaryCount}/{allLendaryCount}";
            m_sliderLegendary.value = (float)curLendaryCount / allLendaryCount;

            int allEpicCount = GameEntry.LogicData.SurvivorData.GetTotalSurviviorCfgCountByType(quality.quality_purple);
            int curEpicCount = GameEntry.LogicData.SurvivorData.GetSurviviorMoudleCountByType(quality.quality_purple);
            m_txtEpic.text = $"{curEpicCount}/{allEpicCount}";
            m_sliderEpic.value = (float)curEpicCount / allEpicCount;

            int totalSurvivorPower = GameEntry.LogicData.SurvivorData.GetTotalSurvivorPower();
            m_txtSurvivorPower.text = ToolScriptExtend.FormatNumberWithSeparator(totalSurvivorPower);

            RefreshBuildingDispatch();
        }

        private void RefreshBuildingDispatch()
        {
            List<buildtype> dispatchBuildingList = GetDispatchList();
            for (int i = 0; i < dispatchBuildingList.Count; i++)
            {
                GameObject itemObj;
                if (m_DispatchBuildListRect.transform.childCount <= i)
                {
                    itemObj = Instantiate(m_goAttrItem,m_DispatchBuildListRect.transform);
                    itemObj.SetActive(true);
                }
                else
                {
                    itemObj = m_DispatchBuildListRect.transform.GetChild(i).gameObject;
                }

                UpdateDispatchList(i, itemObj, dispatchBuildingList[i]);
            }
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_DispatchBuildListRect);
        }

        private List<buildtype> GetDispatchList()
        {
            List<buildtype> dispatchList = new List<buildtype>();

            for (int i = 0; i < m_SurvivorList.Count; i++)
            {
                if (m_SurvivorList[i].BuildingId != 0)
                {
                    BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(m_SurvivorList[i].BuildingId);
                    bool exists = dispatchList.Exists(d => d == buildingModule.GetBuildingType());
                    if (!exists)
                    {
                        dispatchList.Add(buildingModule.GetBuildingType());
                    }
                }
            }
            return dispatchList;
        }

        private void UpdateDispatchList(int index, GameObject obj, buildtype dispatchBuildType)
        {
            Transform itemTransform    = obj.transform;
            Text txtTitle              = itemTransform.Find("titleBg/txtTitle").GetComponent<Text>();
            Transform listTransform    = itemTransform.Find("attrList").transform;
            RectTransform rectTransform = listTransform.gameObject.GetComponent<RectTransform>();

            string buildingName = GameEntry.LogicData.BuildingData.GetBuildingNameByBuildType(dispatchBuildType);
            txtTitle.text = buildingName;

            List<BuildingModule> list = GameEntry.LogicData.BuildingData.GetAllDispatchBuildingModuleListByType(dispatchBuildType);
            listTransform.gameObject.DestroyAllChild();
            int attrIndex = 0;
            for (int i = 0; i < list.Count; i++)
            {
                BuildingModule buildingModule = list[i];
                Dictionary<attributes_type, int> attrValueDic = new Dictionary<attributes_type, int>();
                Dictionary<attributes_type, valuetype> attrValueTypeDic = new Dictionary<attributes_type, valuetype>();
                foreach (uint survivorId in buildingModule.SurvivorList)
                {
                    SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(survivorId);
                    if (survivorMoudle != null)
                    {
                        survivor_star survivorStarConfig = GameEntry.LogicData.SurvivorData.GetSurvivorStarConfig(survivorMoudle.SurvivorCfg.profession,
                            (int)survivorMoudle.StarStage,survivorMoudle.Quality);
                        List<attributes> starAttributes = survivorStarConfig.star_attributes;
                        for (int k = 0; k < starAttributes.Count; k++)
                        {
                            attributes_type attributesType = starAttributes[k].attributes_type;
                            valuetype valueType = starAttributes[k].value_type;
                            attrValueTypeDic[attributesType] = valueType;
                            if (!attrValueDic.ContainsKey(attributesType))
                            {
                                attrValueDic[attributesType] = starAttributes[k].value;
                            }
                            else
                            {
                                attrValueDic[attributesType] += starAttributes[k].value;
                            }
                        }
                    }
                }
                
                foreach (KeyValuePair<attributes_type,int> attrKV in attrValueDic)
                {
                    attributes_type attributesType = attrKV.Key;
                    int attrValue = attrKV.Value;
                    valuetype valuetype = attrValueTypeDic[attributesType];
                    GameObject itemObj = Instantiate(m_goSubAttrItem,listTransform);
                    itemObj.SetActive(true);
                    UpdataDispatchAttrsItem(attrIndex,itemObj,buildingModule,attributesType,attrValue,valuetype);
                    attrIndex += 1;
                }
            }
            LayoutRebuilder.ForceRebuildLayoutImmediate(rectTransform);
        }

        private void UpdataDispatchAttrsItem(int index,GameObject obj, BuildingModule buildingModule,attributes_type attrType, int value,valuetype valueType)
        {
            Transform itemTransform   = obj.transform;
            RectTransform objRect        = obj.transform.GetComponent<RectTransform>();
            UIImage bg                = itemTransform.Find("bg").GetComponent<UIImage>();
            Text txtAttrName          = itemTransform.Find("txtAttrName").GetComponent<UIText>();
            Text txtAttrValue         = itemTransform.Find("txtAttrValue").GetComponent<UIText>();

            if (buildingModule == null)
            {
                return;
            }

            bg.gameObject.SetActive(index % 2 != 0);
            string attrName = ToolScriptExtend.GetNameByAttrbuteType(attrType);
            txtAttrName.text = $"(Lv.{buildingModule.LEVEL} {buildingModule.BuildingName}){attrName}";
            string valueStr = string.Empty;
            if (valueType == valuetype.valuetype_2)
            {
                valueStr = $"+{value/100}%";
            }
            else if (attrType == attributes_type.attributes_type_55 ||
                     attrType == attributes_type.attributes_type_56 ||
                     attrType == attributes_type.attributes_type_63 ||
                     attrType == attributes_type.attributes_type_73)
            {
                valueStr = $"+{TimeHelper.FormatGameTimeWithDays((int)value)}";
            }
            else
            {
                valueStr = $"+{value}";
            }

            txtAttrValue.text = valueStr;
            RectTransform textRect = txtAttrName.transform.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(textRect);
            objRect.sizeDelta = new Vector2(objRect.sizeDelta.x, textRect.rect.height + 30);
        }

        #endregion

        #region 所有列表部分

        private void InitTableViewSurvivorList()
        {
            m_TableViewVSurvivorList.GetItemCount = GetTabCount;
            m_TableViewVSurvivorList.GetItemGo = () =>
            {
                return m_goSurvivorListLine;
            };
            m_TableViewVSurvivorList.UpdateItemCell = UpdateSurvivorLine;
  
        }

        private int GetTabCount()
        {
            //return m_SurvivorListCfgList.Count / 4;
            float line = (float)m_SurvivorListCfgList.Count / 4;
            return (int)Math.Ceiling(line);
        }
        
        private void ResetAllGroup()
        {
            m_IsOpenFilter = false;
            m_TableViewVSurvivorList.StopScrollView();
            
            RefreshFilterInfo();
            List<survivor_list> cfgList = GetSurvivorList();
            m_SurvivorListCfgList = SortSurvivorCfgList(cfgList);
            m_TableViewVSurvivorList.InitTableViewByIndex(0);
            
        }

        protected List<survivor_list> GetSurvivorList()
        {
            List<survivor_list> survivorList = new List<survivor_list>();
            List<survivor_list> cfgList = GameEntry.LDLTable.GetTable<survivor_list>();
            quality selectQuality = quality.quality_nil;
            if (m_SelectSurvivorsQuality == SurvivorsType.Legendary)
            {
                for (int i = 0; i < cfgList.Count; i++)
                {
                    if (cfgList[i].quality == quality.quality_orange)
                    {
                        survivorList.Add(cfgList[i]);
                    }
                }
            }
            else if (m_SelectSurvivorsQuality == SurvivorsType.Epic)
            {
                for (int i = 0; i < cfgList.Count; i++)
                {
                    if (cfgList[i].quality == quality.quality_purple)
                    {
                        survivorList.Add(cfgList[i]);
                    }
                }
            }
            else if (m_SelectSurvivorsQuality == SurvivorsType.Others)
            {
                for (int i = 0; i < cfgList.Count; i++)
                {
                    if (cfgList[i].quality == quality.quality_blue || cfgList[i].quality == quality.quality_green || cfgList[i].quality == quality.quality_white)
                    {
                        survivorList.Add(cfgList[i]);
                    }
                }
            }
            else
            {
                for (int i = 0; i < cfgList.Count; i++)
                {
                    survivorList.Add(cfgList[i]);
                }
            }

            survivorList = FilterSurvivorCfgList(survivorList);

            return survivorList;
        }

        protected List<survivor_list> FilterSurvivorCfgList(List<survivor_list> survivorLists)
        {
            List<survivor_list> lists = new List<survivor_list>();
            for (int i = 0; i < survivorLists.Count; i++)
            {
                survivor_list survivorCfg = survivorLists[i];
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById((uint)survivorCfg.id);
                bool isActive = survivorMoudle != null;
                bool isPassFilterDispatch = false;
                if (m_FliterDispatch == FilterDispatch.All || (!isActive && m_FliterDispatch == FilterDispatch.NotDispatch))
                {
                    isPassFilterDispatch = true;
                }
                else if (m_FliterDispatch == FilterDispatch.IsDispatch || m_FliterDispatch == FilterDispatch.NotDispatch)
                {
                    if (isActive)
                    {
                        if (m_FliterDispatch == FilterDispatch.IsDispatch)
                        {
                            isPassFilterDispatch = survivorMoudle.BuildingId != 0;
                        }
                        else
                        {
                            isPassFilterDispatch = survivorMoudle.BuildingId == 0;
                        }
                    }
                }

                bool isPassFilterActive = false;
                if (m_FliterAcive == FilterAcive.All)
                {
                    isPassFilterActive = true;
                }
                else
                {
                    isPassFilterActive = (m_FliterAcive == FilterAcive.IsActive && isActive) || (m_FliterAcive == FilterAcive.NotActive && !isActive);
                }

                bool isPassUpStar = false;
                if (m_FliterUpStar == FilterUpStar.All)
                {
                    isPassUpStar = true;
                }
                else
                {
                    isPassUpStar = isActive && survivorMoudle.GetCanIncreaseStar();
                }

                if (isPassFilterDispatch && isPassFilterActive && isPassUpStar)
                {
                    lists.Add(survivorCfg);
                }
            }
            return lists;
        }

        protected List<survivor_list> SortSurvivorCfgList(List<survivor_list> survivorLists)
        {
            survivorLists.Sort((a,b) =>
            {
                SurvivorMoudle aMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById((uint)a.id);
                SurvivorMoudle bMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById((uint)b.id);
                
                bool isAUnLock = aMoudle != null;
                bool isBUnLock = bMoudle != null;
                
                int aIsLock = isAUnLock ? 1000000 : 0;
                int bIsLock = isBUnLock ? 1000000 : 0;

                survivor_list aSurvivorCfg = Game.GameEntry.LDLTable.GetTableById<survivor_list>(a.id);
                survivor_list bSurvivorCfg = Game.GameEntry.LDLTable.GetTableById<survivor_list>(b.id);
                long amountById = GameEntry.LogicData.BagData.GetAmountById(aSurvivorCfg.peace);
                long bmountById = GameEntry.LogicData.BagData.GetAmountById(bSurvivorCfg.peace);
                int aActive = !isAUnLock && amountById >= 10 ? 100000 : 0;
                int bActive = !isBUnLock && bmountById >= 10 ? 100000 : 0;
                
                int aQuality = (int)a.quality * 10000;
                int bQuality = (int)b.quality * 10000;
                
                int aUpStar = 0;
                int bUpStar = 0;

                if (isAUnLock && aMoudle.GetCanIncreaseStar())
                {
                    aUpStar = 1000;
                }

                if (isBUnLock && bMoudle.GetCanIncreaseStar())
                {
                    bUpStar = 1000;
                }
                
                int aIsDispatch = 0;
                int bIsDispatch = 0;

                if (isAUnLock && aMoudle.BuildingId != 0)
                {
                    aIsDispatch = 100;
                }

                if (isBUnLock && bMoudle.BuildingId != 0)
                {
                    bIsDispatch = 100;
                }

                int aStar = isAUnLock ? (int)aMoudle.StarStage : 0;
                int bStar = isBUnLock ? (int)bMoudle.StarStage : 0;
                
                int aWeight = aIsLock + aActive + aUpStar + aIsDispatch + aQuality + aStar;
                int bWeight = bIsLock + bActive + bUpStar + bIsDispatch + bQuality + bStar;

                return bWeight.CompareTo(aWeight);
            });

            return survivorLists;
        }

        private void UpdateSurvivorLine(int index, GameObject obj)
        {
            for (int i = 0; i < 4; i++)
            {
                int dataIndex = index * 4 + i;
                GameObject itemRect;
                if (obj.transform.childCount <= i && dataIndex <= m_SurvivorListCfgList.Count - 1)
                {
                    itemRect = Instantiate(m_goSurvival,obj.transform);
                    itemRect.SetActive(true);
                }
                else
                {
                    if(i+1 > obj.transform.childCount){
                        return;
                    }
                    itemRect = obj.transform.GetChild(i).gameObject;
                }

                if (itemRect != null)
                {
                    UpdateSurvivorItem(dataIndex, itemRect);
                }
            }
        }

        private void UpdateSurvivorItem(int dataIndex , GameObject itemObj)
        {
            if (dataIndex >= m_SurvivorListCfgList.Count)
            {
                return;
            }
            Transform itemTransform    = itemObj.transform; 
            Transform goActive         = itemTransform.Find("goActive");
            UIImage qualityBg          = itemTransform.Find("goActive/qualityBg").GetComponent<UIImage>();
            UIImage imgUnLock          = itemTransform.Find("goActive/qualityBg/imgUnLock").GetComponent<UIImage>();
            UIImage headBg             = itemTransform.Find("goActive/qualityBg/headBg").GetComponent<UIImage>();
            UIImage imgUp              = itemTransform.Find("goActive/imgUp").GetComponent<UIImage>();
            UIImage imgDispatch        = itemTransform.Find("goActive/imgDispatch").GetComponent<UIImage>();
            UIImage head               = itemTransform.Find("goActive/qualityBg/headBg/mask/head").GetComponent<UIImage>();
            Text txtSurvivalName       = itemTransform.Find("goActive/txtSurvivalName").GetComponent<Text>();
            Text txtSurvivalType       = itemTransform.Find("goActive/txtSurvivalType").GetComponent<Text>();
            Text txtSurvivalNum        = itemTransform.Find("goActive/txtSurvivalNum").GetComponent<Text>();
            UIButton btnOpen           = itemTransform.Find("btnOpen").GetComponent<UIButton>();
            
            survivor_list mSurvivorListCfg = m_SurvivorListCfgList[dataIndex];
            SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById((uint)mSurvivorListCfg.id);
            bool isLock = survivorMoudle == null;
            
            string bgPath = GameEntry.LogicData.SurvivorData.GetQualityBg(mSurvivorListCfg.quality);
            qualityBg.SetImage(bgPath);

            string headQualityBg = GameEntry.LogicData.SurvivorData.GetHeadQualityBg(mSurvivorListCfg.quality);
            headBg.SetImage(headQualityBg);
            
            if (!string.IsNullOrEmpty(mSurvivorListCfg.icon))
            {
                head.SetImage(mSurvivorListCfg.icon,true);
            }

            txtSurvivalName.text = ToolScriptExtend.GetLang(mSurvivorListCfg.name);
            survivor_config survivorConfig = GameEntry.LDLTable.GetTableById<survivor_config>(mSurvivorListCfg.profession_id);
            string professionName = ToolScriptExtend.GetLang(survivorConfig.profession_name);
            txtSurvivalType.text = professionName;

            imgUnLock.gameObject.SetActive(isLock);
            txtSurvivalNum.gameObject.SetActive(isLock);
            long amountById = GameEntry.LogicData.BagData.GetAmountById(mSurvivorListCfg.peace);
            if (isLock)
            {
                txtSurvivalNum.text = $"{amountById}/10";
            }

            if (isLock)
            {
                imgUp.gameObject.SetActive(false);
                imgDispatch.gameObject.SetActive(false);
            }
            else
            {
                bool canUpStar = survivorMoudle.GetCanIncreaseStar();
                imgUp.gameObject.SetActive(canUpStar);
                imgDispatch.gameObject.SetActive(survivorMoudle.BuildingId != 0);
            }


            btnOpen.onClick.RemoveAllListeners();
            btnOpen.onClick.AddListener(() =>
            {
                if (isLock && amountById >= 10)
                {
                    GameEntry.LogicData.SurvivorData.SurvivorSummonReq((uint)mSurvivorListCfg.id);
                    return;
                }
                GameEntry.UI.OpenUIForm(EnumUIForm.UISurvivorUpStarGradeForm, (uint)mSurvivorListCfg.id);
            });
        }

        private void RefreshFilterInfo()
        {
            Transform childDispatch1 = m_btnDispatch1.transform.GetChild(0);
            Transform childDispatch2 = m_btnDispatch2.transform.GetChild(0);
            Transform childDispatch3 = m_btnDispatch3.transform.GetChild(0);
            
            Transform childActive1 = m_btnActive1.transform.GetChild(0);
            Transform childActive2 = m_btnActive2.transform.GetChild(0);
            Transform childActive3 = m_btnActive3.transform.GetChild(0);
            
            Transform childUpStar1 = m_btnUpStar1.transform.GetChild(0);
            Transform childUpStar2 = m_btnUpStar2.transform.GetChild(0);
            
            childDispatch1.gameObject.SetActive(m_FliterDispatch == FilterDispatch.All);
            childDispatch2.gameObject.SetActive(m_FliterDispatch == FilterDispatch.IsDispatch);
            childDispatch3.gameObject.SetActive(m_FliterDispatch == FilterDispatch.NotDispatch);
            
            childActive1.gameObject.SetActive(m_FliterAcive == FilterAcive.All);
            childActive2.gameObject.SetActive(m_FliterAcive == FilterAcive.IsActive);
            childActive3.gameObject.SetActive(m_FliterAcive == FilterAcive.NotActive);
            
            childUpStar1.gameObject.SetActive(m_FliterUpStar == FilterUpStar.All);
            childUpStar2.gameObject.SetActive(m_FliterUpStar == FilterUpStar.CanUp);

        }

        private void RefreshFilterGroup()
        {
            m_btnMaskFilter.gameObject.SetActive(m_IsOpenFilter);
            m_goFilterRoot.gameObject.SetActive(m_IsOpenFilter);
        }

        #endregion

        #region 派遣部分

        // 刷新派遣界面
        private void RefreshDeploy()
        {
            List<buildtype> allDispatchBuilding = GameEntry.LogicData.BuildingData.GetAllDispatchBuilding();
            for (int i = 0; i < allDispatchBuilding.Count; i++)
            {
                GameObject itemObj;
                if (m_AllDispatchBuildListRect.transform.childCount <= i)
                {
                    itemObj = Instantiate(m_goDispatchItem,m_AllDispatchBuildListRect.transform);
                    itemObj.SetActive(true);
                }
                else
                {
                    itemObj = m_AllDispatchBuildListRect.transform.GetChild(i).gameObject;
                }

                UpdateAllDispatchList(i, itemObj, allDispatchBuilding[i]);
            }
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_AllDispatchBuildListRect);
        }

        private void UpdateAllDispatchList(int index, GameObject itemObj, buildtype buildType)
        {
            Transform itemTransform = itemObj.transform;
            RectTransform itemRect = itemObj.GetComponent<RectTransform>();
            Transform goTop = itemTransform.Find("goTop");
            UIText txtBuildingName = itemTransform.Find("goTop/txtBuildingName").GetComponent<UIText>();
            UIText txtDiaspatchNum = itemTransform.Find("goTop/txtDiaspatchNum").GetComponent<UIText>();
            Transform goBottom = itemTransform.Find("goBottom");
            RectTransform goBottomItemRect = goBottom.GetComponent<RectTransform>();
            UIImage imgPackUp = itemTransform.Find("goTop/bnPackUp").GetComponent<UIImage>();
            UIButton bnPackUp = itemTransform.Find("goTop/bnPackUp").GetComponent<UIButton>();
            UIImage imgPackUpRed = itemTransform.Find("goTop/bnPackUp/imgPackUpRed").GetComponent<UIImage>();
            
            txtBuildingName.text = GameEntry.LogicData.BuildingData.GetBuildingNameByBuildType(buildType);
            
            List<BuildingModule> list = GameEntry.LogicData.BuildingData.GetAllDispatchBuildingModuleListByType(buildType);
            int survivorListCount = 0;
            int limitMax = list.Count > 0 ? list.Count * 4 : 4;
            for (int i = 0; i < list.Count; i++)
            {
                BuildingModule buildingModule = list[i];
                GameObject itemGameObject;
                if (goBottom.childCount <= i)
                {
                    itemGameObject = Instantiate(m_goSurvivorBuildingLine,goBottom);
                    itemGameObject.SetActive(true);
                }
                else
                {
                    itemGameObject = goBottom.GetChild(i).gameObject;
                }

                survivorListCount += buildingModule.SurvivorList.Count;
                bool isShowLine = i != list.Count - 1;
                UpdateAllDispatchBuildingList(i, itemGameObject, buildingModule, isShowLine);
            }
            bool isShowRed = false;
            for (int i = 0; i < list.Count; i++)
            {
                BuildingModule buildingModule = list[i];
                
                bool canDispatch = buildingModule.CheckSurvivorDispatch();
                bool canUpStar = buildingModule.GetSurvivorIsHaveDispatchUpStar();
                if (canUpStar || canDispatch)
                {
                    isShowRed = true;
                    break;
                }
            }
            imgPackUpRed.gameObject.SetActive(isShowRed);
            LayoutRebuilder.ForceRebuildLayoutImmediate(goBottomItemRect);
            txtDiaspatchNum.text = ToolScriptExtend.GetLangFormat(1160,survivorListCount.ToString(),limitMax.ToString());
            
            bool isActiveSelf = true;
            Action<bool> getPackUpPath = (isActive) =>
            {
                string packUpPath = "Sprite/ui_public/button_small2_jiantou2.png";
                if (!isActive)
                {
                    packUpPath = "Sprite/ui_public/button_small2_jiantou1.png";
                }
                imgPackUp.SetImage(packUpPath);
            };
            getPackUpPath.Invoke(isActiveSelf);

            bnPackUp.onClick.RemoveAllListeners();
            bnPackUp.onClick.AddListener(() =>
            {
                isActiveSelf = !isActiveSelf;
                goBottom.gameObject.SetActive(isActiveSelf);
                getPackUpPath.Invoke(isActiveSelf);
                LayoutRebuilder.ForceRebuildLayoutImmediate(itemRect);
                LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewDispatchList.content);
            });
        }

        private void UpdateAllDispatchBuildingList(int index, GameObject itemObj, BuildingModule dispatchBuilding, bool isShow)
        {
            Transform itemTransform = itemObj.transform;
            
            Transform transSurvivorList = itemTransform.Find("transSurvivorList");
            UIImage imgBuildingIcon = itemTransform.Find("imgBuildingIcon").GetComponent<UIImage>();
            UIText txtBuildingLevel = itemTransform.Find("txtBuildingLevel").GetComponent<UIText>();
            UIImage line = itemTransform.Find("line").GetComponent<UIImage>();
            UIButton btnCheck = itemTransform.Find("btnCheck").GetComponent<UIButton>();
            UIImage imgCheckRed = itemTransform.Find("btnCheck/imgCheckRed").GetComponent<UIImage>();

            line.gameObject.SetActive(isShow);
            List<uint> survivorList = dispatchBuilding.SurvivorList;
            txtBuildingLevel.text = $"Lv.{dispatchBuilding.LEVEL}";

            if (!string.IsNullOrEmpty(dispatchBuilding.BuildingIcon))
            {
                imgBuildingIcon.SetImage(dispatchBuilding.BuildingIcon);
            }
            
            for (int i = 0; i < 4; i++)
            {
                GameObject survivorItemObj;
                if (transSurvivorList.childCount <= i)
                {
                    survivorItemObj = Instantiate(m_goDispatchSubItem,transSurvivorList);
                    survivorItemObj.SetActive(true);
                }
                else
                {
                    survivorItemObj = transSurvivorList.GetChild(i).gameObject;
                }

                uint survivorId = 0;
                if (i < survivorList.Count)
                {
                    survivorId = survivorList[i];
                }

                UpdateDispatchSurvivorItem(i, survivorItemObj, survivorId);
            }

            bool canDispatch = dispatchBuilding.CheckSurvivorDispatch();
            imgCheckRed.gameObject.SetActive(canDispatch);
            
            btnCheck.onClick.RemoveAllListeners();
            btnCheck.onClick.AddListener(() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingDispatchForm,dispatchBuilding);
            });
        }

        private void UpdateDispatchSurvivorItem(int index, GameObject itemObj, uint survivorId)
        {
            Transform itemTransform    = itemObj.transform;
            Transform goAdd            = itemTransform.Find("goAdd");
            Transform goActive         = itemTransform.Find("goActive");
            UIImage qualityBg          = itemTransform.Find("goActive/qualityBg").GetComponent<UIImage>();
            UIImage headBg             = itemTransform.Find("goActive/qualityBg/headBg").GetComponent<UIImage>();
            UIImage head               = itemTransform.Find("goActive/qualityBg/headBg/mask/head").GetComponent<UIImage>();
            Text txtSurvivalName       = itemTransform.Find("goActive/txtSurvivalName").GetComponent<Text>();
            UIButton btnOpenTip        = itemTransform.Find("btnOpenTip").GetComponent<UIButton>();
            UIImage imgDispatchRed     = itemTransform.Find("imgDispatchRed").GetComponent<UIImage>();
            UIImage imgUp              = itemTransform.Find("goActive/imgUp").GetComponent<UIImage>();

            goAdd.gameObject.SetActive(survivorId == 0);
            goActive.gameObject.SetActive(survivorId != 0);
            if (survivorId != 0)
            {
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(survivorId);
                if (survivorMoudle != null)
                {
                    string bgPath = GameEntry.LogicData.SurvivorData.GetQualityBg(survivorMoudle.Quality);
                    qualityBg.SetImage(bgPath);

                    string headQualityBg = GameEntry.LogicData.SurvivorData.GetHeadQualityBg(survivorMoudle.Quality);
                    headBg.SetImage(headQualityBg);

                    txtSurvivalName.text = survivorMoudle.Name;

                    if (!string.IsNullOrEmpty(survivorMoudle.HeadIcon))
                    {
                        head.SetImage(survivorMoudle.HeadIcon,true);
                    }

                    bool canIncreaseStar = survivorMoudle.GetCanIncreaseStar();
                    imgUp.gameObject.SetActive(canIncreaseStar);
                }
                btnOpenTip.onClick.RemoveAllListeners();
                btnOpenTip.onClick.AddListener(() =>
                {
                    // todo 打开单个幸存者详情
                    GameEntry.UI.OpenUIForm(EnumUIForm.UISurvivorUpStarGradeForm, survivorId);
                });
            }
        }

        #endregion

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnMaskClick()
        {

        }

        private void OnBtnQuickDispatchClick()
        {
            if (!m_IsClickQuickDispatch)
            {
                m_IsClickQuickDispatch = true;
                GameEntry.LogicData.SurvivorData.SurvivorFastDispatchReq(0,() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1100525)
                    });
                });
            }
        }

        private void OnBtnDispatch1Click()
        {
            if (m_FliterDispatch == FilterDispatch.All)
            {
                return;
            }
            m_FliterDispatch = FilterDispatch.All;
            ResetAllGroup();
        }
        private void OnBtnDispatch2Click()
        {
            if (m_FliterDispatch == FilterDispatch.IsDispatch)
            {
                return;
            }
            m_FliterDispatch = FilterDispatch.IsDispatch;
            ResetAllGroup();
        }
        private void OnBtnDispatch3Click()
        {
            if (m_FliterDispatch == FilterDispatch.NotDispatch)
            {
                return;
            }
            m_FliterDispatch = FilterDispatch.NotDispatch;
            ResetAllGroup();
        }
        private void OnBtnActive1Click()
        {
            if (m_FliterAcive == FilterAcive.All)
            {
                return;
            }
            m_FliterAcive = FilterAcive.All;
            ResetAllGroup();
        }
        private void OnBtnActive2Click()
        {
            if (m_FliterAcive == FilterAcive.IsActive)
            {
                return;
            }
            m_FliterAcive = FilterAcive.IsActive;
            ResetAllGroup();
        }
        private void OnBtnActive3Click()
        {
            if (m_FliterAcive == FilterAcive.NotActive)
            {
                return;
            }
            m_FliterAcive = FilterAcive.NotActive;
            ResetAllGroup();
        }
        private void OnBtnUpStar1Click()
        {
            if (m_FliterUpStar == FilterUpStar.All)
            {
                return;
            }
            m_FliterUpStar = FilterUpStar.All;
            ResetAllGroup();
        }
        private void OnBtnUpStar2Click()
        {
            if (m_FliterUpStar == FilterUpStar.CanUp)
            {
                return;
            }
            m_FliterUpStar = FilterUpStar.CanUp;
            ResetAllGroup();
        }

        private void OnBtnOpenFilterClick()
        {
            m_IsOpenFilter = !m_IsOpenFilter;
            RefreshFilterGroup();
        }

        private void OnBtnMaskFilterClick()
        {
            m_IsOpenFilter = !m_IsOpenFilter;
            RefreshFilterGroup();
        }

        private void OnBtnSurvivorShopClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIRecruitForm,recruittype.recruittype_survivor);
        }
    }
}

using UnityEngine;

namespace Game.Hotfix
{
    public enum SceneDefine
    {
        MenuScene = 1,
        MainScene = 2,
        Battle5v5Scene = 3,
        WorldMapScene = 4,
    }

    public enum WorldMapLOD
    {
        LevelNone = 0,
        Level1 = 1,
        Level2 = 2,
        Level3 = 3,
        Level4 = 4,
        Level5 = 5,
        Level6 = 6,
    }

    public enum WorldMapElementType
    {
        /// <summary>
        /// 收集
        /// </summary>
        Gather = 1,
        /// <summary>
        /// 僵尸
        /// </summary>
        Zombies = 2,
        /// <summary>
        /// 末日精英
        /// </summary>
        DoomElite = 3
    }

    public enum WorldMapGatherType
    {
        /// <summary>
        /// 铁矿
        /// </summary>
        Iron = 1,
        /// <summary>
        /// 农田
        /// </summary>
        Food = 2,
        /// <summary>
        /// 金矿
        /// </summary>
        Gold = 3
    }
    
    public static class GameDefine
    {
        public static string EmptySceneName = "Empty";

        /// <summary>
        /// PvePath路面高度
        /// </summary>
        public const float PvePathRoadHeight = 0.2f;

        #region 沙盘相关

        public static readonly Vector3 HalfOffset = new Vector3(0.5f, 0, 0.5f);

        /// <summary>
        /// Troop 坐标 缩放值 坐标统一除以5
        /// </summary>
        public static float TroopPathScale = 5;

        /// <summary>
        /// 城市占用尺寸
        /// </summary>
        public static readonly Vector2Int WorldMapCitySize = new Vector2Int(5, 5);

        /// <summary>
        /// 玩家城镇占用尺寸
        /// </summary>
        public static readonly Vector2Int WorldMapTownSize = new Vector2Int(3, 3);

        /// <summary>
        /// 城市在沙盘中检索区块大小
        /// </summary>
        public static readonly Vector2Int WorldMapDataGridSize = new Vector2Int(40, 40);

        public static readonly Vector2Int WorldMapDataGridNum =
            new Vector2Int(1000 / WorldMapDataGridSize.x, 1000 / WorldMapDataGridSize.y);
        
        /// <summary>
        /// 摄像机默认距离
        /// </summary>
        public const int WorldMapCameraDefaultDistance = 52;

        /// <summary>
        /// 沙盘grid点击地块素材
        /// </summary>
        public const string WorldMapClickGridRes = "Assets/ResPackage/WorldMap/Prefab/WroldMapClickGrid.prefab";


        #endregion

    }
    
    
    
}
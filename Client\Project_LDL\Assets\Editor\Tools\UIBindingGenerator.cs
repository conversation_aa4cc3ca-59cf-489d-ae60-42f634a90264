using System.IO;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEditor.SceneManagement;

public class UIBindingGenerator : EditorWindow
{
    #region 变量

    GameObject targetPrefab;          // 目标预制体
    string targetPath = "Assets/Scripts/HotUpdate/Game/UI/UIForm";  // 代码生成路径
    bool overwriteExisting = false;   // 是否覆盖已存在的脚本
    bool showNamingIssues = true;     // 是否显示命名规范问题
    Vector2 scrollPositionNameRules;  // 滚动条位置 命名规范
    Vector2 scrollPositionPreview;    // 滚动条位置 预览
    List<string> namingIssues = new List<string>();  // 命名规范问题列表

    GUIStyle buttonStyle;             // 按钮样式
    GUIStyle labelStyle;              // 文本样式
    GUIStyle commonLabelStyle;        // 通用文本样式

    static UIBindingGenerator window; // 窗口实例
    bool isShowAutoBindTip = false;   // 是否显示自动绑定提示

    // 命名规范定义
    readonly Dictionary<string, string> namingRules = new Dictionary<string, string>
    {
        { "GameObject", "m_go" },
        { "Transform", "m_trans" },
        { "RectTransform", "m_rect" },
        { "Image", "m_img" },
        { "Button", "m_btn" },
        { "Text", "m_txt" },
        { "ScrollRect", "m_scrollview" },
        { "Toggle", "m_tog" },
        { "InputField", "m_input" },
        { "Slider", "m_slider" },
        { "TableViewH", "m_TableViewH" },
        { "TableViewV", "m_TableViewV" },
        { "TableViewD", "m_TableViewD" },
        { "SkeletonAnimation", "m_spani" },
        { "SkeletonGraphic", "m_spui" },
        { "TextTMP", "m_txtTMP" },
    };

    // 组件类型映射
    readonly Dictionary<string, string> componentTypeMap = new Dictionary<string, string>
    {
        { "GameObject", "GameObject"},
        { "Transform", "Transform"},
        { "RectTransform", "RectTransform"},
        { "Image", "Image" },
        { "Button", "Button" },
        { "Text", "Text" },
        { "ScrollRect", "ScrollRect" },
        { "Toggle", "Toggle" },
        { "InputField", "InputField" },
        { "Slider", "Slider" },
        { "TableViewH", "Mosframe.TableView" },
        { "TableViewV", "Mosframe.TableView" },
        { "TableViewD", "Mosframe.CCTableViewController" },
        { "UIButton", "UIButton" },
        { "UIImage", "UIImage" },
        { "UIText", "UIText" },
        { "UIToggle", "UIToggle" },
        { "SkeletonAnimation", "Spine.Unity.SkeletonAnimation" },
        { "SkeletonGraphic", "Spine.Unity.SkeletonGraphic" },
        { "TextTMP", "TMPro.TMP_Text" },
    };

    // 扩展组件类型映射
    readonly Dictionary<string, string> extensionComponentTypeMap = new Dictionary<string, string>
    {
        { "Button", "UIButton" },
        { "Image", "UIImage" },
        { "Text", "UIText" },
        { "Toggle", "UIToggle" },
    };

    #endregion

    #region 窗口

    [MenuItem("Tools/UI/UI 绑定生成器")]
    public static void ShowWindow()
    {
        window = GetWindow<UIBindingGenerator>("UI 绑定生成器");
        window.InitGUIStyle();
    }

    void OnEnable()
    {
        PrefabStage.prefabSaved += OnPrefabSaved;

        // 显示自动绑定提示
        if (isShowAutoBindTip)
        {
            isShowAutoBindTip = false;
            // 编译完成后等待一帧再弹窗
            EditorApplication.delayCall += () =>
            {
                if (EditorUtility.DisplayDialog("温馨提示", 
                    "生成脚本成功！是否要自动绑定组件？", 
                    "好哇", "不用了谢谢"))
                {
                    AutoBindComponents(targetPrefab.name);
                }
            };
        }
    }

    void OnDisable() 
    {
        PrefabStage.prefabSaved -= OnPrefabSaved;
    }

    /// <summary>
    /// 预制体保存事件
    /// </summary>
    /// <param name="prefab">被保存的预制体</param>
    void OnPrefabSaved(GameObject prefab)
    {
        Debug.Log($"Prefab saved: {prefab.name}");

        if (targetPrefab != null)
        {
            // 重新加载 targetPrefab
            targetPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GetAssetPath(targetPrefab));
            
            // 重新验证命名规范
            ValidateNaming();
            
            // 刷新编辑器窗口
            Repaint();
        }
    }

    /// <summary>
    /// 初始化 GUI 样式
    /// </summary>
    void InitGUIStyle()
    {
        buttonStyle = new GUIStyle(EditorStyles.miniButton)
        {
            fontSize = 18,
            fixedHeight = 40,
            alignment = TextAnchor.MiddleCenter,
        };

        labelStyle = new GUIStyle(EditorStyles.label)
        {
            fontSize = 14,
            fixedWidth = 150,
        };

        commonLabelStyle = new GUIStyle(EditorStyles.label)
        {
            fontSize = 14,
        };
    }

    void OnGUI()
    {
        // 显示命名规范说明
        if (GUILayout.Button("查看命名规范", buttonStyle))
        {
            ShowNamingRules();
        }

        EditorGUILayout.Space(5);

        // 预制体选择
        EditorGUI.BeginChangeCheck();

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("选择预制体", labelStyle);
        targetPrefab = EditorGUILayout.ObjectField(targetPrefab, typeof(GameObject), false) as GameObject;
        EditorGUILayout.EndHorizontal();

        if (EditorGUI.EndChangeCheck())
        {
            ValidateNaming();
        }

        // 目标路径
        EditorGUILayout.BeginHorizontal();

        GUILayout.Label("代码生成路径", labelStyle);
        targetPath = EditorGUILayout.TextField(targetPath);
        
        if (GUILayout.Button("浏览", GUILayout.Width(60)))
        {
            string path = EditorUtility.OpenFolderPanel("Select Target Path", "Assets", "");
            if (!string.IsNullOrEmpty(path))
            {
                targetPath = path.Substring(path.IndexOf("Assets"));
            }
        }
        EditorGUILayout.EndHorizontal();

        // 覆盖选项
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("覆盖逻辑脚本", labelStyle);
        overwriteExisting = EditorGUILayout.Toggle(overwriteExisting);
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("显示命名规范问题", labelStyle);
        showNamingIssues = EditorGUILayout.Toggle(showNamingIssues);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(10);

        // 显示命名问题
        if (showNamingIssues && namingIssues.Count > 0)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            GUI.backgroundColor = new Color(1, 0.8f, 0.8f);
            EditorGUILayout.LabelField("命名规范问题:", EditorStyles.boldLabel);
            GUI.backgroundColor = Color.white;

            scrollPositionNameRules = EditorGUILayout.BeginScrollView(scrollPositionNameRules, GUILayout.MaxHeight(150));
            foreach (var issue in namingIssues)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("•", GUILayout.Width(15));
                EditorGUILayout.LabelField(issue, EditorStyles.wordWrappedLabel);
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndScrollView();

            EditorGUILayout.EndVertical();
        }
        else if (targetPrefab != null && namingIssues.Count == 0)
        {
            EditorGUILayout.HelpBox("没有发现命名规范问题", MessageType.Info);
        }

        // 生成按钮
        GUI.enabled = targetPrefab != null && !string.IsNullOrEmpty(targetPath);
        EditorGUILayout.Space(5);
        
        if (namingIssues.Count > 0)
        {
            GUI.backgroundColor = new Color(1, 0.8f, 0.8f);
            if (GUILayout.Button("生成（有命名问题）", buttonStyle))
            {
                if (EditorUtility.DisplayDialog("命名问题确认", 
                    "预制体中存在命名规范问题，是否继续生成？", 
                    "继续生成", "取消"))
                {
                    GenerateBinding();
                }
            }
            GUI.backgroundColor = Color.white;
        }
        else
        {
            GUI.backgroundColor = new Color(0.8f, 1, 0.8f);
            if (GUILayout.Button("生成", buttonStyle))
            {
                GenerateBinding();
            }
            GUI.backgroundColor = Color.white;
        }
        
        GUI.enabled = true;

        // 预览区域
        if (targetPrefab != null)
        {
            EditorGUILayout.Space(10);
            GUI.backgroundColor = new Color(0.8f, 1, 0.8f);
            if (GUILayout.Button("绑定", buttonStyle))
            {
                AutoBindComponents(targetPrefab.name);
            }
            GUI.backgroundColor = Color.white;
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("组件预览", labelStyle);
            
            scrollPositionPreview = EditorGUILayout.BeginScrollView(scrollPositionPreview);
            var components = GetAllUIComponents(targetPrefab.transform);
            foreach (var comp in components)
            {
                EditorGUILayout.LabelField($"- {comp.Item1}: {comp.Item2}", commonLabelStyle);
            }
            EditorGUILayout.EndScrollView();
        }
    }

    /// <summary>
    /// 显示命名规范窗口
    /// </summary>
    void ShowNamingRules()
    {
        NamingRulesWindow.ShowWindow(namingRules);
    }

    #endregion

    #region 生成代码

    /// <summary>
    /// 验证命名规范
    /// </summary>
    void ValidateNaming()
    {
        namingIssues.Clear();
        if (targetPrefab == null) return;

        var components = GetAllUIComponents(targetPrefab.transform);
        foreach (var comp in components)
        {
            string componentType = comp.Item1;
            string nodeName = comp.Item2;

            // 对于 Button 节点，允许不检查 Image 组件的命名规范
            if (componentType == "Image" && HasButtonComponent(targetPrefab.transform.Find(nodeName)))
            {
                continue;
            }

            if (namingRules.TryGetValue(componentType, out string expectedSuffix))
            {
                string lowercaseSuffix = char.ToLowerInvariant(expectedSuffix[0]) + expectedSuffix.Substring(1);
                bool isValid = nodeName.StartsWith(lowercaseSuffix, System.StringComparison.OrdinalIgnoreCase);

                if (!isValid)
                {
                    string expected = $"以 m_{char.ToLowerInvariant(expectedSuffix[0]) + expectedSuffix.Substring(1)} 开头";
                    namingIssues.Add($"{nodeName} -> 应{expected} (当前类型: {componentType})");
                }
            }

            // 检查是否包含空格
            if (nodeName.Contains(" "))
            {
                namingIssues.Add($"{nodeName} -> 不应包含空格");
            }

            // 检查是否包含特殊字符或括号
            if (Regex.IsMatch(nodeName, @"[^a-zA-Z0-9_]") || nodeName.Contains("(") || nodeName.Contains(")"))
            {
                namingIssues.Add($"{nodeName} -> 不应包含特殊字符");
            }

            // 检查是否以数字开头
            if (char.IsDigit(nodeName[0]))
            {
                namingIssues.Add($"{nodeName} -> 不应以数字开头");
            }
        }
    }

    /// <summary>
    /// 是否带有 Button 组件
    /// </summary>
    /// <param name="transform">节点</param>
    /// <returns>状态</returns>
    bool HasButtonComponent(Transform transform)
    {
        return transform != null && transform.GetComponent<Button>() != null;
    }

    /// <summary>
    /// 生成 UI 绑定代码
    /// </summary>
    void GenerateBinding()
    {
        if (targetPrefab == null) return;

        string prefabName = targetPrefab.name;

        // 生成的脚本路径和名称
        string componentsPath = Path.Combine(targetPath, $"{prefabName}Bind.cs");
        string logicPath = Path.Combine(targetPath, $"{prefabName}.cs");
        
        // 脚本已存在，询问是否覆盖
        if (File.Exists(componentsPath) && !overwriteExisting)
        {
            if (!EditorUtility.DisplayDialog("组件脚本已存在", 
                "是否要覆盖已存在的组件脚本？", 
                "好嘟", "不行"))
            {
                return;
            }
        }

        // 生成组件代码
        string componentsCode = GenerateComponentsCode(prefabName);
        File.WriteAllText(componentsPath, componentsCode);

        Debug.Log($"Generated components script: {componentsPath}");

        // 如果逻辑脚本不存在，则生成
        if (!File.Exists(logicPath) || overwriteExisting)
        {
            string logicCode = GenerateLogicCode(prefabName);
            File.WriteAllText(logicPath, logicCode);

            Debug.Log($"Generated logic script: {logicPath}");
        }

        AssetDatabase.Refresh();

        // 生成脚本后，弹出自动绑定提示弹窗
        isShowAutoBindTip = true;
    }

    /// <summary>
    /// 生成组件代码
    /// </summary>
    /// <param name="className">类名</param>
    /// <param name="baseName">基类名称</param>
    /// <returns>代码内容</returns>
    string GenerateComponentsCode(string className,string baseName = "UGuiFormEx")
    {
        var form = targetPrefab.GetComponent<Game.Hotfix.UGuiFormEx>();
        if (form)
        {
            var memberInfo = form.GetType().BaseType;
            if (memberInfo != null)
                baseName = memberInfo.Name;
        }

        var components = GetAllUIComponents(targetPrefab.transform);
        var groups = components.GroupBy(x => GetComponentCategory(x.Item1))
                               .OrderBy(x => GetCategoryOrder(x.Key));

        StringBuilder sb = new();
        
        // 添加 using 语句
        sb.AppendLine("using UnityEngine;");
        sb.AppendLine("using UnityEngine.UI;");
        sb.AppendLine();

        // 添加命名空间
        sb.AppendLine("namespace Game.Hotfix");
        sb.AppendLine("{");

        // 类定义
        sb.AppendLine($"    public partial class {className} : {baseName}");
        sb.AppendLine("    {");

        // 组件字段
        foreach (var group in groups)
        {
            if (!group.Any()) continue;

            foreach (var comp in group)
            {
                string fieldName = comp.Item2;
                string typeName = componentTypeMap[comp.Item1];
                sb.AppendLine($"        [SerializeField] private {typeName} {fieldName};");
            }
            sb.AppendLine();
        }

        // InitBind 方法
        sb.AppendLine("        void InitBind()");
        sb.AppendLine("        {");

        // 添加按钮点击事件注册
        var buttons = components.Where(x => x.Item1 == "Button" || x.Item1 == "UIButton");
        foreach (var button in buttons)
        {
            string fieldName = button.Item2;
            string handlerName = $"On{ToPascalCase(fieldName)}Click";
            sb.AppendLine($"            {fieldName}.onClick.AddListener({handlerName});");
        }
        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    /// <summary>
    /// 生成逻辑代码
    /// </summary>
    /// <param name="className">类名</param>
    /// <returns>代码内容</returns>
    string GenerateLogicCode(string className)
    {
        var components = GetAllUIComponents(targetPrefab.transform);
        StringBuilder sb = new StringBuilder();
        
        // 添加 using 语句
        sb.AppendLine("using System.Collections;");
        sb.AppendLine("using System.Collections.Generic;");
        sb.AppendLine("using UnityEngine;");
        sb.AppendLine("using UnityEngine.UI;");
        sb.AppendLine("using UnityGameFramework.Runtime;");
        sb.AppendLine();

        // 添加命名空间
        sb.AppendLine("namespace Game.Hotfix");
        sb.AppendLine("{");

        // 类定义
        sb.AppendLine($"    public partial class {className} : UGuiFormEx");
        sb.AppendLine("    {");

        // OnInit 方法
        sb.AppendLine("        protected override void OnInit(object userData)");
        sb.AppendLine("        {");
        sb.AppendLine("            base.OnInit(userData);");
        sb.AppendLine();
        sb.AppendLine("            InitBind();");
        sb.AppendLine("        }");
        sb.AppendLine();

        // OnOpen 方法
        sb.AppendLine("        protected override void OnOpen(object userData)");
        sb.AppendLine("        {");
        sb.AppendLine("            base.OnOpen(userData);");
        sb.AppendLine("        }");
        sb.AppendLine();

        // OnClose 方法
        sb.AppendLine("        protected override void OnClose(bool isShutdown, object userData)");
        sb.AppendLine("        {");
        sb.AppendLine("            base.OnClose(isShutdown, userData);");
        sb.AppendLine("        }");
        sb.AppendLine();

        // OnRefresh 方法
        sb.AppendLine("        public override void OnRefresh(object userData)");
        sb.AppendLine("        {");
        sb.AppendLine("            base.OnRefresh(userData);");
        sb.AppendLine("        }");
        sb.AppendLine();

        // 添加按钮事件处理方法
        var buttons = components.Where(x => x.Item1 == "Button" || x.Item1 == "UIButton");
        foreach (var button in buttons)
        {
            string fieldName = button.Item2;
            string handlerName = $"On{ToPascalCase(fieldName)}Click";
            sb.AppendLine($"        private void {handlerName}()");
            sb.AppendLine("        {");
            sb.AppendLine();
            sb.AppendLine("        }");
            sb.AppendLine();
        }

        sb.Remove(sb.Length - 2, 2); // 移除最后一个空行

        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    /// <summary>
    /// 自动绑定组件
    /// </summary>
    /// <param name="className">类名</param>
    void AutoBindComponents(string className)
    {
        // 获取预制体路径
        string prefabPath = AssetDatabase.GetAssetPath(targetPrefab);
        
        // 加载预制体
        GameObject prefabInstance = PrefabUtility.LoadPrefabContents(prefabPath);
        
        try
        {
            // 获取或添加脚本组件
            var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
            System.Type scriptType = null;
            foreach (var assembly in assemblies)
            {
                scriptType = assembly.GetType($"Game.Hotfix.{className}");
                if (scriptType != null)
                {
                    Debug.Log($"Found script type: {scriptType.FullName}");
                    break;
                }
            }
            if (scriptType == null)
            {
                Debug.LogWarning($"Cannot find script type: {className}");
                return;
            }

            var scriptComponent = prefabInstance.GetComponent(scriptType) ?? 
                                prefabInstance.AddComponent(scriptType);

            // 获取所有需要绑定的组件
            var components = GetAllUIComponents(prefabInstance.transform);
            
            // 遍历所有组件并自动绑定
            foreach (var comp in components)
            {
                string fieldName = comp.Item2;
                Debug.Log($"Looking for field: {fieldName} for component {comp.Item2} ({comp.Item1})");

                // 对于GameObject，直接查找Transform
                var field = scriptType.GetField(fieldName, 
                    System.Reflection.BindingFlags.NonPublic | 
                    System.Reflection.BindingFlags.Instance);

                if (field != null)
                {
                    Transform targetTransform = FindChildRecursively(prefabInstance.transform, comp.Item2);
                    if (targetTransform != null)
                    {

                        if (comp.Item1 == "GameObject")
                        {
                            field.SetValue(scriptComponent, targetTransform.gameObject);
                            Debug.Log($"Successfully bound GameObject {field.Name} to {comp.Item2}");
                            continue;
                        }

                        if (comp.Item1 == "Transform")
                        {
                            field.SetValue(scriptComponent, targetTransform);
                            Debug.Log($"Successfully bound Transform {field.Name} to {comp.Item2}");
                            continue;
                        }

                        var component = targetTransform.GetComponent(componentTypeMap[comp.Item1]);
                        if (component != null)
                        {
                            field.SetValue(scriptComponent, component);
                            Debug.Log($"Successfully bound {field.Name} to {comp.Item2}");
                        }
                        else
                        {
                            Debug.LogWarning($"Component {componentTypeMap[comp.Item1]} not found on {comp.Item2}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Transform not found: {comp.Item2}");
                    }
                }
                else
                {
                    Debug.LogWarning($"Field not found: {fieldName} in {className}");
                }
            }

            // 保存修改后的预制体
            PrefabUtility.SaveAsPrefabAsset(prefabInstance, prefabPath);
            Debug.Log($"Saved prefab: {prefabPath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error while binding components: {e}");
        }
        finally
        {
            PrefabUtility.UnloadPrefabContents(prefabInstance);
        }
    }

    /// <summary>
    /// 获取所有 UI 组件
    /// </summary>
    /// <param name="root">根节点</param>
    /// <returns>组件列表</returns>
    List<(string, string)> GetAllUIComponents(Transform root)
    {
        var components = new List<(string, string)>();
        GetComponentsRecursive(root, "", components);
        return components.Where(x => IsValidComponentName(x.Item2, x.Item1)).ToList();
    }

    /// <summary>
    /// 递归获取所有组件
    /// </summary>
    /// <param name="transform">节点</param>
    /// <param name="path">路径</param>
    /// <param name="components">组件</param>
    void GetComponentsRecursive(Transform transform, string path, List<(string, string)> components)
    {
        string currentPath = string.IsNullOrEmpty(path) ? transform.name : $"{path}/{transform.name}";

        // 获取第一个匹配的组件
        string foundType = null;
        foreach (var rule in namingRules)
        {
            string prefix = rule.Value.ToLower();
            if (transform.name.StartsWith(prefix, System.StringComparison.OrdinalIgnoreCase))
            {
                foundType = rule.Key;
                if (foundType == "GameObject" || foundType == "Transform")
                {
                    break;
                }

                if (extensionComponentTypeMap.ContainsKey(foundType))
                {
                    var extensionComponent = transform.GetComponent(extensionComponentTypeMap[foundType]);
                    if (extensionComponent != null)
                    {
                        foundType = extensionComponentTypeMap[foundType];
                        break;
                    }
                }

                // 检查组件是否存在
                var component = transform.GetComponent(componentTypeMap[foundType]);
                if (component != null)
                {
                    break;
                }
            }
        }

        if (foundType != null)
        {
            // 忽略 ScrollRect 的 Viewport 节点和多语言的文本
            if (transform.name != "Viewport" && transform.name != "Content"
            && !transform.name.StartsWith("m_txtAuto", System.StringComparison.OrdinalIgnoreCase))
            {
                components.Add((foundType, transform.name));
            }
        }

        // 递归处理子节点
        foreach (Transform child in transform)
        {
            GetComponentsRecursive(child, currentPath, components);
        }
    }

    /// <summary>
    /// 递归查找子节点
    /// </summary>
    /// <param name="parent">父节点</param>
    /// <param name="childName">子节点名称</param>
    /// <returns>子节点</returns>
    Transform FindChildRecursively(Transform parent, string childName)
    {
        if (parent.name == childName)
            return parent;

        for (int i = 0; i < parent.childCount; i++)
        {
            var result = FindChildRecursively(parent.GetChild(i), childName);
            if (result != null)
                return result;
        }

        return null;
    }

    /// <summary>
    /// 转换为帕斯卡命名法
    /// </summary>
    /// <param name="name">节点名称</param>
    /// <returns>转换名称</returns>
    string ToPascalCase(string name)
    {
        if (string.IsNullOrEmpty(name))
            return name;

        // 移除 m_ 前缀
        if (name.StartsWith("m_"))
            name = name.Substring(2);

        // 确保第一个字符大写
        return char.ToUpperInvariant(name[0]) + name.Substring(1);
    }

    bool IsValidComponentName(string nodeName, string componentType)
    {
        string curComponentType = componentType;
        if (extensionComponentTypeMap.ContainsValue(curComponentType))
        {
            curComponentType = extensionComponentTypeMap.FirstOrDefault(x => x.Value == curComponentType).Key;
        }

        if (namingRules.TryGetValue(curComponentType, out string expectedSuffix))
        {
            string lowercaseSuffix = char.ToLowerInvariant(expectedSuffix[0]) + expectedSuffix.Substring(1);
            return nodeName.StartsWith(lowercaseSuffix, System.StringComparison.OrdinalIgnoreCase);
        }
        return false;
    }

    /// <summary>
    /// 获取组件类别
    /// </summary>
    /// <param name="componentType">类型</param>
    /// <returns>类型字符串</returns>
    string GetComponentCategory(string componentType)
    {
        switch (componentType)
        {
            case "Button":
            case "UIButton":
                return "Buttons";
            case "Text":
            case "UIText":
            case "TMP_Text":
                return "Labels";
            case "InputField":
            case "TMP_InputField":
                return "Input Fields";
            case "Image":
            case "UIImage":
            case "RawImage":
                return "Images";
            case "ScrollRect":
                return "ScrollView";
            case "Toggle":
            case "UIToggle":
            case "Slider":
            case "Dropdown":
                return "Controls";
            case "ParticleSystem":
                return "Effects";
            case "Animator":
                return "Animations";
            default:
                return "Other";
        }
    }

    /// <summary>
    /// 获取类别顺序
    /// </summary>
    /// <param name="category">类别</param>
    /// <returns>顺序</returns>
    int GetCategoryOrder(string category)
    {
        switch (category)
        {
            case "Buttons": return 0;
            case "Labels": return 1;
            case "Images": return 2;
            case "ScrollView": return 3;
            case "Controls": return 4;
            case "Input Fields": return 5;
            case "Effects": return 6;
            case "Animations": return 7;
            default: return 99;
        }
    }

    #endregion

    #region 命名规范窗口

    // 添加新的命名规范窗口类
    public class NamingRulesWindow : EditorWindow
    {
        Dictionary<string, string> namingRules;
        Vector2 scrollPosition;
        GUIStyle titleStyle;
        GUIStyle headerStyle;
        GUIStyle contentStyle;
        GUIStyle ruleStyle;
        Texture2D bgTexture;
        Color accentColor = new Color(1f, 0.75f, 0.79f);
        int selectedTab = 0;

        public static void ShowWindow(Dictionary<string, string> rules)
        {
            var window = GetWindow<NamingRulesWindow>("UI命名规范");
            window.namingRules = rules;
            window.minSize = new Vector2(500, 600);
            window.maxSize = new Vector2(800, 1000);
            window.InitializeStyles();
            window.ShowUtility();
        }

        void InitializeStyles()
        {
            // 创建背景纹理
            bgTexture = new Texture2D(1, 1);
            bgTexture.SetPixel(0, 0, new Color(0.2f, 0.2f, 0.2f));
            bgTexture.Apply();

            // 标题样式
            titleStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(0, 0, 10, 10),
                normal = { textColor = accentColor }
            };

            // 头部样式
            headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                margin = new RectOffset(0, 0, 10, 5),
                normal = { textColor = accentColor }
            };

            // 内容样式
            contentStyle = new GUIStyle(EditorStyles.label)
            {
                fontSize = 14,
                wordWrap = true,
                margin = new RectOffset(20, 20, 5, 5),
                normal = { textColor = Color.white }
            };

            // 规则样式
            ruleStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(10, 10, 10, 10),
                margin = new RectOffset(20, 20, 5, 5)
            };
        }

        void OnGUI()
        {
            // 绘制背景
            GUI.DrawTexture(new Rect(0, 0, position.width, position.height), bgTexture, ScaleMode.StretchToFill);

            // 标题
            GUILayout.Space(10);
            EditorGUILayout.LabelField("UI组件命名规范", titleStyle);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            switch (selectedTab)
            {
                case 0:
                    DrawMPrefixNamingRules();
                    break;
            }

            EditorGUILayout.EndScrollView();

            // 底部关闭按钮
            GUILayout.FlexibleSpace();
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            if (GUILayout.Button("关闭", GUILayout.Width(100), GUILayout.Height(30)))
            {
                Close();
            }
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
            GUILayout.Space(10);
        }

        void DrawMPrefixNamingRules()
        {
            // 基本规则
            EditorGUILayout.LabelField("1. m_前缀命名规则", headerStyle);
            EditorGUILayout.BeginVertical(ruleStyle);
            EditorGUILayout.LabelField("• 命名格式：m_ + 组件类型前缀 + 功能描述", contentStyle);
            EditorGUILayout.LabelField("• 前缀小写，功能描述首字母大写", contentStyle);
            EditorGUILayout.LabelField("• 用于明确标识成员变量", contentStyle);
            EditorGUILayout.EndVertical();

            // 示例
            GUILayout.Space(10);
            EditorGUILayout.LabelField("2. 命名示例", headerStyle);
            EditorGUILayout.BeginVertical(ruleStyle);
            EditorGUILayout.LabelField("• 登录按钮：m_btnLogin", contentStyle);
            EditorGUILayout.LabelField("• 用户名输入框：m_inputUserName", contentStyle);
            EditorGUILayout.LabelField("• 头像图片：m_imgAvatar", contentStyle);
            EditorGUILayout.LabelField("• 分数文本：m_txtScore", contentStyle);
            EditorGUILayout.EndVertical();

            DrawCommonRules();
        }

        void DrawCommonRules()
        {
            // 组件类型规则
            GUILayout.Space(10);
            EditorGUILayout.LabelField("3. 组件类型对照表", headerStyle);
            EditorGUILayout.BeginVertical(ruleStyle);
            foreach (var rule in namingRules)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"• {rule.Key}", contentStyle, GUILayout.Width(150));
                GUI.color = accentColor;
                EditorGUILayout.LabelField(rule.Value, contentStyle);
                GUI.color = Color.white;
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndVertical();

            // 注意事项
            GUILayout.Space(10);
            EditorGUILayout.LabelField("4. 注意事项", headerStyle);
            EditorGUILayout.BeginVertical(ruleStyle);
            EditorGUILayout.LabelField("• 避免使用特殊字符和空格", contentStyle);
            EditorGUILayout.LabelField("• 不要以数字开头", contentStyle);
            EditorGUILayout.LabelField("• 保持命名的一致性", contentStyle);
            EditorGUILayout.LabelField("• 避免使用无意义的名称（如 Btn1, Img2 等）", contentStyle);
            EditorGUILayout.EndVertical();
        }
    }

    #endregion
}
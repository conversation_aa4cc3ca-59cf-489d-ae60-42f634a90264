using UnityEngine;
using DG.Tweening;
using System;
using GameFramework.Event;

namespace Game.Hotfix
{
    public class ED_Train : EntityData
    {
        public ED_Train(int entityId) : base(entityId)
        {

        }
    }

    public class EL_Train : Entity
    {
        private Transform m_StartPoint;
        private Transform m_StayPoint;
        private Transform m_EndPoint;
        private Tween m_CurrentTween;
        private uint? uid = null;
        private TrainStatus curTrainStatus;
        private GameObject normal;
        private GameObject gold;

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);

            gameObject.SetActive(false);

            curTrainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            ColorLog.Green("curTrainStatus", curTrainStatus);

            normal = transform.Find("huoche").gameObject;
            gold = transform.Find("huoche_gold").gameObject;

            bool isGold = GameEntry.TradeTruckData.IsTrainGold();
            normal.SetActive(!isGold);
            gold.SetActive(isGold);

            GameEntry.Event.Subscribe(TrainRefreshGoodsEventArgs.EventId, OnTrainRefreshGoodsEventArgs);
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
            GameEntry.Event.Unsubscribe(TrainRefreshGoodsEventArgs.EventId, OnTrainRefreshGoodsEventArgs);

            // 停止所有动画
            StopAllAnimations();
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            if (curTrainStatus < TrainStatus.Prepare)
            {
                curTrainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            }

            if (curTrainStatus == TrainStatus.Prepare && trainStatus == TrainStatus.Depart)
            {
                PlayExitStationAnimation();
                curTrainStatus = TrainStatus.Depart;
            }
        }

        private void OnTrainRefreshGoodsEventArgs(object sender, GameEventArgs e)
        {
            bool isGold = GameEntry.TradeTruckData.IsTrainGold();
            normal.SetActive(!isGold);
            gold.SetActive(isGold);
        }

        /// <summary>
        /// 初始化火车站点位置
        /// </summary>
        /// <param name="startPoint">起始点</param>
        /// <param name="stayPoint">停靠点</param>
        /// <param name="endPoint">终点</param>
        public void InitializeStationPoints(Transform startPoint, Transform stayPoint, Transform endPoint)
        {
            m_StartPoint = startPoint;
            m_StayPoint = stayPoint;
            m_EndPoint = endPoint;

            ColorLog.Green("火车站点初始化完成");

            // 站点初始化完成后，重新设置火车位置并创建HUD
            if (GameEntry.TradeTruckData.myTrain != null)
            {
                if (curTrainStatus == TrainStatus.LineUp || curTrainStatus == TrainStatus.Prepare)
                {
                    TeleportToStation(TrainStationType.Stay);

                    // 现在创建HUD，此时火车位置已正确设置
                    uid = GameEntry.HUD.ShowHUD(this, EnumHUD.HUDTrainPassenger);
                }
            }
        }

        /// <summary>
        /// 进站动画：从起始点移动到停靠点
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">动画完成回调</param>
        public void PlayEnterStationAnimation(float duration = 2f, Action onComplete = null)
        {
            if (m_StartPoint == null || m_StayPoint == null)
            {
                ColorLog.Red("火车站点未初始化，无法播放进站动画");
                onComplete?.Invoke();
                return;
            }

            // 停止当前动画
            StopCurrentAnimation();

            // 设置起始位置
            transform.position = m_StartPoint.position;

            if (GameEntry.TradeTruckData.IsUnlockTrade())
            {
                gameObject.SetActive(true);
            }

            if (uid != null) GameEntry.HUD.HideHUD(uid.Value);

            // 使用简单的缓动函数：慢启动，快结束（模拟火车进站减速）
            m_CurrentTween = transform.DOMove(m_StayPoint.position, duration)
                .SetEase(Ease.OutCubic)
                .OnComplete(() =>
                {
                    onComplete?.Invoke();
                    uid = GameEntry.HUD.ShowHUD(this, EnumHUD.HUDTrainPassenger);
                    curTrainStatus = GameEntry.TradeTruckData.GetTrainStatus();
                    ColorLog.Green("火车进站动画完成");
                    ColorLog.Green("更新当前火车状态", curTrainStatus);
                    if (GameEntry.TradeTruckData.myTrain != null)
                    {
                        ColorLog.Green("ArrivalTime", GameEntry.TradeTruckData.myTrain.ArrivalTime);
                        if (GameEntry.TradeTruckData.myTrain.Train != null)
                        {
                            ColorLog.Green("PrepareTime", GameEntry.TradeTruckData.myTrain.Train.PrepareTime);
                        }
                    }
                });
            ColorLog.Green("开始播放火车进站动画");
        }

        /// <summary>
        /// 出站动画：从停靠点移动到终点
        /// </summary>
        /// <param name="duration">动画时长</param>
        /// <param name="onComplete">动画完成回调</param>
        public void PlayExitStationAnimation(float duration = 2f, Action onComplete = null)
        {
            if (m_StayPoint == null || m_EndPoint == null)
            {
                ColorLog.Red("火车站点未初始化，无法播放出站动画");
                onComplete?.Invoke();
                return;
            }

            // 停止当前动画
            StopCurrentAnimation();

            // 确保在停靠点位置
            transform.position = m_StayPoint.position;
            if (uid != null) GameEntry.HUD.HideHUD(uid.Value);

            // 使用简单的缓动函数：慢启动，快结束（模拟火车出站加速）
            m_CurrentTween = transform.DOMove(m_EndPoint.position, duration)
                .SetEase(Ease.InCubic)
                .OnComplete(() =>
                {
                    ColorLog.Green("火车出站动画完成");
                    onComplete?.Invoke();
                    gameObject.SetActive(false);
                    GameEntry.Event.Fire(TrainContractEventArgs.EventId, TrainContractEventArgs.Create(true));
                });
            ColorLog.Green("开始播放火车出站动画");
        }

        /// <summary>
        /// 停止当前动画
        /// </summary>
        public void StopCurrentAnimation()
        {
            if (m_CurrentTween != null && m_CurrentTween.IsActive())
            {
                m_CurrentTween.Kill();
                m_CurrentTween = null;
                ColorLog.Yellow("火车动画已停止");
            }
        }

        /// <summary>
        /// 停止所有动画
        /// </summary>
        private void StopAllAnimations()
        {
            StopCurrentAnimation();
            transform.DOKill(); // 停止所有与此Transform相关的DOTween动画
        }

        /// <summary>
        /// 瞬间移动到指定站点
        /// </summary>
        /// <param name="stationType">站点类型</param>
        public void TeleportToStation(TrainStationType stationType)
        {
            StopCurrentAnimation();

            Vector3 targetPosition = stationType switch
            {
                TrainStationType.Start => m_StartPoint != null ? m_StartPoint.position : transform.position,
                TrainStationType.Stay => m_StayPoint != null ? m_StayPoint.position : transform.position,
                TrainStationType.End => m_EndPoint != null ? m_EndPoint.position : transform.position,
                _ => transform.position
            };

            transform.position = targetPosition;
            if (GameEntry.TradeTruckData.IsUnlockTrade())
            {
                gameObject.SetActive(true);
            }
            ColorLog.Green($"火车瞬移到{stationType}站点");
            curTrainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            ColorLog.Green("更新当前火车状态", curTrainStatus);
            if (GameEntry.TradeTruckData.myTrain != null)
            {
                ColorLog.Green("ArrivalTime", GameEntry.TradeTruckData.myTrain.ArrivalTime);
                if (GameEntry.TradeTruckData.myTrain.Train != null)
                {
                    ColorLog.Green("PrepareTime", GameEntry.TradeTruckData.myTrain.Train.PrepareTime);
                }
            }
        }

        public override void OnClick()
        {
            base.OnClick();
            ColorLog.Pink("点击火车");
            TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
            ColorLog.Pink("火车状态", trainStatus);
            if (trainStatus == TrainStatus.LineUp || trainStatus == TrainStatus.Prepare)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainStationPlatformForm);
            }
        }
    }

    /// <summary>
    /// 火车站点类型
    /// </summary>
    public enum TrainStationType
    {
        Start,  // 起始点
        Stay,   // 停靠点
        End     // 终点
    }
}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &954974051390069826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435564365532514040}
  - component: {fileID: 612931771464903428}
  m_Layer: 9
  m_Name: 8_ZSJ_Battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435564365532514040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 954974051390069826}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1777741686053635971}
  - {fileID: 9139768838897921521}
  m_Father: {fileID: 1982839361562537378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &612931771464903428
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 954974051390069826}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 77254995ea013ed4281188e97fd73076, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1848374584621016149
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3776208872207355707}
  m_Layer: 9
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3776208872207355707
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848374584621016149}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000019726507, y: -0.00000035690132, z: -0.7071068, w: 0.7071067}
  m_LocalPosition: {x: -0.03986821, y: 1, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7205474169755378256}
  m_Father: {fileID: 2107136332881683819}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1909771263356921090
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3752680625233027246}
  m_Layer: 9
  m_Name: Bone004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3752680625233027246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909771263356921090}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -5.266305e-21, z: -0, w: 1}
  m_LocalPosition: {x: -0.30108994, y: 0.000000038146972, z: 5.953461e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2822785857559594738}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2338103128241691239
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2822785857559594738}
  m_Layer: 9
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2822785857559594738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2338103128241691239}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000009674111, y: -5.0382276e-10, z: -0.005208143, w: 0.99998647}
  m_LocalPosition: {x: -0.13079727, y: 0, z: 0}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3752680625233027246}
  m_Father: {fileID: 4455277831627642545}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2663728412263874965
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4455277831627642545}
  m_Layer: 9
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4455277831627642545
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2663728412263874965}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000002379967, y: -0.000000059265066, z: 0.0072235437, w: 0.99997395}
  m_LocalPosition: {x: -0.23635803, y: -0.000000038146972, z: -0.00000002818093}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2822785857559594738}
  m_Father: {fileID: 2788926672877492910}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3346860032917423815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9139768838897921521}
  m_Layer: 9
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9139768838897921521
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3346860032917423815}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2107136332881683819}
  - {fileID: 4071225099554929338}
  - {fileID: 2369942568993829241}
  m_Father: {fileID: 435564365532514040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4943368097770878886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2107136332881683819}
  m_Layer: 9
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2107136332881683819
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4943368097770878886}
  serializedVersion: 2
  m_LocalRotation: {x: -1.6658097e-21, y: 0.7071067, z: 1.6658093e-21, w: 0.7071069}
  m_LocalPosition: {x: 0.000000008742278, y: 2.638869, z: 0.19999968}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2788926672877492910}
  - {fileID: 3776208872207355707}
  m_Father: {fileID: 9139768838897921521}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5130312304101411887
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2788926672877492910}
  m_Layer: 9
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2788926672877492910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5130312304101411887}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000014073638, y: -2.836673e-10, z: -0.0020155893, w: 0.999998}
  m_LocalPosition: {x: -1.2000004, y: -0.9964097, z: 0.0056834603}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4455277831627642545}
  m_Father: {fileID: 2107136332881683819}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6495775727166508242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7205474169755378256}
  m_Layer: 9
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7205474169755378256
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6495775727166508242}
  serializedVersion: 2
  m_LocalRotation: {x: 6.123234e-17, y: -6.123234e-17, z: 6.123234e-17, w: 1}
  m_LocalPosition: {x: -0.29999998, y: 0.000000038146972, z: -0.000000034560724}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3776208872207355707}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6624520807362488747
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1982839361562537378}
  - component: {fileID: 8313841435776570954}
  - component: {fileID: 176132655653562278}
  m_Layer: 9
  m_Name: role_13402_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1982839361562537378
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6624520807362488747}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9, y: 0.9, z: 0.9}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 435564365532514040}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8313841435776570954
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6624520807362488747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1770034b84515b45a96b3f473aae6c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjTransform: {fileID: 9139768838897921521}
--- !u!114 &176132655653562278
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6624520807362488747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1eb6e16bbb69d1478b3a81466b2d544, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots:
  - Slot: 0
    Transform: {fileID: 2369942568993829241}
  - Slot: 9
    Transform: {fileID: 4071225099554929338}
--- !u!1 &8479476038717222097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4071225099554929338}
  m_Layer: 9
  m_Name: slot_hurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4071225099554929338
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8479476038717222097}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 2.5000002, z: 1.1999997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 9139768838897921521}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8525132716299775304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1777741686053635971}
  - component: {fileID: 1220906511600981111}
  m_Layer: 9
  m_Name: 8_ZSJ_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1777741686053635971
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8525132716299775304}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0.00038726805, y: 0, z: 0.23816028}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 435564365532514040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1220906511600981111
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8525132716299775304}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb0f78d6ff9d84b46a04b2f668ce1c2b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4881485729063806263, guid: 25b8604316b8f5a4cb1bc1f334fbb962, type: 3}
  m_Bones:
  - {fileID: 2107136332881683819}
  - {fileID: 9139768838897921521}
  - {fileID: 2788926672877492910}
  - {fileID: 2822785857559594738}
  - {fileID: 4455277831627642545}
  - {fileID: 3776208872207355707}
  - {fileID: 3752680625233027246}
  - {fileID: 7205474169755378256}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9139768838897921521}
  m_AABB:
    m_Center: {x: 0.0003874302, y: 2.762135, z: -0.108297825}
    m_Extent: {x: 0.79906815, y: 1.3185363, z: 2.3256934}
  m_DirtyAABB: 0
--- !u!1 &9161924008067092934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2369942568993829241}
  m_Layer: 9
  m_Name: slot_fire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2369942568993829241
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9161924008067092934}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000006657903, y: -1.1014576e-31, z: -1.6543612e-24, w: 1}
  m_LocalPosition: {x: 0, y: 1.6000003, z: 2.1999998}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 9139768838897921521}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

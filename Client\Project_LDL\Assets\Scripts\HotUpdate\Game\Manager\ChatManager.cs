using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class ChatManager : Singleton<ChatManager>
    {
        // 正则表达式缓存（提升性能）
        private Regex regexPattern;

        public override void Init()
        {
            InitRegexPattern();
        }

        public override void Dispose()
        {
        }

        // 构建正则表达式（优化性能）
        private void InitRegexPattern()
        {
            var forbiddenList = new List<string>(); // 屏蔽词列表
            var config = GameEntry.LDLTable.GetTable<forbidden>();
            for (int i = 0; i < config.Count; i++)
            {
                forbiddenList.Add(config[i].illegal_character);
            }

            if (forbiddenList.Count == 0)
            {
                regexPattern = null;
                return;
            }

            // 按长度排序（优先匹配长词）
            var sortList = forbiddenList
                .Distinct(StringComparer.OrdinalIgnoreCase) // 去重
                .OrderByDescending(w => w.Length) // 排序
                .Select(Regex.Escape); // 关键：转义特殊字符

            // 构建正则表达式模式
            var patternBuilder = new StringBuilder();
            patternBuilder.Append(@"\b(?:"); // 添加单词边界（避免部分匹配）
            patternBuilder.Append(string.Join("|", sortList));
            patternBuilder.Append(@")\b");

            // 创建编译后的正则表达式（提升性能）
            regexPattern = new Regex(patternBuilder.ToString(),
                RegexOptions.Compiled |
                RegexOptions.IgnoreCase |
                RegexOptions.CultureInvariant);
        }

        // 检查文本是否包含屏蔽词
        public bool IsInvalid(string input)
        {
            if (string.IsNullOrWhiteSpace(input) || regexPattern == null)
                return false;

            return regexPattern.IsMatch(input);
        }

        // 获取过滤后的字符串
        public string GetFilterStr(string input, char replacementChar = '*')
        {
            if (string.IsNullOrWhiteSpace(input) || regexPattern == null)
                return input;

            return regexPattern.Replace(input, m => new string(replacementChar, m.Value.Length));
        }

        // 获取匹配的屏蔽词
        public IEnumerable<string> GetMatchedWords(string input)
        {
            if (string.IsNullOrWhiteSpace(input) || regexPattern == null)
                return Enumerable.Empty<string>();

            return regexPattern.Matches(input)
                .Cast<Match>()
                .Select(m => m.Value)
                .Distinct(StringComparer.OrdinalIgnoreCase);
        }
    }
}


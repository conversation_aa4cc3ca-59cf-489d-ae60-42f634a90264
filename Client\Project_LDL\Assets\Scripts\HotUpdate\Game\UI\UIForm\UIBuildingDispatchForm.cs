using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingDispatchForm : UGuiFormEx
    {
        private GameObject _attrObj;
        private build_level buildingLevelCfg;
        private Dictionary<attributes_type,int> _buildAttributes = new Dictionary<attributes_type,int>();
        private int _curBuildingID;
        private BuildingModule curBuildingModule;
        private List<Transform> m_SurvivorObjList;

        private List<attributes> m_BuildingAttrList;
        private Dictionary<attributes_type, float> m_BuildingAttrDic;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitUI();
        }

        protected void InitUI()
        {
            _attrObj = m_goDetailItem.gameObject;
        }
        
        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            curBuildingModule = userData as BuildingModule;
            if (curBuildingModule == null)
            {
                return;
            }

            m_SurvivorObjList = new List<Transform>();
            m_BuildingAttrList = new List<attributes>();
            m_BuildingAttrDic = new Dictionary<attributes_type, float>();
            _curBuildingID = curBuildingModule.BuildingId;
            int level = curBuildingModule.LEVEL;
            buildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(curBuildingModule.buildingCfg.build_type,level);
            InitSurvivorList();
            ResetAttrParams();
            RestAttrScrollView();
            RestUI();
            ResetSurvivorList();
            m_TableViewVAttr.InitTableViewByIndex(0);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_SurvivorObjList.Clear();
            m_transSurvivorList.gameObject.DestroyAllChild();
            m_BuildingAttrList.Clear();
            m_BuildingAttrDic.Clear();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            ResetSurvivorList();
            m_BuildingAttrList.Clear();
            m_BuildingAttrDic.Clear();
            ResetAttrParams();
            m_TableViewVAttr.ReloadData();
        }
        
        protected void RestUI()
        {
            m_imgBuildingIcon.SetImage(curBuildingModule.BuildingIcon);
            m_txtBuildingName.text = curBuildingModule.BuildingName;
            int level = curBuildingModule.LEVEL;
            m_txtBuildingCurLevel.text = $"Lv.{level}";
        }

        protected void RestAttrScrollView()
        {
            m_TableViewVAttr.GetItemCount = () => m_BuildingAttrList.Count;
            m_TableViewVAttr.GetItemGo = () => _attrObj;
            m_TableViewVAttr.UpdateItemCell = UpdataAttrListView;
        }

        private void UpdataAttrListView(int index, GameObject obj)
        {
            obj.SetActive(true);
            Text txtAttrName     = obj.transform.Find("txtAttrName").GetComponent<Text>();
            Text txtAttrValue    = obj.transform.Find("txtAttrValue").GetComponent<Text>();

            attributes mBuildingAttr = m_BuildingAttrList[index];
            attributes_type buildAttrType = mBuildingAttr.attributes_type;
            valuetype valueType = mBuildingAttr.value_type;
            txtAttrName.text = ToolScriptExtend.GetNameByAttrbuteType(buildAttrType);
            float value = m_BuildingAttrDic[buildAttrType];
            string valueStr = string.Empty;
            if (valueType == valuetype.valuetype_2)
            {
                valueStr = $"+{value}%";
            }
            else if (buildAttrType == attributes_type.attributes_type_55 ||
                     buildAttrType == attributes_type.attributes_type_56 ||
                     buildAttrType == attributes_type.attributes_type_63 ||
                     buildAttrType == attributes_type.attributes_type_83 ||
                     buildAttrType == attributes_type.attributes_type_73)
            {
                //valueStr = TimeHelper.FormatGameTimeWithDays((int)value);
                valueStr = ToolScriptExtend.FormatTimeShow((int)value);
            }
            else
            {
                valueStr = $"+{value}";
            }
            txtAttrValue.text = valueStr;
        }
        
        protected void ResetAttrParams()
        {
            if (buildingLevelCfg != null)
            {
                for (int i = 0; i < buildingLevelCfg.build_level_attributes.Count; i++)
                {
                    attributes buildLevelAttribute = buildingLevelCfg.build_level_attributes[i];
                    if (buildLevelAttribute.attributes_type != attributes_type.attributes_type_68)//士兵等级隐藏
                    {
                        m_BuildingAttrList.Add(buildLevelAttribute);
                    }
                    
                    m_BuildingAttrDic[buildLevelAttribute.attributes_type] = buildLevelAttribute.value_type switch
                    {
                        valuetype.valuetype_1 => buildLevelAttribute.value,
                        valuetype.valuetype_2 => (float)buildLevelAttribute.value/100,
                        _ => 0
                    };
                }
            }

            // 建筑本身属性
            Dictionary<attributes_type,float> buildingAttrDic = GameEntry.LogicData.BuildingData.GetAttrbutesConfigByType(curBuildingModule.GetBuildingType(),curBuildingModule.LEVEL);
            List<uint> survivorList = curBuildingModule.SurvivorList;
            for (int i = 0; i < survivorList.Count; i++)
            {
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(survivorList[i]);
                survivor_star survivorStarConfig = GameEntry.LogicData.SurvivorData.GetSurvivorStarConfig(survivorMoudle.SurvivorCfg.profession,
                    (int)survivorMoudle.StarStage,survivorMoudle.Quality);
                List<attributes> starAttributes = survivorStarConfig.star_attributes;
                for (var i1 = 0; i1 < starAttributes.Count; i1++)
                {
                    attributes survivorStarAttributes = starAttributes[i1];
                    attributes_type survivorAdditionType = survivorStarAttributes.attributes_type;
                    attributes_type mappingBuildingType = GameEntry.LogicData.BuildingData.GetAttrbutesMappingAdditionType(survivorAdditionType);
                    //属于加成对应建筑属性
                    if (m_BuildingAttrDic.ContainsKey(mappingBuildingType))
                    {
                        if (survivorStarAttributes.value_type == valuetype.valuetype_1)
                        {
                            int additionValue = survivorStarAttributes.value;
                            if (survivorStarAttributes.attributes_type == attributes_type.attributes_type_81)
                            {
                                m_BuildingAttrDic[mappingBuildingType] -= additionValue;
                            }
                            else
                            {
                                m_BuildingAttrDic[mappingBuildingType] += additionValue;
                            }
                        }
                        else if (survivorStarAttributes.value_type == valuetype.valuetype_2)
                        {
                            float additionValue = (float)survivorStarAttributes.value / 10000;
                            int buildingAttrValue = (int)buildingAttrDic[mappingBuildingType];
                            int realAdd = Mathf.FloorToInt(additionValue * buildingAttrValue);
                            m_BuildingAttrDic[mappingBuildingType] += realAdd;
                        }
                        
                    }
                    // 幸存者属性对建筑没加成
                    else
                    {
                        if (!m_BuildingAttrDic.ContainsKey(survivorStarAttributes.attributes_type))
                        {
                            m_BuildingAttrList.Add(survivorStarAttributes);
                            m_BuildingAttrDic[survivorStarAttributes.attributes_type] = survivorStarAttributes.value_type switch
                            {
                                valuetype.valuetype_1 => survivorStarAttributes.value,
                                valuetype.valuetype_2 => (float)survivorStarAttributes.value/100,
                                _ => 0
                            };
                        }
                        else
                        {
                            m_BuildingAttrDic[survivorStarAttributes.attributes_type] = survivorStarAttributes.value_type switch
                            {
                                valuetype.valuetype_1 => m_BuildingAttrDic[survivorStarAttributes.attributes_type] + survivorStarAttributes.value,
                                valuetype.valuetype_2 => (float)m_BuildingAttrDic[survivorStarAttributes.attributes_type] + (float)survivorStarAttributes.value / 100,
                                _ => 0
                            };
                        }
                    }
                }
              
            }
        }

        private void InitSurvivorList()
        {
            for (int i = 0; i < 4; i++)
            {
                GameObject go = Instantiate(m_goSurvival, m_transSurvivorList);
                go.gameObject.SetActive(true);
                m_SurvivorObjList.Add(go.transform);   
            }
        }

        private void ResetSurvivorList()
        {
            for (int i = 0; i < 4; i++)
            {
                Transform survivorObj = m_SurvivorObjList[i];
                if (i < curBuildingModule.SurvivorList.Count)
                {
                    uint survivorId = curBuildingModule.SurvivorList[i];
                    UpdateSurvivorList(survivorObj,i,survivorId);
                }
                else
                {
                    UpdateSurvivorList(survivorObj,i);
                }
            }
        }

        private void UpdateSurvivorList(Transform itemTransform,int index,uint survivorId = 0)
        {
            Transform goAdd            = itemTransform.Find("goAdd");
            Transform goActive         = itemTransform.Find("goActive");
            Transform goStarRoot         = itemTransform.Find("goActive/goStarRoot");
            UIImage qualityBg          = itemTransform.Find("goActive/qualityBg").GetComponent<UIImage>();
            UIImage headBg             = itemTransform.Find("goActive/qualityBg/headBg").GetComponent<UIImage>();
            UIImage head               = itemTransform.Find("goActive/qualityBg/headBg/mask/head").GetComponent<UIImage>();
            Text txtSurvivalName       = itemTransform.Find("goActive/txtSurvivalName").GetComponent<Text>();
            Text txtSurvivalType       = itemTransform.Find("goActive/txtSurvivalType").GetComponent<Text>();
            UIButton btnOpen           = itemTransform.Find("goActive/btnOpen").GetComponent<UIButton>();
            UIButton btnOpenList       = itemTransform.Find("btnOpenList").GetComponent<UIButton>();
            
            SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(survivorId);
            goAdd.gameObject.SetActive(true);
            goActive.gameObject.SetActive(false);

            if (survivorMoudle != null)
            {
                goAdd.gameObject.SetActive(false);
                goActive.gameObject.SetActive(true);
                string bgPath = GameEntry.LogicData.SurvivorData.GetQualityBg(survivorMoudle.Quality);
                qualityBg.SetImage(bgPath);

                string headQualityBg = GameEntry.LogicData.SurvivorData.GetHeadQualityBg(survivorMoudle.Quality);
                headBg.SetImage(headQualityBg);

                txtSurvivalName.text = survivorMoudle.Name;
                txtSurvivalType.text = survivorMoudle.ProfessionName;

                if (!string.IsNullOrEmpty(survivorMoudle.HeadIcon))
                {
                    head.SetImage(survivorMoudle.HeadIcon,true);
                }

                bool canUpGrade = survivorMoudle.GetCanUpGrade();
                goStarRoot.gameObject.SetActive(canUpGrade);
                if (canUpGrade)
                {
                    RefreshStarInfo(survivorMoudle,goStarRoot);
                }
            }
            
            btnOpen.onClick.RemoveAllListeners();
            btnOpen.onClick.AddListener(() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UISurvivorUpStarGradeForm, survivorId);
            });
            
            btnOpenList.onClick.RemoveAllListeners();
            btnOpenList.onClick.AddListener(() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIDispatchListForm, new OpenSurvivorListParams(survivorId, curBuildingModule,index + 1));

            });
        }
        
        protected void RefreshStarInfo(SurvivorMoudle curSurvivorMoudle, Transform starRoot)
        {
            int starNum = curSurvivorMoudle.StarNum;
            int starOrder = curSurvivorMoudle.StarOrder;
            var count = starRoot.transform.childCount;
            for (int i = 0; i < count; i++)
            {
                var starSp = starRoot.transform.GetChild(i).GetComponent<UIImage>();
                string pathStr;
                if (i < starNum)
                {
                    pathStr = "Sprite/ui_hero/hero_icon_star5.png";
                }
                else if (i < starNum + 1 && starOrder > 0)
                {
                    pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder);
                }
                else
                {
                    pathStr = "Sprite/ui_hero/hero_icon_star0.png";
                }
                starSp.SetImage(pathStr);
            }
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnDispatchClick()
        {
            if (curBuildingModule == null)
            {
                Debug.LogError($"当前建筑为空!");
                return;
            }
            GameEntry.LogicData.SurvivorData.SurvivorFastDispatchReq((uint)curBuildingModule.BuildingId,() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100525)
                });
            });
        }
    }
}

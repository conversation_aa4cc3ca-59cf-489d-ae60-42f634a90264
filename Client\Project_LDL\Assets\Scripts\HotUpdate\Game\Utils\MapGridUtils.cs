using System;
using System.Collections.Generic;
using UnityEngine;
using Wmcamera;

namespace Game.Hotfix
{
    public static class MapGridUtils
    {
        /// <summary>
        ///  获取射线与地面的交点
        /// </summary>
        /// <param name="position"></param>
        /// <param name="forward"></param>
        /// <param name="groundLevel"></param>
        /// <returns></returns>
        public static Vector3 GetIntersectionPoint(Vector3 position, Vector3 forward, float groundLevel = 0)
        {
            return position + forward * ((groundLevel - position.y) / forward.y);
        }

        // 根据世界坐标转化为grid坐标
        public static Vector2Int WorldToGrid(Vector3 worldPosition, float gridSizeX = 1,float? gridSizeY = null)
        {
            int gridX = Mathf.FloorToInt(worldPosition.x / gridSizeX);
            int gridY = Mathf.FloorToInt(worldPosition.z / (gridSizeY ?? gridSizeX));
            return new Vector2Int(gridX, gridY);
        }
        
        public static Vector2Int WorldToGrid(Vector3 worldPosition, Vector2Int gridSize)
        {
            return WorldToGrid(worldPosition, gridSize.x, gridSize.y);
        }

        /// <summary>
        /// 获取相机注视的世界位置。
        /// </summary>
        /// <param name="cityCamera">城市相机对象。</param>
        /// <returns>返回相机注视点的世界位置，如果相机对象为空，则返回(0, 0, 0)。</returns>
        public static Vector3 GetCameraLookAtWorldPosition(Camera cityCamera)
        {
            // 检查相机对象是否存在
            if (cityCamera)
            {
                // 调用方法计算并返回相机注视点的世界位置
                return GetIntersectionPoint(cityCamera.transform.position, cityCamera.transform.forward);
            }

            // 如果相机对象为空，返回原点位置
            return Vector3.zero;
        }

        
        public static Vector3 GetWorldPositionByScreenPos(Vector2 screenPos,Camera camera)
        {
            var forward = camera.transform.forward;
            var pPosition = camera.ScreenToWorldPoint(screenPos);
            if (!camera.orthographic)
                forward = camera.ScreenPointToRay(screenPos).direction;//透视相机打开
            return GetIntersectionPoint(pPosition, forward);
        }

        /// <summary>
        /// 查找路径
        /// </summary>
        /// <param name="start"></param>
        /// <param name="isTargetNode"></param>
        /// <param name="isValidNode"></param>
        /// <returns></returns>
        public static List<Vector2Int> PathFind(Vector2Int start, Func<Vector2Int,bool> isTargetNode, Func<Vector2Int,bool> isValidNode)
        {
            List<Vector2Int> directions = new List<Vector2Int> { new Vector2Int(-1, 0), new Vector2Int(1, 0), new Vector2Int(0, -1), new Vector2Int(0, 1) };
            
            Dictionary<Vector2Int, Vector2Int> parent = new Dictionary<Vector2Int, Vector2Int>();
            HashSet<Vector2Int> visited = new HashSet<Vector2Int>();
            
            Queue<Vector2Int> queue = new Queue<Vector2Int>();
            visited.Clear();
            parent.Clear();

            queue.Enqueue(start);
            visited.Add(start);

            List<Vector2Int> path = null;

            while (queue.Count > 0)
            {
                Vector2Int current = queue.Dequeue();

                if (isTargetNode(current))
                {
                    path = new List<Vector2Int>();
                    Vector2Int node = current;

                    while (parent.ContainsKey(node))
                    {
                        path.Insert(0, node);
                        node = parent[node];
                    }

                    path.Insert(0, start); // Add the start node at the beginning
                    break;
                }

                foreach (Vector2Int dir in directions)
                {
                    Vector2Int neighbor = current + dir;

                    if (!visited.Contains(neighbor) && isValidNode(neighbor))
                    {
                        queue.Enqueue(neighbor);
                        visited.Add(neighbor);
                        parent[neighbor] = current;
                    }
                }
            }

            return path;
        }


        public static Vector2Int GetGrid(Vector3 position, Vector2Int gridSize)
        {
            return GetGrid(position.x, position.z, (ushort)gridSize.x, (ushort)gridSize.y);
        }
        
        public static Vector2Int GetGrid(Vector3 position,ushort gridSize = 1)
        {
            return GetGrid(position.x, position.z, gridSize, gridSize);
        }
        
        public static Vector2Int GetGrid(Vector3 position,ushort gridSizeX,ushort gridSizeY)
        {
            return GetGrid(position.x, position.z, gridSizeX, gridSizeY);
        }

        public static Vector2Int GetGrid(float x, float y, Vector2Int gridSize)
        {
            return GetGrid(x, y, (ushort)gridSize.x, (ushort)gridSize.y);
        }
        
        public static Vector2Int GetGrid(float x, float y, ushort gridSizeX)
        {
            return GetGrid(x, y, gridSizeX, gridSizeX);
        }
        
        private static Vector2Int GetGrid(float x,float y,ushort gridSizeX,ushort gridSizeY)
        {
            if(gridSizeX == 0 || gridSizeY == 0)
                return Vector2Int.zero;
            
            int gridX = Mathf.FloorToInt(x / gridSizeX);
            int gridZ = Mathf.FloorToInt(y / gridSizeY); // 假设使用 XZ 平面
            return new Vector2Int(gridX, gridZ);
        }
        
        public static List<Vector2Int> GetCornerPoints(List<Vector2Int> points, float tolerance = 0f)
        {
            // 如果输入列表为空或点数小于3，则无法确定拐点.直接返回空列表
            if (points == null || points.Count < 3)
            {
                return new List<Vector2Int>(); // 返回一个空的 List<Vector2Int>
            }

            List<Vector2Int> cornerPoints = new List<Vector2Int>();
            cornerPoints.Add(points[0]); // 始终包含第一个点

            for (int i = 1; i < points.Count - 1; i++)
            {
                Vector2Int a = points[i - 1];
                Vector2Int b = points[i];
                Vector2Int c = points[i + 1];

                // 计算两个向量的方向.
                Vector2 direction1 = ((Vector2)(b - a)).normalized;
                Vector2 direction2 = ((Vector2)(c - b)).normalized;

                // 如果方向发生改变，则将当前点添加到拐点列表.
                //注意这里使用的是方向向量的的数量积来判断。
                if (Mathf.Abs(Vector2.Dot(direction1, direction2)) < 1 - tolerance)
                {
                    cornerPoints.Add(b); // b 是一个拐点
                }
            }

            cornerPoints.Add(points[points.Count - 1]); // 始终包含最后一个点
            return cornerPoints;
        }

        public static int LogicGridPos2Id(int x, int y)
        {
            return x / 40 + y / 40 * 1000 + 1;
        }

        public static void LogicGridId2Pos(int gridId, out int x, out int y)
        {
            x = ((gridId - 1) % 1000) * 40;
            y = ((gridId - 1) / 1000) * 40;
        }

        public static void ScreenId2GridId(int screenId, out int gridX,out int gridY)
        {
            gridX = (screenId-1) % GameDefine.WorldMapDataGridNum.x; // 整数除法
            gridY = (screenId-1) / GameDefine.WorldMapDataGridNum.x; // 取模
        }
        
        public static int GridId2ScreenId(int x, int y)
        {
            return y * GameDefine.WorldMapDataGridNum.x + x + 1;
        }

        public static void MapId2Pos(int mapId, out int x, out int y)
        {
            x = mapId % 1000;
            y = mapId / 1000;
        }

        public static void Pos2MapId(int x,int y, out int mapId)
        {
            mapId = y * 1000 + x;
        } 
        
        public static CameraLayer GetLayerByLod(WorldMapLOD lod)
        {
            return lod >= WorldMapLOD.Level4 ? CameraLayer._2 : CameraLayer._1;
        }
        
        
    }
}
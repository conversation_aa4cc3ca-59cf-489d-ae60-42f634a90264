using System;
using UnityEngine;
using Wmcamera;

namespace Game.Hotfix
{
    public class WMCmpLogicCameraCtrl : IWMCmp
    {
        public Vector2Int GridSize => m_GridSize;
        
        private Vector2Int m_GridSize = new Vector2Int(40, 40);

        private Vector2Int m_LastLookAtLogicGrid;
        private Vector2Int m_LastLookAtLogicGridSize;
        
        private WorldMapComponent m_WorldMapComponent;
        
        public WMCmpLogicCameraCtrl(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }
        
        public void Init()
        {
            CameraInit((size) =>
            {
                // if (size != Vector2Int.zero)
                //     m_GridSize = size;
            });
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
        }

        private void OnCameraMove()
        {
            if(CameraComponent.ZoomLevel >= WorldMapLOD.Level5)
            {
                return;
            }
            
            var worldPos = GameEntry.Camera.GetCurrentLookAtPosition();
            
            var curLookAtLogicGrid =MapGridUtils.WorldToGrid(worldPos,m_GridSize);
            var size = m_WorldMapComponent.GetCameraLookGridBorderSize();
            
            if (curLookAtLogicGrid == m_LastLookAtLogicGrid && (size-m_LastLookAtLogicGridSize).sqrMagnitude <= 1)
                return;
            
            m_LastLookAtLogicGrid = curLookAtLogicGrid;
            m_LastLookAtLogicGridSize = size;
            
            var gridPos = MapGridUtils.WorldToGrid(worldPos);

            CameraLayer layer = MapGridUtils.GetLayerByLod(CameraComponent.ZoomLevel);

            CameraMove(gridPos, size, layer);
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            CameraUnInit();
        }


        private void CameraInit(Action<Vector2Int> callback)
        {
            
            CameraInitReq cameraInitReq = new CameraInitReq();
            cameraInitReq.ServerID = 1;
            GameEntry.LDLNet.Send(Protocol.MessageID.CameraInit, cameraInitReq, (errorCode,message) =>
            {
                if (errorCode != 0) 
                {
                    callback?.Invoke(new Vector2Int(20, 20));
                }
                CameraInitResp resp = message as CameraInitResp;
                if (resp != null)
                {
                    callback?.Invoke(new Vector2Int((int)resp.GridWidth, (int)resp.GridHeight));
                }
            });
        }
        
        private void CameraUnInit(Action callback = null)
        {
            
            CameraRemoveReq req = new CameraRemoveReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.CameraRemove, req, (message) =>
            {
                CameraRemoveResp resp = message as CameraRemoveResp;
                if (resp != null)
                {
                    callback?.Invoke();
                }
            });
        }
        
        private void CameraMove(Vector2Int pos,Vector2Int size,CameraLayer layer,Action callback = null)
        {
            CameraMoveReq req = new CameraMoveReq();
            req.X = pos.x;
            req.Y = pos.y;
            req.Layer = layer;
            ColorLog.Pink(req.X + " " + req.Y + " " + layer);
            GameEntry.LDLNet.Send(Protocol.MessageID.CameraMove, req, (error,message) =>
            {
                CameraMoveResp resp = message as CameraMoveResp;
                if (resp != null)
                {
                    GameEntry.LogicData.WorldMapData.ProcessLogicalCameraData(error,resp);
                    callback?.Invoke();
                }
            });
        }
    }
}
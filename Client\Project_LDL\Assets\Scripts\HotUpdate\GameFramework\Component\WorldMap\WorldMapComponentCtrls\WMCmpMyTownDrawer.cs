using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpMyTownDrawer : IWMCmp
    {
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private int? m_MyTownEntityId = null;
        private Vector2Int? m_MyTownPos;

        private WorldMapComponent m_WorldMapComponent;

        private ulong? m_PlayerId = null;

        public WMCmpMyTownDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_PlayerId = GameEntry.LogicData.UserData.uuid;
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            GameEntry.Event?.Subscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);
        }

        private void OnOpModeChangeEvent(object sender, GameEventArgs e)
        {
            if (e is OnOpModeChangeEventArgs args)
            {
                if (args.newOp == MapOpType.TOWN_MOVE_MODE)
                {
                    var previewUid = m_WorldMapComponent.GetPreviewUid();
                    if (previewUid != null)
                    {
                        if (m_MyTownEntityId != null)
                        {
                            Game.GameEntry.Entity.AttachEntity(m_MyTownEntityId.Value, previewUid.Value);
                        }
                        OnCameraMove();
                    }
                }
                else
                {
                    //恢复
                    if (m_MyTownEntityId != null)
                    {
                        if (GameEntry.Entity.HasEntity(m_MyTownEntityId.Value))
                        {
                            GameEntry.Entity.DetachEntity(m_MyTownEntityId.Value);
                            GameEntry.Entity.HideEntity(m_MyTownEntityId.Value);
                        }

                        m_MyTownEntityId = null;
                    }

                    m_ShowList.Clear();
                    OnCameraMove();
                }
            }
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            GameEntry.Event?.Unsubscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);
        }

        private void OnCameraMove()
        {
            if (m_WorldMapComponent.GetMapOpType() == MapOpType.TOWN_MOVE_MODE)
            {
                //迁城模式
                if(CameraComponent.ZoomLevel == WorldMapLOD.Level1)
                {
                    if (m_MyTownEntityId == null)
                    {
                        var data = GameEntry.LogicData.WorldMapData.GetMyTownData();
                        m_MyTownEntityId = CreateMyTownEntity(data, m_WorldMapComponent.GetPreviewUid());
                    }
                }
                else
                {
                    if (m_MyTownEntityId != null)
                    {
                        if (GameEntry.Entity.HasEntity(m_MyTownEntityId.Value))
                        {
                            GameEntry.Entity.DetachEntity(m_MyTownEntityId.Value);
                            GameEntry.Entity.HideEntity(m_MyTownEntityId.Value);
                        }

                        m_MyTownEntityId = null;
                    }
                }
            }
            else
            {
                m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                    WorldMapLOD.Level1, out List<Vector2Int> showListNew);

                List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
                List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

                foreach (var t in remove)
                {
                    Remove(t);
                }

                foreach (var t in add)
                {
                    Add(t);
                }
            }
            
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                var list = gridData.TownData;
                if (list != null)
                {
                    for (int i = 0; i < list.Count; i++)
                    {
                        GridTownData data = list[i];
                        if (data.RoleId == m_PlayerId)
                        {
                            m_MyTownEntityId = CreateMyTownEntity(data, null);
                            m_MyTownPos = grid;
                        }
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (grid == m_MyTownPos)
                {
                    if (m_MyTownEntityId != 0)
                    {
                        GameEntry.Entity.HideEntity(m_MyTownEntityId.Value);
                    }

                    m_MyTownPos = null;
                    m_MyTownEntityId = null;
                }
            }
        }

        private string GetPathById(GridTownData data)
        {
            var cfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildtype.buildtype_headquarters,
                data.Level);
            if (cfg is { map_pre_id: > 0 })
            {
                var config = GameEntry.LDLTable.GetTableById<map_buildpre>(cfg.map_pre_id);
                return config?.pre;
            }

            return string.Empty;
        }

        private int CreateMyTownEntity(GridTownData data,int? parentId)
        {
            var path = GetPathById(data);
            ED_WorldMapTown param =
                new ED_WorldMapTown(Game.GameEntry.Entity.GenerateSerialId(), data,parentId);
            param.Position = new Vector3(data.PosX, 0, data.PosY);
            int id = GameEntry.Entity.ShowWorldMapDisplay(path, param, typeof(EL_WorldMapTown));
            m_MyTownEntityId = id;
            return id;
        }

    }
}
using GameFramework.Fsm;
using GameFramework.Procedure;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class Procedure5v5Battle : ProcedureBase
    {
        private IFsm<IProcedureManager> m_ProcedureOwner;
        public override bool UseNativeDialog { get; }

        private int? m_UIId = null;

        public void GoBackToMain()
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", (int)SceneDefine.MainScene);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }

        protected override void OnEnter(IFsm<IProcedureManager> procedureOwner)
        {
            base.OnEnter(procedureOwner);
            m_ProcedureOwner = procedureOwner;

            if (TempConnectHelper.Instance.ConnectTargetPath == TempConnectHelper.ConnectTarget.技能编辑)
            {
                m_UIId = GameEntry.UI?.OpenUIForm(EnumUIForm.UIBattle5v5Debug);
            }
            else if(GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.Replay)
            {
                //这里什么界面也不弹出，在Battle5v5Component中直接回放
            }
            else
            {
                m_UIId = GameEntry.UI?.OpenUIForm(EnumUIForm.UIBattle5v5Choose);
            }
        }

        protected override void OnLeave(IFsm<IProcedureManager> procedureOwner, bool isShutdown)
        {
            if (m_UIId != null && GameEntry.UI.HasUIForm(m_UIId.Value))
                GameEntry.UI.CloseUIForm(m_UIId.Value);
            GameEntry.UI.CloseAllLoadedUIForms();

            base.OnLeave(procedureOwner, isShutdown);
        }

        public void GotoMainCity()
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", (int)SceneDefine.MainScene);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }
        
        public void GoToWorldMap()
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", (int)SceneDefine.WorldMapScene);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }
    }
}
using System;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class WmpTownUiHud : WmpUiHudBase
    {
        [SerializeField] public GameObject goA;
        [SerializeField] public GameObject goB;

        [SerializeField] public GameObject goLevelItem;

        [SerializeField] public UIText txtLevel;


        private Camera m_MainCamera;
        private RectTransform m_SelfRect;
        private RectTransform m_ParentRect;
        private Vector2 m_TempV2;

        private Vector2Int m_Pos;
        private ulong m_PlayerId;


        public void SetData(GridTownData data)
        {
            m_PlayerId = data.RoleId;
            m_Pos = new Vector2Int((int)data.PosX, (int)data.PosY);

            txtLevel.text = data.Level.ToString();

            ResetDisplay();
        }

        public override void Depool()
        {
            if (m_MainCamera == null)
                m_MainCamera = GameEntry.Camera.WorldMapCamera;
            if (m_ParentRect == null)
                m_ParentRect = transform.parent.GetComponent<RectTransform>();
            if (m_SelfRect == null)
                m_SelfRect = transform.GetComponent<RectTransform>();

            GameEntry.Event?.Subscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
        }

        private void OnWorldMapChangeLOD(object sender, GameEventArgs e)
        {
            ResetDisplay();
        }

        private void ResetDisplay()
        {
            var curLevel = CameraComponent.ZoomLevel;
            goA.SetActive(curLevel >= WorldMapLOD.Level2 && curLevel < WorldMapLOD.Level3);
            goB.SetActive(curLevel >= WorldMapLOD.Level3 && curLevel <= WorldMapLOD.Level5);
            
            goLevelItem.SetActive(curLevel <= WorldMapLOD.Level3);
        }

        public override void Repool()
        {
            GameEntry.Event?.Unsubscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
        }

        private void LateUpdate()
        {
            ResetPos();
        }

        private void ResetPos()
        {
            var screenPoint = RectTransformUtility.WorldToScreenPoint(m_MainCamera,
                new Vector3(m_Pos.x, 0, m_Pos.y) + GameDefine.HalfOffset);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRect, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            m_SelfRect.anchoredPosition = m_TempV2;
        }
    }
}
using System;
using DG.Tweening;
using UnityEngine;

namespace Game.Hotfix
{
    public class OpModuleParam
    {
        public float ZOOM_MAX = 0;
        public float ZOOM_MIN = 0;
        public float ZOOM_SPEED = 0;
        public int MAP_MAX_SIZE = 0;

        public float ZOOM_Value_P = 0;
        public float ZOOM_MAX_P = 0;//透视
        public float ZOOM_MIN_P = 0;//透视
        public float ZOOM_SPEED_P = 0;//透视 （ZOOM_MAX_P-ZOOM_MIN_P)/5

        public float[] ZOOM_LEVEL_DEFINE = null;
        public float[] ZOOM_SPEED_DEFINE = null;
        
        public Camera camera;
        public Rect mapLimit;

        public WorldMapLOD GetLODLevel(float zoomPct)
        {
            if (ZOOM_LEVEL_DEFINE != null)
            {
                for (int i = 0; i < ZOOM_LEVEL_DEFINE.Length; i++)
                {
                    if (zoomPct <= ZOOM_LEVEL_DEFINE[i])
                    {
                        return (WorldMapLOD)(i + 1);
                    }
                }
            }
            return WorldMapLOD.LevelNone;
        }

        public float GetZoomSpeed(float zoomPct)
        {
            float speed = ZOOM_SPEED_P;
            if (ZOOM_SPEED_DEFINE != null)
            {
                for (int i = 0; i < ZOOM_SPEED_DEFINE.Length; i++)
                {
                    if (zoomPct >= i/(float)ZOOM_SPEED_DEFINE.Length)
                    {
                        speed = ZOOM_SPEED_DEFINE[i];
                    }
                }
            }
            return speed;
        }
    }
    
    public abstract class OpModeBase
    {   
        private bool m_Enable = false;
        protected OpModuleParam m_Param;
        private Rect m_MapLimit;
        private Tween m_CameraMoveTween;
        private PointerActionInfo m_NewPointer;
        private float m_ThrowProgress = 0f;
        private Vector2 m_ThrowSpeed;

        public OpModeBase()
        {
            m_Param = GameEntry.Camera.GetCameraParam();
            m_MapLimit = m_Param.mapLimit;
            
            var controller = GameEntry.Input;
            controller.tapped += OnBaseTap;
            controller.dragged += OnBaseDragged;
            controller.released += OnBaseReleased;
            controller.pressed += OnBasePressed;
            controller.spunWheel += OnBaseSpunWheel;
            controller.pinched += OnBasePinched;

            //重置
            OnSpunWheel(new WheelInfo
            {
                zoomAmount = 0
            });
        }

        public void Destroy()
        {
            var controller = GameEntry.Input;
            controller.tapped -= OnBaseTap;
            controller.dragged -= OnBaseDragged;
            controller.released -= OnBaseReleased;
            controller.pressed -= OnBasePressed;
            controller.spunWheel -= OnBaseSpunWheel;
            controller.pinched -= OnBasePinched;
            
        }

        public abstract void OnDestroy();
        
        public abstract MapOpType GetOpType();

        public abstract void Update(float dt);

        public virtual void LateUpdate(float dt)
        {
            if (m_CameraMoveTween != null)
            {
                m_NewPointer.previousPosition = m_NewPointer.currentPosition;
                m_NewPointer.currentPosition += m_ThrowSpeed * m_ThrowProgress; 
                OnDragged(m_NewPointer);
            }
        }
        
        public void SetEnable(bool enable)
        {
            m_Enable = enable;
            OnEnableStateChange(m_Enable);
        }

        protected virtual void OnEnableStateChange(bool enable)
        {
            
        }
        
        private void OnBaseTap(PointerActionInfo obj)
        {
            if (m_Enable)
            {
                BreakCameraThrow();
                OnTap(obj);
            }
            
        }

        private void OnBaseDragged(PointerActionInfo obj)
        {
            if (m_Enable)
            {
                BreakCameraThrow();
                OnDragged(obj);
            }
        }

        private void OnBaseReleased(PointerActionInfo obj)
        {
            if (m_Enable)
            {
                BreakCameraThrow();
                OnReleased(obj);
            }
        }

        private void OnBasePressed(PointerActionInfo obj)
        {
            if (m_Enable)
            {
                BreakCameraThrow();
                OnPressed(obj);
            }
        }

        private void OnBaseSpunWheel(WheelInfo obj)
        {
            if (m_Enable)
            {
                if (Input.GetKey(KeyCode.LeftControl))
                {
                    BreakCameraThrow();
                    OnSpunWheel(obj);    
                }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "按住Ctrl开始缩放",
                    });
                }
                
            }
        }

        private void OnBasePinched(PinchInfo pinchInfo)
        {
            var currentDistance = pinchInfo.touch1.currentPosition - pinchInfo.touch2.currentPosition;
            var previousDistance = pinchInfo.touch1.previousPosition - pinchInfo.touch2.previousPosition;
            float offset = currentDistance.magnitude - previousDistance.magnitude;
            var speed = 0.001f;
            var scaleFactor = offset * speed;
            // Debug.LogError("缩放" + scaleFactor);
            OnSpunWheel(new WheelInfo
            {
                zoomAmount = scaleFactor
            });
            
        }


        //==================================
        
        protected virtual void OnTap(PointerActionInfo pointer)
        {
            
        }

        protected virtual void OnDragged(PointerActionInfo pointer)
        {
            if (pointer.startedOverUI)
            {
                return;
            }
            var camera = m_Param.camera;
            if (camera == null) return;
            var forward = camera.transform.forward;
            var pPosition = camera.ScreenToWorldPoint(pointer.previousPosition);
            if (!camera.orthographic)
                forward = camera.ScreenPointToRay(pointer.previousPosition).direction;//透视相机打开
            pPosition = MapGridUtils.GetIntersectionPoint(pPosition, forward);
            
            var cPosition = camera.ScreenToWorldPoint(pointer.currentPosition);
            if (!camera.orthographic)
                forward = camera.ScreenPointToRay(pointer.currentPosition).direction;//透视相机打开
            cPosition = MapGridUtils.GetIntersectionPoint(cPosition, forward);
            var delta = pPosition - cPosition;
            
            //边界检测 
            var screenPoint = camera.ViewportToScreenPoint(new Vector2(0.5f, 0.5f));
            var centerPos = camera.ScreenToWorldPoint(screenPoint);
            if (!camera.orthographic)
                forward = camera.ScreenPointToRay(screenPoint).direction;//透视相机打开
            centerPos = MapGridUtils.GetIntersectionPoint(centerPos, forward);

            var newCenterX = centerPos.x + delta.x;
            if (newCenterX > m_MapLimit.xMax && delta.x > 0)
                delta.x = 0;
            else if (newCenterX < m_MapLimit.xMin && delta.x < 0)
                delta.x = 0;
            camera.transform.AddPositionX(delta.x);
            
            var newCenterZ = centerPos.z + delta.z;
            if (newCenterZ > m_MapLimit.yMax && delta.z > 0)
                delta.z = 0;
            else if (newCenterZ < m_MapLimit.yMin && delta.z < 0)
                delta.z = 0;
            camera.transform.AddPositionZ(delta.z);

            if (m_ThrowSpeed.x != 0 && delta.x == 0)
                m_ThrowSpeed.x = 0;
            if (m_ThrowSpeed.y != 0 && delta.z == 0)
                m_ThrowSpeed.y = 0;
        }

        protected virtual void OnReleased(PointerActionInfo pointer)
        {
            // PointerActionInfo newPointer = new PointerActionInfo();
            // newPointer.startedOverUI = pointer.startedOverUI;
            // newPointer.currentPosition = pointer.currentPosition;
            //
            //
            // Debug.Log(pointer.currentPosition - pointer.previousPosition);
            // var speed = pointer.currentPosition - pointer.previousPosition;
            // float num = 1f;
            // m_CameraMoveTween = DOTween.To(() => num, x => num = x, 0, 0.8f).OnUpdate(() =>
            // {
            //     newPointer.previousPosition = newPointer.currentPosition;
            //     newPointer.currentPosition += speed * num; 
            //     OnDragged(newPointer);
            // });
            if(pointer.isDrag)
                ThrowCamera(pointer);
        }

        protected virtual void OnPressed(PointerActionInfo pointer)
        {
        }

        protected virtual void OnSpunWheel(WheelInfo obj)
        {
            if (m_Param == null || m_Param.camera == null) return;
            if (m_Param.camera.orthographic)
            {
                float size = m_Param.camera.orthographicSize;
                size += (-obj.zoomAmount) * m_Param.ZOOM_SPEED;

                size = Mathf.Clamp(size, m_Param.ZOOM_MIN, m_Param.ZOOM_MAX);
                m_Param.camera.orthographicSize = size;
            }
            else
            {
                m_Param.camera.fieldOfView = m_Param.ZOOM_Value_P;
                var cacheTrans = m_Param.camera.transform;

                if (cacheTrans.position.y < 0)
                    cacheTrans.SetPositionY(1);
                
                Vector3 forward =cacheTrans.forward;
                Vector3 targetPos = cacheTrans.position + forward * (m_Param.GetZoomSpeed(CameraComponent.ZoomPct) * obj.zoomAmount);
                
                var distance = Vector3.Distance(MapGridUtils.GetCameraLookAtWorldPosition(m_Param.camera), targetPos);
                if (distance < m_Param.ZOOM_MIN_P)
                {
                    targetPos += forward * (distance-m_Param.ZOOM_MIN_P);
                }
                else if(distance > m_Param.ZOOM_MAX_P)
                {
                    targetPos -= forward * (m_Param.ZOOM_MAX_P-distance);
                }

                GameEntry.Camera.CalculateZoom(distance);
                
                cacheTrans.transform.position = targetPos;
                
            }
        }
        
        private void ThrowCamera(PointerActionInfo pointer)
        {
            m_NewPointer = new PointerActionInfo();
            m_NewPointer.startedOverUI = pointer.startedOverUI;
            m_NewPointer.currentPosition = pointer.currentPosition;
            
            m_ThrowSpeed = pointer.currentPosition - pointer.previousPosition;

            m_ThrowProgress = 1f;
            m_CameraMoveTween = DOTween.To(() => m_ThrowProgress, x => m_ThrowProgress = x, 0, 0.5f).OnComplete(() =>
            {
                m_CameraMoveTween = null;
            });
        }
        
        private void BreakCameraThrow()
        {
            if (m_CameraMoveTween != null)
            {
                m_CameraMoveTween.Kill();
                m_CameraMoveTween = null;
            }
        }
    }
}
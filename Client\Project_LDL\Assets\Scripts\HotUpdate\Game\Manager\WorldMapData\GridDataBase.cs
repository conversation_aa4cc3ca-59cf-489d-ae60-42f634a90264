using Wmcamera;

namespace Game.Hotfix
{
    public class GridDataBase
    {
        public int PosX => m_PosX;
        public int PosY => m_PosY;
        public ulong RoleId => m_MapInfo.RoleId;
        public int ElementId => m_MapInfo.ElementId;
        protected MapInfo m_MapInfo;

        private int m_PosX;
        private int m_PosY;

        public GridDataBase(MapInfo mapInfo)
        {
            m_MapInfo = mapInfo;
            MapGridUtils.MapId2Pos(mapInfo.MapId, out m_PosX, out m_PosY);
        }
    }
    
    public class GridTownData:GridDataBase
    {
        public int Level {
            get
            {
                var t = m_MapInfo?.Role?.Level;
                if (t != null)
                    return (int)t;
                return 0;
            }
        }

        public GridTownData(MapInfo mapInfo):base(mapInfo)
        {
            
        }
    }

    public class GridMineData:GridDataBase
    {
        public GridMineData(MapInfo mapInfo):base(mapInfo)
        {
        }
    }
    
    public class GridMonsterData:GridDataBase
    {
        public GridMonsterData(MapInfo mapInfo):base(mapInfo)
        {
        }
    }
    
    public class GridFortsData:GridDataBase
    {
        public GridFortsData(MapInfo mapInfo):base(mapInfo)
        {
        }
    }
    
}
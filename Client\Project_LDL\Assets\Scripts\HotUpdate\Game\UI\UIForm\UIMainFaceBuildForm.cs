using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Event;
using NUnit.Framework;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using UIForm = Game.Hotfix.Config.uiform_config;

namespace Game.Hotfix
{
    public partial class UIMainFaceBuildForm : UGuiFormEx
    {
        private List<build_config> _buildList = new List<build_config>();
        //private List<BuildingModule> _buildModuleList = new List<BuildingModule>();
        private List<build_config> _showBuildList = new List<build_config>();
        private Dictionary<buildclass, List<build_config>> _buildDis = new Dictionary<buildclass, List<build_config>>();
        private buildclass _selectClass = buildclass.buildclass_development;
        private int m_SelectIndex = 0;
        private List<Sequence> m_TweenSequenceList;
        private bool isPlayTween = false;
        private Transform scrollViewContent;
        private bool isShowSelectTween = false;
        private Tween showSelectTween;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitConfig();
            InitUI();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            _selectClass = buildclass.buildclass_development;
            m_SelectIndex = 0;
            
            build_config selectBuildConfig = null;
            if (userData != null)
            {
                isShowSelectTween = true;
                selectBuildConfig = (build_config)userData;
                CheckSelectIndex(selectBuildConfig);
            }
            else
            {
                _showBuildList = GetBuildConfigListByType(_selectClass);
            }

            m_TweenSequenceList = new List<Sequence>();

            //_buildDis.TryGetValue(_selectClass, out _showBuildList);

            m_TableViewH.InitTableViewByIndex(m_SelectIndex);
            m_imgBtnIconLight_1.gameObject.SetActive(_selectClass == buildclass.buildclass_development);
            m_imgBtnIconLight_2.gameObject.SetActive(_selectClass == buildclass.buildclass_military);
            m_imgBtnIconLight_3.gameObject.SetActive(_selectClass == buildclass.buildclass_decoration);
            RefreshBuildRed();
            PlayTween();
            GameEntry.Event.Subscribe(OnNewBuildingCreateEventArgs.EventId, OnBuildingCreate);
        }
        
        private void OnBuildingCreate(object sender, GameEventArgs e)
        {
            var ne = (OnNewBuildingCreateEventArgs)e;
            if (ne != null)
            {
                RefreshBuildRed();
                _showBuildList = GetBuildConfigListByType(_selectClass);
                m_TableViewH.ReloadData();
            }
        }
        
        private int GetBuildingRed(buildclass value)
        {
            int totalRedNum = 0;
            List<build_config> buildConfigs = GameEntry.LogicData.BuildingData.GetUnLockBuildConfigListByType(value);
            if (buildConfigs.Count > 0)
            {
                totalRedNum += buildConfigs.Count;
            } 

            return totalRedNum;
        }

        protected void RefreshBuildRed()
        {
            int redCount1 = GetBuildingRed(buildclass.buildclass_development);
            int redCount2 = GetBuildingRed(buildclass.buildclass_military);
            int redCount3 = GetBuildingRed(buildclass.buildclass_decoration);
            m_imgBuildRed_1.gameObject.SetActive(redCount1 > 0);
            m_txtBuildRed_1.text = redCount1.ToString();
            m_imgBuildRed_2.gameObject.SetActive(redCount2 > 0);
            m_txtBuildRed_2.text = redCount2.ToString();
            m_imgBuildRed_3.gameObject.SetActive(redCount3 > 0);
            m_txtBuildRed_3.text = redCount3.ToString();
        }

        protected void CheckSelectIndex(build_config selectBuildConfig)
        {
            if (selectBuildConfig == null)
            {
                return;
            }
            _selectClass = selectBuildConfig.build_type2;
            List<build_config> list;
            _showBuildList = GetBuildConfigListByType(_selectClass);

            for (int i = 0; i < _showBuildList.Count; i++)
            {
                if (_showBuildList[i].build_type == selectBuildConfig.build_type)
                {
                    m_SelectIndex = i;
                    return;
                }
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            _selectClass = buildclass.buildclass_development;
            m_SelectIndex = 0;
            isPlayTween = false;
            isShowSelectTween = false;
            m_TweenSequenceList.Clear();
            scrollViewContent.gameObject.DestroyAllChild();
            showSelectTween.Restart();
            Game.GameEntry.Event.Unsubscribe(OnNewBuildingCreateEventArgs.EventId,OnBuildingCreate);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnExitClick()
        {
            Close();
        }
        
        protected List<build_config> GetBuildConfigListByType(buildclass selectType)
        {
            int count = 0;
            List<build_config> buildConfigList = new List<build_config>();
            List<build_config> list;
            bool isExist = _buildDis.TryGetValue(selectType,out list);
            if (isExist)
            {
                foreach (build_config buildConfig in list)
                {
                    bool exists = buildConfigList.Exists(b => b.build_type == buildConfig.build_type);
                    if (!exists)
                    {
                        buildConfigList.Add(buildConfig);
                    }
                }

                buildConfigList.Sort((a,b) =>
                {
                    bool aCanBuild = GameEntry.LogicData.BuildingData.CheckBuildingCanBuildByType(selectType, a.build_type);
                    bool bCanBuild = GameEntry.LogicData.BuildingData.CheckBuildingCanBuildByType(selectType, b.build_type);
                    int aCanBuildWeight = aCanBuild ? 100 : 0;
                    int bCanBuildWeight = bCanBuild ? 100 : 0;
                    
                    return bCanBuildWeight.CompareTo(aCanBuildWeight);
                });
            }
            
            return buildConfigList;
        }
        
        // 刷新列表元素
        protected void UpdataBuildView(int index, GameObject obj)
        {
            build_config buildConfig = _showBuildList[index];
            if (buildConfig == null)
            {
                return;
            }

            Image bg                  = obj.transform.Find("tween/bg").GetComponent<Image>();
            Image jiantouTween        = obj.transform.Find("tween/jiantouTween").GetComponent<Image>();
            Text txtName              = obj.transform.Find("tween/name").GetComponent<Text>();
            Image buildingIcon        = obj.transform.Find("tween/buildingIcon").GetComponent<Image>();
            Text txtBuildDetail       = obj.transform.Find("tween/txtBuildDetail").GetComponent<Text>();
            GameObject normalRoot     = obj.transform.Find("tween/normalRoot").gameObject;
            Text costNun_1            = obj.transform.Find("tween/normalRoot/costNun_1").GetComponent<Text>();
            Text costNun_2            = obj.transform.Find("tween/normalRoot/costNun_2").GetComponent<Text>();
            Text txtCostTime          = obj.transform.Find("tween/normalRoot/txtCostTime").GetComponent<Text>();
            Text txtHaveBuilding      = obj.transform.Find("tween/normalRoot/txtHaveBuilding").GetComponent<Text>();
            Text txtCostTimeNum       = obj.transform.Find("tween/normalRoot/detailBg/txtCostTimeNum").GetComponent<Text>();
            Text txtHaveBuildingNum   = obj.transform.Find("tween/normalRoot/detailBg/txtHaveBuildingNum").GetComponent<Text>();
            GameObject unLockRoot     = obj.transform.Find("tween/unLockRoot").gameObject;
            Text desc                 = obj.transform.Find("tween/unLockRoot/desc").GetComponent<Text>();
            GameObject newFlag        = obj.transform.Find("tween/newFlag").gameObject;
            Text txtNew               = obj.transform.Find("tween/newFlag/txtNew").GetComponent<Text>();
            Button btn                = obj.transform.Find("tween/btnBuild").GetComponent<Button>();
            var nameConfig = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.language_config>(buildConfig.name);
            if (nameConfig != null)
            {
                txtName.text = nameConfig.GetLang();
            }
            buildingIcon.SetImage(buildConfig.picture);
            txtCostTime.text = ToolScriptExtend.GetLang(1100138);
            txtHaveBuilding.text = ToolScriptExtend.GetLang(1100139);
            txtBuildDetail.text = ToolScriptExtend.GetLang(buildConfig.describe);

            jiantouTween.gameObject.SetActive(false);
            if (isShowSelectTween && index == m_SelectIndex)
            {
                jiantouTween.gameObject.SetActive(true);
                float positionY = jiantouTween.transform.localPosition.y;
                showSelectTween = jiantouTween.transform.DOLocalMoveY(positionY - 40,0.8f).SetLoops(-1,LoopType.Yoyo).SetEase(Ease.Linear).OnKill((
                    () =>
                    {
                        jiantouTween.gameObject.SetActive(false);
                    }));
            }
            
            bool isCanBuild = GameEntry.LogicData.BuildingData.CheckBuildingCanBuildByType(buildConfig.build_type2,buildConfig.build_type);
            build_level buildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildConfig.build_type,1);

            normalRoot.SetActive(isCanBuild);
            unLockRoot.SetActive(!isCanBuild);

            string bgPath = isCanBuild
                ? "Sprite/ui_mainface/jianzhu_win_xuan1.png"
                : "Sprite/ui_mainface/jianzhu_win_xuan2.png";
            bg.SetImage(bgPath,false);
            bool isMax = false;
            if (isCanBuild)
            {
                for (int i = 0; i < buildingLevelCfg?.build_cost.Count; i++)
                {
                    if (buildingLevelCfg?.build_cost[i].item_id == itemid.itemid_2)
                    {
                        costNun_2.text = buildingLevelCfg?.build_cost[i].num.ToString();
                    }else if (buildingLevelCfg?.build_cost[i].item_id ==  itemid.itemid_3)
                    {
                        costNun_1.text = buildingLevelCfg?.build_cost[i].num.ToString();
                    }
                }
                txtCostTimeNum.text = buildingLevelCfg != null ? buildingLevelCfg.time.ToString() : "0";
                int maxLimit;
                List<build_config> unLockList;
                GameEntry.LogicData.BuildingData.GetUnLockLimitMax(buildConfig.build_type,out maxLimit,out unLockList);
                if (maxLimit == 0)
                {
                    maxLimit = 1;
                }

                int curBuildCount = GameEntry.LogicData.BuildingData.GetBuildingCountByBuildType(unLockList);
                txtHaveBuildingNum.text = $"{curBuildCount}/{maxLimit}";
            }
            else
            {
                build_config nextLockBuildConfig = GameEntry.LogicData.BuildingData.GetBuildingNextLockBuilding(buildConfig.build_type2, buildConfig.build_type);
                isMax = nextLockBuildConfig == null;
                string txtStr = String.Empty;
                if (isMax)
                {
                    txtStr = ToolScriptExtend.GetLang(1100125);
                }
                else
                {
                    int demandId = nextLockBuildConfig.build_demand.Count > 0 ? nextLockBuildConfig.build_demand[0] : 0;
                    demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    if (demandConfig != null && demandConfig.build_demand != null)
                    {
                        string bName = GameEntry.LogicData.BuildingData.GetBuildingNameByBuildType(demandConfig.build_demand.build_type_demand);

                        int buildIndex = buildConfig.id % 100;
                        if (buildIndex == 1)
                        {
                            txtStr = ToolScriptExtend.GetLangFormat(1100123,bName,demandConfig.build_demand.build_level_demand.ToString());
                        }else
                        {
                            txtStr = ToolScriptExtend.GetLangFormat(1100124,bName,demandConfig.build_demand.build_level_demand.ToString(),buildIndex.ToString());
                        }
                    }
                    else if (demandConfig != null && demandConfig.dungeon_demand != 0)
                    {
                        txtStr = ToolScriptExtend.GetLangFormat(1091, demandConfig.dungeon_demand.ToString());
                    }
                    
                }

                desc.text = txtStr;
            }

            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() =>
            {
                if (!isCanBuild)
                {
                    if (isMax)
                    {
                        GameEntry.LogicData.BuildingData.FindBuildingByType(buildConfig.build_type);
                        return;
                    }
                    int demandId = buildConfig?.build_demand.Count > 0 ? buildConfig.build_demand[0] : 0;
                    demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    if (demandConfig != null && demandConfig.build_demand != null)
                    {
                        GameEntry.LogicData.BuildingData.FindBuildingByLevelMax(demandConfig.build_demand.build_type_demand,true);
                    }
                    else if (demandConfig != null && demandConfig.dungeon_demand != null)
                    {
                        
                    }
                    else
                    {
                        GameEntry.LogicData.BuildingData.FindBuildingByLevelMin(false);
                    }
                    return;
                }
                bool isEnough = GameEntry.LogicData.BuildingData.CheckResourceIsEnough(buildConfig.build_type, 1);
                if (isEnough)
                {
                    showSelectTween.Kill();
                    build_config nextLockBuildConfig = GameEntry.LogicData.BuildingData.GetBuildingNextLockBuilding(buildConfig.build_type2, buildConfig.build_type);
                    if (nextLockBuildConfig != null)
                    {
                        GameEntry.CityMap.Build(nextLockBuildConfig.id, 1);
                    }

                    Close(true);
                }
                
            });
        }
        
        private void PlayTween()
        {
            if (isPlayTween)
            {
                return;
            }
            
            Sequence sequence1 = DOTween.Sequence();            
            CanvasGroup bgCanvasGroup = m_goTween.GetComponent<CanvasGroup>();
            m_goTween.transform.localScale = new Vector3(2, 2, 2);
            sequence1.Insert(0, bgCanvasGroup.DOFade(1, 0.3f));
            sequence1.Insert(0, m_goTween.transform.DOScale(1,0.2f));
            int childCount = scrollViewContent.childCount;
            for (int i = 0; i < childCount; i++)
            {
                var child = scrollViewContent.GetChild(i);
                GameObject tweenObj = child.Find("tween").gameObject;
                Transform tweenTransform = tweenObj.transform;
                float positionX = tweenTransform.localPosition.x;
                float positionY = tweenTransform.localPosition.y;
                tweenTransform.localScale = new Vector3(1.05f, 1.05f, 1.05f);
                //tweenTransform.localPosition = new Vector3(positionX - 50,tweenTransform.localPosition.y,tweenTransform.localPosition.z);
                CanvasGroup objCanvasGroup = tweenTransform.GetComponent<CanvasGroup>();
                objCanvasGroup.alpha = 0;
                
                var timeKey = "UIMainFaceBuildForm" + i;
                Timers.Instance.Remove(timeKey);
                Timers.Instance.Add(timeKey, i * 0.1f, (param) =>
                {
                    Sequence sequence2 = DOTween.Sequence();
                    m_TweenSequenceList.Add(sequence2);
                    sequence2.Join( objCanvasGroup.DOFade(1, 0.2f).SetEase(Ease.Linear));
                    //sequence2.Join( tweenTransform.DOLocalMoveY(positionY+10, 0.2f).SetEase(Ease.Linear).SetLoops(1));
                    //sequence2.Insert( 0,tweenTransform.DOScale(1f, 0.1f).SetEase(Ease.InQuad));
                    sequence2.Join(tweenTransform.DOScale(0.95f, 0.1f).SetEase(Ease.InOutSine));
                    sequence2.Insert(0.15f,tweenTransform.DOScale(1f, 0.1f).SetEase(Ease.Linear));
                    sequence2.OnComplete(()=>
                    {
                        m_TweenSequenceList.Remove(sequence2);
                        isPlayTween = true;
                    });
                });
                
            }

            // Transform tweenTransform   = obj.transform.Find("tween").GetComponent<Transform>();
            // tweenTransform.localScale = new Vector3(3, 3, 3);
            // CanvasGroup objCanvasGroup = tweenTransform.GetComponent<CanvasGroup>();
            // objCanvasGroup.alpha = 0;
            // float alphaTime = 0.2f;
            // Sequence sequence2 = DOTween.Sequence();
            // m_TweenSequenceList.Add(sequence2);
            // sequence2.AppendInterval(index + 0.1f);
            // sequence2.Insert(0, objCanvasGroup.DOFade(1, alphaTime));
            // sequence2.Insert(0, tweenTransform.DOScale(0.95f, 0.3f).SetEase(Ease.Linear));
            // sequence2.Insert(0.3f,tweenTransform.DOScale(1f, 0.3f).SetEase(Ease.Linear));
            // sequence2.OnComplete(()=>
            // {
            //     objCanvasGroup.alpha = 1;
            //     m_TweenSequenceList.Remove(sequence2);
            // });
        }

        private void InitUI()
        {
            m_txtBtnNormal_1.text = ToolScriptExtend.GetLang(1100004);
            m_txtBtnNormal_2.text = ToolScriptExtend.GetLang(1100005);
            m_txtBtnNormal_3.text = ToolScriptExtend.GetLang(1100006);
            m_txtBtnLight_1.text  = ToolScriptExtend.GetLang(1100004);
            m_txtBtnLight_2.text  = ToolScriptExtend.GetLang(1100005);
            m_txtBtnLight_3.text  = ToolScriptExtend.GetLang(1100006);
            
            GameObject obj = transform.Find("buildItem").gameObject;
            m_TableViewH.GetItemCount = () => { 
                return _showBuildList.Count; 
            };
            m_TableViewH.GetItemGo = () =>
            {
                return obj;
            };
            m_TableViewH.UpdateItemCell = UpdataBuildView;
            scrollViewContent = m_TableViewH.transform.Find("Viewport/Content").transform;
        }
        
        private void InitConfig()
        {
            
            _buildList = Game.GameEntry.LDLTable.GetTable<build_config>();
            foreach (var buildConfig in _buildList)
            {
                if (!_buildDis.ContainsKey(buildConfig.build_type2))
                {
                    _buildDis[buildConfig.build_type2] = new List<build_config>();
                }
                _buildDis[buildConfig.build_type2].Add(buildConfig);
            }
        }

        private void OnBtnBuildType_1Click()
        {
            OnBuldTypeBtnClick(buildclass.buildclass_development);
        }       
        private void OnBtnBuildType_2Click()
        {
            OnBuldTypeBtnClick(buildclass.buildclass_military);
        }
        private void OnBtnBuildType_3Click()
        {
            OnBuldTypeBtnClick(buildclass.buildclass_decoration);
        }

        private void OnBuldTypeBtnClick(buildclass buildType)
        {
            Debug.Log("onclick type :" + buildType);
            m_imgBtnIconLight_1.gameObject.SetActive(buildType == buildclass.buildclass_development);
            m_imgBtnIconLight_2.gameObject.SetActive(buildType == buildclass.buildclass_military);
            m_imgBtnIconLight_3.gameObject.SetActive(buildType == buildclass.buildclass_decoration);
            _selectClass = buildType;
            _showBuildList = GetBuildConfigListByType(_selectClass);
            m_TableViewH.ReloadData();
        }
    }
}

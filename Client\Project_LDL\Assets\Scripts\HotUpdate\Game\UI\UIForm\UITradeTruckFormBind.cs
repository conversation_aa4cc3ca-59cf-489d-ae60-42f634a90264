using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTruckForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnAddCount;
        [SerializeField] private UIButton m_btnReset;
        [SerializeField] private UIButton m_btnHistory;
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnPlunderOther;
        [SerializeField] private UIButton m_btnViewMy;
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnCardLeft;
        [SerializeField] private UIButton m_btnCardRight;

        [SerializeField] private UIText m_txtTodayTradeCount;
        [SerializeField] private UIText m_txtTodayPlunderCount;
        [SerializeField] private UIText m_txtTruckCardArriveTime;
        [SerializeField] private UIText m_txtTruckCardName;
        [SerializeField] private UIText m_txtTruckCardLevel;
        [SerializeField] private UIText m_txtTruckCardPower;

        [SerializeField] private UIImage m_imgTruckCardQualityBg;
        [SerializeField] private UIImage m_imgTruckCardIcon;
        [SerializeField] private UIImage m_imgTruckCardQuality;

        [SerializeField] private UIToggle m_togFilterTruck;
        [SerializeField] private UIToggle m_togOtherTruck;
        [SerializeField] private UIToggle m_togMyTruck;

        [SerializeField] private Transform m_transTruckSelected;
        [SerializeField] private GameObject m_goNoPlunderTruck;
        [SerializeField] private GameObject m_goNoTradeCount;
        [SerializeField] private GameObject m_goMyTruck;
        [SerializeField] private GameObject m_goOtherTruck;
        [SerializeField] private GameObject m_goTruckItem;
        [SerializeField] private GameObject m_goOtherTrainItem;
        [SerializeField] private GameObject m_goMyTrainItem;
        [SerializeField] private GameObject m_goPlayerInfo;
        [SerializeField] private GameObject m_goPlayerInfoItem;
        [SerializeField] private GameObject m_goMyTrade;
        [SerializeField] private GameObject m_goMyPlunder;
        [SerializeField] private GameObject m_goTruckCard;
        [SerializeField] private Transform m_transReward;
        [SerializeField] private Transform m_transContentReward;

        void InitBind()
        {
            m_btnAddCount.onClick.AddListener(OnBtnAddCountClick);
            m_btnReset.onClick.AddListener(OnBtnResetClick);
            m_btnHistory.onClick.AddListener(OnBtnHistoryClick);
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnPlunderOther.onClick.AddListener(OnBtnPlunderOtherClick);
            m_btnViewMy.onClick.AddListener(OnBtnViewMyClick);
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnCardLeft.onClick.AddListener(OnBtnCardLeftClick);
            m_btnCardRight.onClick.AddListener(OnBtnCardRightClick);
        }
    }
}

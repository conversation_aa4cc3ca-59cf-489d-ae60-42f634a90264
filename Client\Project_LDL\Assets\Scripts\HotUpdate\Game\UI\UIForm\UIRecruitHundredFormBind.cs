using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitHundredForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;

        [SerializeField] private UIText m_txtDesc;

        [SerializeField] private ScrollRect m_scrollviewList;

        [SerializeField] private Toggle m_togSkip;

        [SerializeField] private GameObject m_goEffect;
        [SerializeField] private GameObject m_goTitle;
        [SerializeField] private GameObject m_goDiamond;
        [SerializeField] private GameObject m_goTopItem;
        [SerializeField] private GameObject m_goContent;
        [SerializeField] private GameObject m_goMain;
        [SerializeField] private GameObject m_goOther;
        [SerializeField] private GameObject m_goMask;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goSingleCard;
        [SerializeField] private GameObject m_goSurvivorCard;
        [SerializeField] private GameObject m_goNormalSingleCard;
        [SerializeField] private GameObject m_goNormalSurvivorCard;
        [SerializeField] private GameObject m_goReward;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
        }
    }
}

using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using Wmcamera;

namespace Game.Hotfix
{
    public class WorldMapGridData
    {
        public List<Vector3Int> CityData => m_CityData;
        public List<GridTownData> TownData => m_TownData;
        public List<GridMonsterData> MonsterData => m_MonsterData;
        public List<GridFortsData> FortsData => m_FortsData;
        public List<GridMineData> MineData => m_MineData;
        
        private int m_GridX;
        private int m_GridY;
        private int m_LogicId;
        
        /// <summary>
        /// 城市数据
        /// </summary>
        private List<Vector3Int> m_CityData;

        private List<GridTownData> m_TownData;
        private List<GridMonsterData> m_MonsterData;
        private List<GridFortsData> m_FortsData;
        private List<GridMineData> m_MineData;
        
        public static WorldMapGridData Create(int x, int y)
        {
            var t = new WorldMapGridData(x,y);
            return t;
        }

        private WorldMapGridData(int x, int y)
        {
            m_GridX = x;
            m_GridY = y;
            m_LogicId = MapGridUtils.GridId2ScreenId(m_GridX, m_GridY);

            m_CityData = new List<Vector3Int>();
            m_TownData = new List<GridTownData>();
            m_MonsterData = new List<GridMonsterData>();
            m_FortsData = new List<GridFortsData>();
            m_MineData = new List<GridMineData>();
        }

        public void AddCity(int posX,int posY,int cityId)
        {
            var data = new Vector3Int(posX, posY, cityId);
            m_CityData.Add(data);
        }

        public void AddMonster(MapInfo mapInfo)
        {
            var data = new GridMonsterData(mapInfo);
            m_MonsterData.Add(data);
        }

        public void AddForts(MapInfo mapInfo)
        {
            var data = new GridFortsData(mapInfo);
            m_FortsData.Add(data);
        }

        public void AddMine(MapInfo mapInfo)
        {
            var data = new GridMineData(mapInfo);
            AddMine(data);
        }
        
        public void AddMine(GridMineData data)
        {
            MineData.Add(data);
        }
        
        public void AddTown(MapInfo mapInfo)
        {
            var data = new GridTownData(mapInfo);
            AddTown(data);
        }
        
        public void AddTown(GridTownData data)
        {
            TownData.Add(data);
        }
        
        public List<Rect> GetUnBuildArea()
        {
            List<Rect> list = new List<Rect>();
            //city
            foreach (var cityData in m_CityData)
            {
                Rect rect = new Rect(cityData.x, cityData.y, GameDefine.WorldMapCitySize.x,
                    GameDefine.WorldMapCitySize.y);
                list.Add(rect);
            }
            //town
            foreach (var townData in m_TownData)
            {
                Rect rect = new Rect(townData.PosX, townData.PosY, GameDefine.WorldMapTownSize.x,
                    GameDefine.WorldMapTownSize.y);
                list.Add(rect);
            }

            //mine
            foreach (var data in m_MineData)
            {
                Rect rect = new Rect(data.PosX, data.PosY, 1, 1);
                list.Add(rect);
            }
            
            return list;
        }

        public void Clear()
        {
            TownData.Clear();
            ClearElement();
        }
        
        public void ClearElement()
        {
            MonsterData.Clear();
            FortsData.Clear();
            MineData.Clear();
        }
        
    }
}

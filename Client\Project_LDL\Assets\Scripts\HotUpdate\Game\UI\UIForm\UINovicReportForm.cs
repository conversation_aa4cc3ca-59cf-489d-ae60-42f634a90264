
using System.Collections.Generic;
using GameFramework.Event;
using Novice;
using Peak;
using UnityEngine;
using UnityEngine.UI;


namespace Game.Hotfix
{
    public partial class UINovicReportForm : UGuiFormEx
    {
        public PeakArenaTabType type;
        public List<NoviceReport> reportListData = new List<NoviceReport>();
        public List<PeakReport> peakReportListData = new List<PeakReport>();
        public List<ArenaScrollData> playerListData = new List<ArenaScrollData>();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_TableViewV.GetItemGo = GetItemGo;
            m_TableViewV.UpdateItemCell = UpdateItemCell;
        }
        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            //RequestReportInfo
            GameEntry.Event.Subscribe(NoviceCompRefreshEventArgs.EventId, OnNoviceCompRefresh);
            type = (PeakArenaTabType)userData;
            switch (type)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营数据
                    m_btnAddSoldierTime.gameObject.SetActive(true); 
                    GameEntry.LogicData.NoviceTrainingData.RefreshRepotr((resp) =>
                    {
                        if (resp != null)
                        {
                            var data = resp.ReportList;
                            reportListData.Clear();
                            foreach (var item in resp.ReportList)
                            {
                                reportListData.Add(item);
                            }
                            //InitScrollData();
                            Debug.Log("[ArenaChallenge] 获取挑战记录成功");
                            m_TableViewV.GetItemCount = GetItemCount;
                            // 重新加载TableView数据
                            m_TableViewV.InitTableViewByIndex(0);
                        }
                        else
                        {
                            Debug.LogError("[ArenaChallenge] 获取挑战记录失败");
                            // 可以添加失败提示
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = "error"//ToolScriptExtend.GetLang(1100292)
                            });
                        }
                        OnRefreshPanel();
                    });
                    break;
                case PeakArenaTabType.two:
                    m_btnAddSoldierTime.gameObject.SetActive(false);
                    GameEntry.LogicData.PeakRankData.RequestReportInfo((resp) =>
                    {
                        if (resp != null)
                        {
                            var data = resp.ReportList;
                            peakReportListData.Clear();
                            foreach (var item in resp.ReportList)
                            {
                                peakReportListData.Add(item);
                            }
                            //InitScrollData();
                            Debug.Log("[ArenaChallenge] 获取挑战记录成功");
                            m_TableViewV.GetItemCount =() => peakReportListData.Count;
                            // 重新加载TableView数据
                            m_TableViewV.InitTableViewByIndex(0);
                        }
                        else
                        {
                            Debug.LogError("[ArenaChallenge] 获取挑战记录失败");
                            // 可以添加失败提示
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = "error"//ToolScriptExtend.GetLang(1100292)
                            });
                        }
                        OnRefreshPanel();
                    });
                    // 第二个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.three:
                    // 第三个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.four:
                    // 第四个页签的数据加载逻辑
                    break;
            }
            
        }
        
        public void OnNoviceCompRefresh(object sender, GameEventArgs e)
        {
            OnRefreshPanel();
        }
        public void OnRefreshPanel()
        {
            switch (type)
            {
                case PeakArenaTabType.NewSoldier:
                    // 新兵训练营数据
                    if (reportListData.Count > 0)
                    {
                        m_txtDes.text = " ";
                    }
                    else
                    {
                        m_txtDes.text = ToolScriptExtend.GetLang(1100385);
                    }
                    GameEntry.LogicData.NoviceTrainingData.RequestPersonInfo((personInfo) =>
                    {
                        if (personInfo != null)
                        {
                            // 获取最大挑战次数和已使用次数
                            int maxChallengeTimes = GameEntry.LogicData.NoviceTrainingData.GetMaxChallenges(); // 默认最大挑战次数，可以从配置表获取
                            int usedTimes = (int)personInfo.UsedFightTimes;
                            int remainingTimes = maxChallengeTimes - usedTimes;

                            // 更新UI显示
                            if (m_txtChallenges != null)
                            {
                                string txtDes = ToolScriptExtend.GetLang(1100356);
                                m_txtChallenges.text = txtDes + remainingTimes;
                            }
                        }
                    });
                    break;
                case PeakArenaTabType.two:
                    // 第二个页签的数据加载逻辑
                    if (peakReportListData.Count > 0)
                    {
                        m_txtDes.text = " ";
                    }
                    else
                    {
                        m_txtDes.text = ToolScriptExtend.GetLang(1100385);
                    }
                    int maxChallengeTimes = GameEntry.LogicData.PeakRankData.GetMaxChallenges();
                    int remainingTimes = GameEntry.LogicData.PeakRankData.GetRemainingChallenges();

                    // 更新UI显示
                    if (m_txtChallenges != null)
                    {
                        string txtDes = ToolScriptExtend.GetLang(1100356);
                        m_txtChallenges.text = txtDes + remainingTimes;
                    }
                    break;
                case PeakArenaTabType.three:
                    // 第三个页签的数据加载逻辑
                    break;
                case PeakArenaTabType.four:
                    // 第四个页签的数据加载逻辑
                    break;
            }

          
        }
        public int GetItemCount()
        {
            return reportListData.Count;
        }
        public GameObject GetItemGo()
        {
            return m_goItem;
        }
        private void UpdateItemCell(int index, GameObject cellObj)
        {
            switch (type)
            {
                case PeakArenaTabType.NewSoldier:
                    UpdateNoviceReportItem(index, cellObj);
                    break;
                case PeakArenaTabType.two:
                    UpdatePeakReportItem(index, cellObj);
                    break;
                case PeakArenaTabType.three:
                    // 处理第三种类型的报告
                    break;
                case PeakArenaTabType.four:
                    // 处理第四种类型的报告
                    break;
                default:
                    Debug.LogWarning($"[UINovicReportForm] 未处理的类型: {type}");
                    break;
            }
        }
        public void UpdatePeakReportItem(int index, GameObject cellObj)
        {
            var data = peakReportListData[index];

            var successNode = cellObj.transform.Find("successNode");
            var failNode = cellObj.transform.Find("failNode");
            var txtResult = cellObj.transform.Find("successNode/txtResult")?.GetComponent<Text>();
            var txtResult2 = cellObj.transform.Find("failNode/txtResult")?.GetComponent<Text>();
            var txtUp = cellObj.transform.Find("successNode/txtUp")?.GetComponent<Text>();
            UIButton Button = cellObj.transform.Find("Button")?.GetComponent<UIButton>();
            successNode.gameObject.SetActive(data.IsWin);
            failNode.gameObject.SetActive(!data.IsWin);
            if(Button != null && !string.IsNullOrEmpty(data.VideoId))
            {
                Button.onClick.RemoveAllListeners();
                Button.onClick.AddListener(() =>
                {
                    // 处理点击事件
                    GameEntry.LogicData.Battle5v5Data.GoBattleReplay(data.VideoId);
                });
            }
            if (data.ReportType == 1)
            {
                //进攻
                txtResult.text = ToolScriptExtend.GetLang(1100381);
                txtResult2.text = ToolScriptExtend.GetLang(1100382);
            }
            else
            {
                //防守
                txtResult.text = ToolScriptExtend.GetLang(1100383);
                txtResult2.text = ToolScriptExtend.GetLang(1100384);
            }
            if (data.OldRank - data.NewRank > 0)
            {
                txtUp.text = (data.OldRank - data.NewRank).ToString();
            }
            else
            {
                txtUp.text = "";
            }
            // 时间
            var txtTime = cellObj.transform.Find("txtTime")?.GetComponent<Text>();
            if (txtTime != null) txtTime.text = TimeHelper.ToDateTimeText(data.Time);

            ArenaScrollData playerInfo = new ArenaScrollData();
            if (playerListData.Count > index && playerListData[index] != null)
            {
                playerInfo = playerListData[index];
                // 获取并设置名称
                var txtName = cellObj.transform.Find("playerName")?.GetComponent<Text>();
                if (txtName != null) txtName.text = playerInfo.Name;

                // 设置头像
                var imgHead = cellObj.transform.Find("playerIcon")?.GetComponent<Image>();
                if (imgHead != null && !string.IsNullOrEmpty(playerInfo.HeadPath))
                {
                    imgHead.SetImage(playerInfo.HeadPath);
                }
                // 获取并设置战力
                var txtPower = cellObj.transform.Find("fight")?.GetComponent<Text>();
                if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(playerInfo.Power);
            }
            else
            {
                playerInfo.Initialize(0, data.FightRoleId, false, (int)GameEntry.RoleData.ServerId, (info) =>
                {
                    playerListData.Add(info);
                    playerInfo = info;
                    // 获取并设置名称
                    var txtName = cellObj.transform.Find("playerName")?.GetComponent<Text>();
                    if (txtName != null) txtName.text = playerInfo.Name;

                    // 设置头像
                    var imgHead = cellObj.transform.Find("playerIcon")?.GetComponent<Image>();
                    if (imgHead != null && !string.IsNullOrEmpty(playerInfo.HeadPath))
                    {
                        imgHead.SetImage(playerInfo.HeadPath);
                    }
                    // 获取并设置战力
                    var txtPower = cellObj.transform.Find("fight")?.GetComponent<Text>();
                    if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(playerInfo.Power);
                });
            }
           
        }
        /// <summary>
        /// 更新新兵训练营战报项
        /// </summary>
        private void UpdateNoviceReportItem(int index, GameObject cellObj)
        {
            if (index >= reportListData.Count)
            {
                Debug.LogError($"[UINovicReportForm] 索引越界: {index}, 列表长度: {reportListData.Count}");
                return;
            }

            var data = reportListData[index];
            if (data.IsRobot)
            {
                RobotData robotData = RobotManager.GetRobotById((int)data.FightRoleId);

                // 获取并设置名称
                var txtName = cellObj.transform.Find("playerName")?.GetComponent<Text>();
                if (txtName != null) txtName.text = robotData.Name;

                // 设置头像
                var imgHead = cellObj.transform.Find("playerIcon")?.GetComponent<Image>();
                if (imgHead != null && !string.IsNullOrEmpty(robotData.HeadPath))
                {
                    imgHead.SetImage(robotData.HeadPath);
                }
                // 时间
                var txtTime = cellObj.transform.Find("txtTime")?.GetComponent<Text>();
                if (txtTime != null) txtTime.text = TimeHelper.ToDateTimeText(data.Time);


                // 获取并设置战力
                var txtPower = cellObj.transform.Find("fight")?.GetComponent<Text>();
                if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(robotData.Power);
                
            }
            else
            {
                ArenaScrollData playerInfo = new ArenaScrollData();
                if (playerListData.Count > index && playerListData[index] != null)
                {
                    playerInfo = playerListData[index];
                     // 获取并设置名称
                    var txtName = cellObj.transform.Find("playerName")?.GetComponent<Text>();
                    if (txtName != null) txtName.text = playerInfo.Name;

                    // 设置头像
                    var imgHead = cellObj.transform.Find("playerIcon")?.GetComponent<Image>();
                    if (imgHead != null && !string.IsNullOrEmpty(playerInfo.HeadPath))
                    {
                        imgHead.SetImage(playerInfo.HeadPath);
                    }
                    // 获取并设置战力
                    var txtPower = cellObj.transform.Find("fight")?.GetComponent<Text>();
                    if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(playerInfo.Power);
                }
                else
                {
                    playerInfo.Initialize(0, data.FightRoleId, false, (int)GameEntry.RoleData.ServerId, (info) =>
                    {
                        playerListData.Add(info);
                        playerInfo = info;
                        // 获取并设置名称
                        var txtName = cellObj.transform.Find("playerName")?.GetComponent<Text>();
                        if (txtName != null) txtName.text = playerInfo.Name;

                        // 设置头像
                        var imgHead = cellObj.transform.Find("playerIcon")?.GetComponent<Image>();
                        if (imgHead != null && !string.IsNullOrEmpty(playerInfo.HeadPath))
                        {
                            imgHead.SetImage(playerInfo.HeadPath);
                        }
                        // 获取并设置战力
                        var txtPower = cellObj.transform.Find("fight")?.GetComponent<Text>();
                        if (txtPower != null) txtPower.text = ToolScriptExtend.FormatNumberWithUnit(playerInfo.Power);
                    });
                }
             
            }
            var successNode = cellObj.transform.Find("successNode");
            var failNode = cellObj.transform.Find("failNode");
            var txtResult = cellObj.transform.Find("successNode/txtResult")?.GetComponent<Text>();
            var txtResult2 = cellObj.transform.Find("failNode/txtResult")?.GetComponent<Text>();
            var txtUp = cellObj.transform.Find("successNode/txtUp")?.GetComponent<Text>();
            successNode.gameObject.SetActive(data.IsWin);
            failNode.gameObject.SetActive(!data.IsWin);
            
            if (data.ReportType == 1)
            {
                //进攻
                txtResult.text = ToolScriptExtend.GetLang(1100381);
                txtResult2.text = ToolScriptExtend.GetLang(1100382);
            }
            else
            {
                //防守
                txtResult.text = ToolScriptExtend.GetLang(1100383);
                txtResult2.text = ToolScriptExtend.GetLang(1100384);
            }
            if (data.OldRank - data.NewRank > 0)
            {
                txtUp.text = (data.OldRank - data.NewRank).ToString();
            }
           
        }
        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            playerListData.Clear();
            GameEntry.Event.Unsubscribe(NoviceCompRefreshEventArgs.EventId, OnNoviceCompRefresh);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnAddSoldierTimeClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIChallengeBuyForm);
        }
    }
}

using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroBattleBuffCtrl
    {
        private BattleHero m_BattleHero;
        private List<HeroBattleBuff> m_HeroBuffList;
        private BattleEffectCtrl m_EffectCtrl;

        private Dictionary<int, BattleEffect> m_Effects;
        
        public HeroBattleBuffCtrl(BattleFiled battleFiled,BattleHero battleHero)
        {
            m_BattleHero = battleHero;
            m_EffectCtrl = battleFiled.EffectCtrl;
            
            m_HeroBuffList = new List<HeroBattleBuff>();
            m_Effects = new Dictionary<int, BattleEffect>();
        }

        public void Init()
        {
            
        }
        
        public void AddBuff(long buffIdNum)
        {
            int buffId, durationFrame, buffUid;
            ParseNumToId(buffIdNum, out buffId, out durationFrame, out buffUid);

            HeroBattleBuff buff = new HeroBattleBuff(buffId, buffUid, durationFrame);
            if (m_HeroBuffList.Contains(buff))
            {
                Debug.LogError("buff已经存在" + buff.Id);
                return;
            }

            m_HeroBuffList.Add(buff);
            OnAddBuff(buff);
        }

        public void RemoveBuff(long buffUid)
        {
            // int buffId, durationFrame, buffUid;
            // ParseNumToId(buffIdNum, out buffId, out durationFrame, out buffUid);
            
            foreach (var buff in m_HeroBuffList.Where(buff => buff.Uid == buffUid))
            {
                OnRemoveBuff(buff);
                m_HeroBuffList.Remove(buff);
            }
        }

        private void ParseNumToId(long buffIdNum,out int buffId,out int durationFrame,out int buffUid)
        {
            buffId = (int)(buffIdNum % 1000);
            buffIdNum = Mathf.FloorToInt(buffIdNum / 1000f);
            durationFrame = (int)(buffIdNum % 10000000);
            buffIdNum = Mathf.FloorToInt(buffIdNum / 10000000f);
            buffUid = (int)(buffIdNum);
        }
        
        private void OnAddBuff(HeroBattleBuff buff)
        {
            if (buff.EffectId <= 0)
                return;
            if (HasEffect(buff.EffectId))
            {
                TryCreateEffect(buff.EffectId);    
            }
        }

        private void OnRemoveBuff(HeroBattleBuff buff)
        {
            if (buff.EffectId <= 0)
                return;
            if (!HasEffect(buff.EffectId))
            {
                TryRemoveEffect(buff.EffectId);
            }
        }

        private bool HasEffect(int effectId)
        {
            return m_HeroBuffList.Any(t => t.EffectId == effectId);
        }

        private void TryCreateEffect(int effectId)
        {
            if (!m_Effects.ContainsKey(effectId))
            {
                var effect = m_EffectCtrl.GetEffect(effectId);
                effect.SetPosition(m_BattleHero.GetPosition());
                m_Effects.Add(effectId, effect);
            }
        }

        private void TryRemoveEffect(int effectId)
        {
            if (m_Effects.TryGetValue(effectId,out var effect))
            {
                m_EffectCtrl.RemoveEffect(effect);
                m_Effects.Remove(effectId);
            }
        }

        public void RemoveAllEffect()
        {
            foreach (var effect in m_Effects)
            {
                m_EffectCtrl.RemoveEffect(effect.Value);
            }
            m_Effects.Clear();
        }
        
    }
}
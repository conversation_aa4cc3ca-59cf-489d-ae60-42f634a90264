using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class UIBattle5V5DungeonStatsFormParam
    {
        public Report Report;
        public bool IsReplay = false;
    }
    
    public partial class UIBattle5V5DungeonStatsForm : UGuiFormEx
    {
        private Report m_Report;
        private bool m_IsReplay;

        private ulong m_AttackMax = 0;
        private ulong m_DefendMax = 0;
        private ulong m_BuffMax = 0;
        private ulong m_DebuffMax = 0;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UIBattle5V5DungeonStatsFormParam param)
            {
                m_Report = param.Report;
                m_IsReplay = param.IsReplay;
            }

            //
            if (m_Report == null)
                return;

            m_txtTime.text = TimeHelper.FormatGameTime(
                (int)(m_Report.Stats.Duration * BattleDefine.BATTLE_FRAME_DURATION),
                true);
            var role = m_Report.Defender.Role;
            m_txtEnemyName.text = "#" + role.ServerId + "[" + role.ServerId + "]" + role.Name;
            m_txtEnemyPower.text = "战力：" + role.Power;

            role = m_Report.Attacker.Role;
            m_txtMyselfName.text = "#" + role.ServerId + "[" + role.ServerId + "]" + role.Name;
            m_txtMyselfPower.text = "战力：" + role.Power;

            m_goEnemyVictory.SetActive(m_Report.Result == BattleResult.DefenderWin);
            m_goEnemyDefeat.SetActive(!m_goEnemyVictory.activeSelf);
            m_goMyselfVictory.SetActive(m_Report.Result == BattleResult.AttackerWin);
            m_goMyselfDefeat.SetActive(!m_goMyselfVictory.activeSelf);

            

            foreach (var item in m_Report.Stats.Defender)
            {
                m_AttackMax = (ulong)Mathf.Max(m_AttackMax, item.Attack);
                m_DefendMax = (ulong)Mathf.Max(m_DefendMax, item.Defend);
                m_BuffMax = (ulong)Mathf.Max(m_BuffMax, item.Buff);
                m_DebuffMax = (ulong)Mathf.Max(m_DebuffMax, item.Debuff);
            }
            foreach (var item in m_Report.Stats.Attacker)
            {
                m_AttackMax = (ulong)Mathf.Max(m_AttackMax, item.Attack);
                m_DefendMax = (ulong)Mathf.Max(m_DefendMax, item.Defend);
                m_BuffMax = (ulong)Mathf.Max(m_BuffMax, item.Buff);
                m_DebuffMax = (ulong)Mathf.Max(m_DebuffMax, item.Debuff);
            }
            
            
            RefreshHeroItem(m_transEnemyContent, m_Report.Stats.Defender, m_Report.Defender);
            RefreshHeroItem(m_transMyselfContent, m_Report.Stats.Attacker, m_Report.Attacker);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            if (m_IsReplay)
            {
                GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
            }
            
            m_Report = null;
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnEnemyClick()
        {
        }

        private void OnBtnMyselfClick()
        {
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        void HideDefault()
        {
            m_transHeroItem.gameObject.SetActive(false);
        }

        void RefreshHeroItem(Transform parent, RepeatedField<BattleTeamStats> battleTeamStats, Battle.Team team)
        {
            for (int i = 1; i <= 5; i++)
            {
                Transform item;
                if (i < parent.childCount)
                {
                    item = parent.GetChild(i);
                }
                else
                {
                    item = Instantiate(m_transHeroItem, parent);
                }

                var j = i - 1;
                if (j < battleTeamStats.Count && j < team.Heroes.Count)
                {
                    BattleTeamStats teamStats = battleTeamStats[j];
                    item.gameObject.SetActive(true);
                    RefreshItem(teamStats, item, team.Heroes[j]);
                }
                else
                {
                    item.gameObject.SetActive(false);
                }
            }
        }

        void RefreshItem(BattleTeamStats teamStats, Transform item, Battle.TeamHero teamHero)
        {
            
            var imgHeroBg = item.Find("hero/heroBg")?.GetComponent<UIImage>();
            var imgHeroSp = item.Find("hero/heroMask/heroSp")?.GetComponent<UIImage>();
            var txtHeroLevel = item.Find("hero/levelTxt")?.GetComponent<UIText>();

            item_config itemConfig = ToolScriptExtend.GetItemConfig((itemid)teamHero.Code);
            HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)teamHero.Code);
            
            imgHeroBg?.SetImage(ToolScriptExtend.GetQualityBg(itemConfig.quality));
            imgHeroSp?.SetImage(heroModule.HeroHead);
            if (txtHeroLevel != null) txtHeroLevel.text = "LV." + teamHero.Level;
            
            var txtValueOutput = item.Find("valueOutput/Text")?.GetComponent<UIText>();
            var sliderValueOutput = item.Find("valueOutput/slider")?.GetComponent<Slider>();
            
            var txtValueInjury = item.Find("valueInjury/Text")?.GetComponent<UIText>();
            var sliderValueInjury = item.Find("valueInjury/slider")?.GetComponent<Slider>();
            
            var txtValueBuffEffect = item.Find("valueBuffEffect/Text")?.GetComponent<UIText>();
            var sliderValueBuffEffect = item.Find("valueBuffEffect/slider")?.GetComponent<Slider>();
            
            var txtValueWeaken = item.Find("valueWeaken/Text")?.GetComponent<UIText>();
            var sliderValueWeaken = item.Find("valueWeaken/slider")?.GetComponent<Slider>();

            DOTween.To(() => 0, newValue =>
            {
                txtValueOutput.text = ToolScriptExtend.FormatNumberWithUnit(teamStats.Attack*newValue);
                txtValueInjury.text = ToolScriptExtend.FormatNumberWithUnit(teamStats.Defend*newValue);
                txtValueBuffEffect.text = ToolScriptExtend.FormatNumberWithUnit(teamStats.Buff*newValue);
                txtValueWeaken.text = ToolScriptExtend.FormatNumberWithUnit(teamStats.Debuff*newValue);

                sliderValueOutput.value = teamStats.Attack*newValue / m_AttackMax;
                sliderValueInjury.value = teamStats.Defend*newValue / m_DefendMax;
                sliderValueBuffEffect.value = teamStats.Buff*newValue / m_BuffMax;
                sliderValueWeaken.value = teamStats.Debuff*newValue / m_DebuffMax;
            }, 1f, 1f);

        }
    }
}
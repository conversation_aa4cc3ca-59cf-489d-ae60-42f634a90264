using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpTownDrawer : IWMCmp
    {

        private Dictionary<Vector2Int, List<int>> m_ShowListDic = new Dictionary<Vector2Int, List<int>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private ulong? m_PlayerId = null;
        
        private WorldMapComponent m_WorldMapComponent;

        public WMCmpTownDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_PlayerId = GameEntry.LogicData.UserData.uuid;
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty += OnGridDataDirty;
        }


        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty -= OnGridDataDirty;
        }
        
        private void OnGridDataDirty(HashSet<Vector2Int> dirtyData)
        {
            foreach (var t in dirtyData)
            {
                Remove(t);
            }
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                WorldMapLOD.Level1, out List<Vector2Int> showListNew);

            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    var list = gridData.TownData;
                    if (list != null)
                    {
                        var idList = new List<int>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            GridTownData data = list[i];
                            if (data.RoleId == m_PlayerId)
                                continue;
                            
                            var path = GetPathById(data);

                            ED_WorldMapTown param =
                                new ED_WorldMapTown(Game.GameEntry.Entity.GenerateSerialId(), data, null);
                            param.Position = new Vector3(data.PosX, 0, data.PosY);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(path, param, typeof(EL_WorldMapTown));
                            idList.Add(id);
                        }
                    
                        m_ShowListDic.Add(grid, idList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var idList))
                {
                    for (int i = 0; i < idList.Count; i++)
                    {
                        var id = idList[i];
                        GameEntry.Entity.HideEntity(id);
                    }

                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private string GetPathById(GridTownData data)
        {
            var cfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildtype.buildtype_headquarters,
                data.Level);
            if (cfg is { map_pre_id: > 0 })
            {
                var config = GameEntry.LDLTable.GetTableById<map_buildpre>(cfg.map_pre_id);
                return config?.pre;
            }
            return string.Empty;
        }
    }
}
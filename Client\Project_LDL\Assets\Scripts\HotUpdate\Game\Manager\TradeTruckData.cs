using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Roledata;

namespace Game.Hotfix
{
    public class TradeTruckData
    {
        List<Trade.TradeCargoTransport> myTruckList = new();
        List<Trade.TradeCargoTransport> otherTruckList = new();

        public List<Trade.TradeCargoTransport> MyTruckList => myTruckList;
        public List<Trade.TradeCargoTransport> OtherTruckList => otherTruckList;

        public Trade.TradeCargoTransport myTrain;
        public Trade.TradeCargoTransport otherTrain;

        public int TruckTradeTodayCount { get; set; }
        public int TruckPlunderTodayCount { get; set; }
        public int BuyExpressContractTimes { get; set; }
        public bool IsFilterTruck { get; set; }
        public bool HasThumbsUpAngel { get; set; }
        public Team.TeamType CurTruckTeamType { get; set; }
        public Trade.TradeVanRobResp TradeVanRobResp { get; set; }
        public Trade.TradeVipPassengerType TrainVipInviteType { get; set; }
        public int unlockAreaId = 45;

        public void Init(RoleTrade roleTrade)
        {
            ColorLog.Pink("城际贸易登录数据", roleTrade);
            if (roleTrade != null)
            {
                TruckTradeTodayCount = roleTrade.TodayTradeTimes;            // 今日已贸易次数
                TruckPlunderTodayCount = roleTrade.TodayRobTimes;            // 今日已掠夺次数
                BuyExpressContractTimes = roleTrade.BuyExpressContractTimes; // 特快合约购买次数

                ColorLog.Pink("特快合约购买次数", BuyExpressContractTimes);

                foreach (var item in roleTrade.VanList)
                {
                    if (IsTrain(item.TradeId))
                    {
                        myTrain ??= item;
                        if (myTrain != null && myTrain.Id < item.Id && item.ArrivalTime != 0)
                        {
                            myTrain = item;
                        }
                    }
                    else
                    {
                        UpdateTruckListByID(item, item.Id);
                    }
                }
            }

            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushTradeChange, (message) =>
            {
                ColorLog.Pink("推送货车变化", message);
                if (message is Trade.PushTradeChange resp)
                {
                    if (myTrain == null)
                    {
                        ColorLog.Pink("没有火车数据，第一次推送");
                    }
                    myTrain = resp.CargoTransport;
                    GameEntry.Event.Fire(TrainAnimationEventArgs.EventId, TrainAnimationEventArgs.Create(TrainAnimationType.Enter));
                    GameEntry.Event.Fire(TrainContractEventArgs.EventId, TrainContractEventArgs.Create(false));
                }
            });
        }

        #region 接口

        public bool IsOccupiedTradeVanDefendTeams(Team.TeamType teamType)
        {
            List<Team.TeamType> teamTypes = GetOccupiedTradeVanDefendTeams();
            return teamTypes.Contains(teamType);
        }

        /// <summary>
        /// 获取已被占用的贸易货车防守编队类型
        /// </summary>
        /// <returns>已被占用的编队类型列表</returns>
        public List<Team.TeamType> GetOccupiedTradeVanDefendTeams()
        {
            List<Team.TeamType> occupiedTeams = new();

            // 检查所有已出发的货车
            foreach (var truck in myTruckList)
            {
                if (truck != null && truck.ArrivalTime > 0 && truck.Teams != null)
                {
                    // 货车已出发且有编队信息
                    foreach (var team in truck.Teams)
                    {
                        if (IsTradeVanDefendTeam(team.Type) && !occupiedTeams.Contains(team.Type))
                        {
                            occupiedTeams.Add(team.Type);
                        }
                    }
                }
            }

            return occupiedTeams;
        }

        /// <summary>
        /// 判断是否是贸易货车防守编队
        /// </summary>
        /// <param name="teamType">编队类型</param>
        /// <returns>是否是贸易货车防守编队</returns>
        public bool IsTradeVanDefendTeam(Team.TeamType teamType)
        {
            return teamType == Team.TeamType.TradeVanDefend1 ||
                   teamType == Team.TeamType.TradeVanDefend2 ||
                   teamType == Team.TeamType.TradeVanDefend3 ||
                   teamType == Team.TeamType.TradeVanDefend4;
        }

        public Trade.TradeCargoTransport GetTruckByPos(int pos)
        {
            for (int i = 0; i < myTruckList.Count; i++)
            {
                if (myTruckList[i] != null && myTruckList[i].Position == pos)
                {
                    return myTruckList[i];
                }
            }
            return null;
        }

        public bool IsTrain(int tradeID)
        {
            trade trade = GetTruckConfigByID(tradeID);
            if (trade != null)
            {
                return trade.car_type == car_type.car_type_2 || trade.car_type == car_type.car_type_3;
            }
            return false;
        }

        public bool IsLineUpTrain()
        {
            if (myTrain != null)
            {
                foreach (var item in myTrain.Boxcar)
                {
                    if (item.Passengers != null && item.Passengers.Count > 0)
                    {
                        foreach (var passenger in item.Passengers)
                        {
                            if (passenger.RoleId == GameEntry.RoleData.RoleID)
                            {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }

        public TrainStatus GetTrainStatus()
        {
            if (myTrain != null)
            {
                if (myTrain.Train != null)
                {
                    long remainTime = myTrain.Train.PrepareTime - (long)TimeComponent.Now;
                    long endTime = 300;
                    trade_set setting = GetTradeSet();
                    if (setting != null)
                    {
                        endTime = setting.train_prepare_endtime;
                    }
                    if (remainTime > endTime)
                    {
                        return TrainStatus.LineUp;
                    }
                    else if (remainTime > 0)
                    {
                        return TrainStatus.Prepare;
                    }
                    else
                    {
                        long remainTime2 = myTrain.ArrivalTime - (long)TimeComponent.Now;
                        if (remainTime2 > 0)
                        {
                            return TrainStatus.Depart;
                        }
                        else
                        {
                            return TrainStatus.Arrive;
                        }
                    }
                }
            }
            return TrainStatus.None;
        }

        public bool HasSpecialGuest()
        {
            if (myTrain != null && myTrain.Train != null && myTrain.Train.VipPassenger != null)
            {
                return myTrain.Train.VipPassenger.Type == Trade.TradeVipPassengerType.DistinguishedGuest;
            }
            return false;
        }

        public Trade.TradeVipPassenger GetTradeVipPassenger()
        {
            if (myTrain != null && myTrain.Train != null && myTrain.Train.VipPassenger != null)
            {
                return myTrain.Train.VipPassenger;
            }
            return null;
        }

        public bool IsTrainGold()
        {
            if (myTrain == null) return false;
            int count = 0;
            for (int i = 0; i < myTrain.Boxcar.Count; i++)
            {
                Trade.TradeBoxcar tradeBoxcar = myTrain.Boxcar[i];
                if (tradeBoxcar != null)
                {
                    if (tradeBoxcar.Quality == PbGameconfig.car_quality._5)
                    {
                        count++;
                    }
                }
            }
            return count >= 5;
        }

        public bool IsOtherTrainGold()
        {
            if (otherTrain == null) return false;
            int count = 0;
            for (int i = 0; i < otherTrain.Boxcar.Count; i++)
            {
                Trade.TradeBoxcar tradeBoxcar = otherTrain.Boxcar[i];
                if (tradeBoxcar != null)
                {
                    if (tradeBoxcar.Quality == PbGameconfig.car_quality._5)
                    {
                        count++;
                    }
                }
            }
            return count >= 5;
        }

        public bool HasConductor()
        {
            if (myTrain == null) return false;
            if (myTrain.Boxcar.Count > 0)
            {
                Trade.TradeBoxcar tradeBoxcarHead = myTrain.Boxcar[0];
                if (tradeBoxcarHead != null)
                {
                    return tradeBoxcarHead.Passengers.Count > 0;
                }
            }
            return false;
        }

        public bool IsShowTrainEntry()
        {
            if (!IsUnlockTrade()) return false;
            if (myTrain == null) return false;
            bool hasConductor = HasConductor();
            bool isLineUpTrain = IsLineUpTrain();
            // 有列车长，但未排队，入口图标显示为火车样式
            return hasConductor && !isLineUpTrain;
        }

        public bool IsUnlockTrade()
        {
            return GameEntry.LogicData.PvePathData.CurStep >= unlockAreaId;
        }

        public bool IsTrainContractMax()
        {
            return BuyExpressContractTimes >= 2;
        }

        #endregion

        #region 配置表

        public trade GetTruckConfigByID(int id)
        {
            return GameEntry.LDLTable.GetTableById<trade>(id);
        }

        public trade_setting GetTradeSettingByID(int id)
        {
            return GameEntry.LDLTable.GetTableById<trade_setting>(id);
        }

        public trade_set GetTradeSet()
        {
            return GameEntry.LDLTable.GetKeyValueTable<trade_set>();
        }

        #endregion

        #region 公共协议

        /// <summary>
        /// 分享
        /// </summary>
        /// <param name="id">车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTradeShare(long id, Action<Trade.TradeShareResp> callback = null)
        {
            Trade.TradeShareReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeShare, req, (message) =>
            {
                Trade.TradeShareResp resp = message as Trade.TradeShareResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 查看货车、火车、月台
        /// </summary>
        /// <param name="id">车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTradeLookUp(long id, Action<Trade.TradeLookUpResp> callback = null)
        {
            Trade.TradeLookUpReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeLookUp, req, (message) =>
            {
                Trade.TradeLookUpResp resp = message as Trade.TradeLookUpResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        #endregion

        #region 货车协议

        /// <summary>
        /// 根据 ID 更新我的货车列表
        /// </summary>
        /// <param name="truck">货车数据</param>
        /// <param name="id">货车 id</param>
        void UpdateTruckListByID(Trade.TradeCargoTransport truck, long id)
        {
            if (truck == null)
            {
                ColorLog.Red("货车数据为空");
                return;
            }

            bool isFound = false;
            for (int i = 0; i < myTruckList.Count; i++)
            {
                if (myTruckList[i] != null && myTruckList[i].Id == id)
                {
                    myTruckList[i] = truck;
                    isFound = true;
                }
            }
            if (!isFound && truck != null)
            {
                myTruckList.Add(truck);
                myTruckList.Sort((a, b) => a.Position.CompareTo(b.Position));
            }
        }

        /// <summary>
        /// 根据车位更新我的货车列表
        /// </summary>
        /// <param name="truck">货车数据</param>
        /// <param name="pos">货车车位</param>
        void UpdateTruckListByPos(Trade.TradeCargoTransport truck, int pos)
        {
            if (truck == null)
            {
                ColorLog.Red("货车数据为空");
                return;
            }

            bool isFound = false;
            for (int i = 0; i < myTruckList.Count; i++)
            {
                if (myTruckList[i] != null && myTruckList[i].Position == pos)
                {
                    myTruckList[i] = truck;
                    isFound = true;
                }
            }
            if (!isFound && truck != null)
            {
                myTruckList.Add(truck);
                myTruckList.Sort((a, b) => a.Position.CompareTo(b.Position));
            }
        }

        /// <summary>
        /// 请求货车信息
        /// </summary>
        /// <param name="type">类型 1 我的货车 2 他人货车</param>
        /// <param name="filter_my_server">是否过滤本服</param>
        /// <param name="callback">回调</param>
        public void RequestTruckList(int type, bool filter_my_server, Action<Trade.TradeCargoTransportListResp> callback = null)
        {
            Trade.TradeCargoTransportListReq req = new()
            {
                Type = type,
                FilterMyServer = filter_my_server
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeCargoTransportList, req, (message) =>
            {
                Trade.TradeCargoTransportListResp resp = message as Trade.TradeCargoTransportListResp;
                if (resp != null)
                {
                    if (type == 1)
                    {
                        myTruckList.Clear();
                        var temp = new List<Trade.TradeCargoTransport>(resp.CargoTransportList);
                        for (int i = 0; i < temp.Count; i++)
                        {
                            if (temp[i].TradeId == 0) continue;
                            if (IsTrain(temp[i].TradeId))
                            {
                                myTrain = temp[i];
                            }
                            else
                            {
                                myTruckList.Add(temp[i]);
                            }
                        }

                        TruckTradeTodayCount = resp.Times;
                    }
                    else if (type == 2)
                    {
                        otherTruckList.Clear();
                        bool hasTrain = false;
                        var temp = new List<Trade.TradeCargoTransport>(resp.CargoTransportList);
                        for (int i = 0; i < temp.Count; i++)
                        {
                            if (temp[i].TradeId == 0) continue;
                            if (IsTrain(temp[i].TradeId))
                            {
                                otherTrain = temp[i];
                                hasTrain = true;
                            }
                            else
                            {
                                otherTruckList.Add(temp[i]);
                            }
                        }

                        if (!hasTrain)
                        {
                            otherTrain = null;
                        }

                        TruckPlunderTodayCount = resp.Times;
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车历史记录
        /// </summary>
        /// <param name="tradeRecordType">类型</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRecord(Trade.TradeRecordType tradeRecordType, Action<Trade.TradeVanRecordListResp> callback = null, int page = 0)
        {
            Trade.TradeVanRecordListReq req = new()
            {
                Type = tradeRecordType
            };

            if (tradeRecordType == Trade.TradeRecordType.Rob)
            {
                req.Page = page;
            }

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRecordList, req, (message) =>
            {
                Trade.TradeVanRecordListResp resp = message as Trade.TradeVanRecordListResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车详情
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckDetail(long id, Action<Trade.TradeVanDetailResp> callback = null)
        {
            Trade.TradeVanDetailReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanDetail, req, (message) =>
            {
                Trade.TradeVanDetailResp resp = message as Trade.TradeVanDetailResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求刷新货车品质
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRefresh(long id, Action<Trade.TradeVanRefreshResp> callback = null)
        {
            Trade.TradeVanRefreshReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRefresh, req, (message) =>
            {
                Trade.TradeVanRefreshResp resp = message as Trade.TradeVanRefreshResp;
                if (resp != null)
                {
                    UpdateTruckListByID(resp.CargoTransport, id);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车出发
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="teamType">队伍</param>
        /// <param name="callback">回调</param>
        public void RequestTruckDepart(long id, Team.TeamType teamType, Action<Trade.TradeVanDepartResp> callback = null)
        {
            Trade.TradeVanDepartReq req = new()
            {
                Id = id,
                TeamType = teamType
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanDepart, req, (message) =>
            {
                Trade.TradeVanDepartResp resp = message as Trade.TradeVanDepartResp;
                if (resp != null)
                {
                    TruckTradeTodayCount++;
                    UpdateTruckListByID(resp.CargoTransport, id);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车车位信息
        /// </summary>
        /// <param name="pos">位置</param>
        /// <param name="callback">回调</param>
        public void RequestTruckParking(int pos, Action<Trade.TradeVanSetOutResp> callback = null)
        {
            Trade.TradeVanSetOutReq req = new()
            {
                Position = pos
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanSetOut, req, (message) =>
            {
                Trade.TradeVanSetOutResp resp = message as Trade.TradeVanSetOutResp;
                if (resp != null)
                {
                    UpdateTruckListByPos(resp.Van, pos);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车领取奖励
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckReward(long id, Action<Trade.TradeVanReceiveRewardResp> callback = null)
        {
            Trade.TradeVanReceiveRewardReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanReceiveReward, req, (message) =>
            {
                Trade.TradeVanReceiveRewardResp resp = message as Trade.TradeVanReceiveRewardResp;
                if (resp != null)
                {
                    for (int i = myTruckList.Count - 1; i >= 0; i--)
                    {
                        if (myTruckList[i].Id == id)
                        {
                            myTruckList.RemoveAt(i);
                        }
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车掠夺
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRob(long id, Team.TeamType teamType, Action<Trade.TradeVanRobResp> callback = null)
        {
            Trade.TradeVanRobReq req = new()
            {
                Id = id,
                AtkOrder = teamType
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRob, req, (message) =>
            {
                Trade.TradeVanRobResp resp = message as Trade.TradeVanRobResp;
                if (resp != null)
                {
                    if (resp.Result == Battle.BattleResult.AttackerWin)
                    {
                        TruckPlunderTodayCount++;

                        for (int i = 0; i < otherTruckList.Count; i++)
                        {
                            if (otherTruckList[i].Id == id)
                            {
                                otherTruckList[i].RobTimes++;
                            }
                        }
                    }
                    TradeVanRobResp = resp;
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求通缉玩家
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="id">记录 id</param>
        /// <param name="robID">掠夺记录 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckWanted(Trade.TradeRecordType type, ulong robID, Action<Trade.TradeVanWantedResp> callback = null)
        {
            Trade.TradeVanWantedReq req = new()
            {
                Type = type,
                RobId = robID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanWanted, req, (message) =>
            {
                Trade.TradeVanWantedResp resp = message as Trade.TradeVanWantedResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求收藏货车记录
        /// </summary>
        /// <param name="id">记录 id</param>
        /// <param name="flag">收藏/取消</param>
        /// <param name="callback">回调</param>
        public void RequestTruckCollect(ulong id, bool flag, ulong roleID, uint serverID, Action<Trade.TradeVanCollectResp> callback = null)
        {
            Trade.TradeVanCollectReq req = new()
            {
                Id = id,
                Flag = flag,
                RoleId = roleID,
                ServerId = serverID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanCollect, req, (message) =>
            {
                Trade.TradeVanCollectResp resp = message as Trade.TradeVanCollectResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        #endregion

        #region 火车协议

        /// <summary>
        /// 请求火车指派列车长
        /// </summary>
        /// <param name="roleID">角色 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainConductor(ulong roleID, Action<Trade.TradeTrainAppointConductorResp> callback = null)
        {
            Trade.TradeTrainAppointConductorReq req = new()
            {
                RoleId = roleID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainAppointConductor, req, (message) =>
            {
                Trade.TradeTrainAppointConductorResp resp = message as Trade.TradeTrainAppointConductorResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车刷新货物
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainRefreshGoods(long id, Action<Trade.TradeTrainRefreshGoodsResp> callback = null)
        {
            Trade.TradeTrainRefreshGoodsReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainRefreshGoods, req, (message) =>
            {
                Trade.TradeTrainRefreshGoodsResp resp = message as Trade.TradeTrainRefreshGoodsResp;
                if (resp != null)
                {
                    myTrain = resp.CargoTransport;
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车排队上车
        /// </summary>
        /// <param name="pos">车厢位置</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainLineUp(int pos, long id, Action<Trade.TradeTrainLineUpResp> callback = null)
        {
            Trade.TradeTrainLineUpReq req = new()
            {
                Position = pos,
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainLineUp, req, (message) =>
            {
                Trade.TradeTrainLineUpResp resp = message as Trade.TradeTrainLineUpResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车详细信息
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainDetail(long id, Action<Trade.TradeTrainDetailResp> callback = null)
        {
            Trade.TradeTrainDetailReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainDetail, req, (message) =>
            {
                Trade.TradeTrainDetailResp resp = message as Trade.TradeTrainDetailResp;
                if (resp != null)
                {
                    if (resp.CargoTransport != null && id == resp.CargoTransport.Id)
                    {
                        myTrain = resp.CargoTransport;
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车乘车感谢
        /// </summary>
        /// <param name="type">感谢类型</param>
        /// <param name="articles">赠送合约</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainThanks(Trade.TradeThanksType type, List<Article.Article> articles, long id, Action<Trade.TradeTrainThanksResp> callback = null)
        {
            Trade.TradeTrainThanksReq req = new()
            {
                Type = type,
                Article = { articles },
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainThanks, req, (message) =>
            {
                Trade.TradeTrainThanksResp resp = message as Trade.TradeTrainThanksResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车感谢列表
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainThanksList(long id, Action<Trade.TradeTrainThanksListResp> callback = null)
        {
            Trade.TradeTrainThanksListReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainThanksList, req, (message) =>
            {
                Trade.TradeTrainThanksListResp resp = message as Trade.TradeTrainThanksListResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车邀请 VIP 乘客
        /// </summary>
        /// <param name="type">邀请类型 1 特别嘉宾 2 守护天使</param>
        /// <param name="roleId">角色 id</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainInvite(Trade.TradeVipPassengerType type, ulong roleId, long id, Action<Trade.TradeTrainInviteResp> callback = null)
        {
            Trade.TradeTrainInviteReq req = new()
            {
                Type = type,
                RoleId = roleId,
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainInvite, req, (message) =>
            {
                Trade.TradeTrainInviteResp resp = message as Trade.TradeTrainInviteResp;
                if (resp != null)
                {
                    TrainVipInviteType = type;
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车调整布阵
        /// </summary>
        /// <param name="teams">布阵队伍</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainFormation(List<Fight.FormationTeam> teams, long id, Action<Trade.TradeTrainFormationResp> callback = null)
        {
            Trade.TradeTrainFormationReq req = new()
            {
                Teams = { teams },
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainFormation, req, (message) =>
            {
                Trade.TradeTrainFormationResp resp = message as Trade.TradeTrainFormationResp;
                if (resp != null)
                {
                    ColorLog.Pink("火车调整布阵", resp);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车调整布阵顺序
        /// </summary>
        /// <param name="random">是否随机顺序迎敌</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainFormationOrder(bool random, long id, Action<Trade.TradeTrainFormationOrderResp> callback = null)
        {
            Trade.TradeTrainFormationOrderReq req = new()
            {
                Random = random,
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainFormationOrder, req, (message) =>
            {
                Trade.TradeTrainFormationOrderResp resp = message as Trade.TradeTrainFormationOrderResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车选择 VIP 乘客奖励
        /// </summary>
        /// <param name="position">车厢 1 开始</param>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainSelectVipPassengerReward(List<int> position, long id, Action<Trade.TradeTrainSelectVipPassengerRewardResp> callback = null)
        {
            Trade.TradeTrainSelectVipPassengerRewardReq req = new()
            {
                Position = { position },
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainSelectVipPassengerReward, req, (message) =>
            {
                Trade.TradeTrainSelectVipPassengerRewardResp resp = message as Trade.TradeTrainSelectVipPassengerRewardResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车守护天使点赞
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainGuardAngelLike(long id, Action<Trade.TradeTrainGuardAngelLikeResp> callback = null)
        {
            Trade.TradeTrainGuardAngelLikeReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainGuardAngelLike, req, (message) =>
            {
                Trade.TradeTrainGuardAngelLikeResp resp = message as Trade.TradeTrainGuardAngelLikeResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车领取感谢奖励
        /// </summary>
        /// <param name="trainID">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainReceiveThanksReward(long trainID, Action<Trade.TradeReceiveThanksRewardResp> callback = null)
        {
            Trade.TradeReceiveThanksRewardReq req = new()
            {
                TrainId = trainID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeReceiveThanksReward, req, (message) =>
            {
                Trade.TradeReceiveThanksRewardResp resp = message as Trade.TradeReceiveThanksRewardResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车同意邀请
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainAgreeInvite(long id, Action<Trade.TradeTrainAgreeInviteResp> callback = null)
        {
            Trade.TradeTrainAgreeInviteReq req = new()
            {
                Id = id,
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainAgreeInvite, req, (message) =>
            {
                Trade.TradeTrainAgreeInviteResp resp = message as Trade.TradeTrainAgreeInviteResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车开始战斗
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="attack">进攻阵型顺序</param>
        /// <param name="defend">防守阵型顺序（如果设置了随机出战，客户端随机即可）</param>
        /// <param name="callback">回调</param>
        public void RequestTrainStartFight(long id, List<Team.TeamType> attack, List<Team.TeamType> defend, Action<Trade.TradeTrainStartFightResp> callback = null)
        {
            Trade.TradeTrainStartFightReq req = new()
            {
                Id = id,
                AtkOrder = { attack },
                DefOrder = { defend }
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainStartFight, req, (message) =>
            {
                Trade.TradeTrainStartFightResp resp = message as Trade.TradeTrainStartFightResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求火车战斗记录
        /// </summary>
        /// <param name="id">火车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTrainFightRecordList(long id, Action<Trade.TradeTrainFightRecordListResp> callback = null)
        {
            Trade.TradeTrainFightRecordListReq req = new()
            {
                Id = id,
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeTrainFightRecordList, req, (message) =>
            {
                Trade.TradeTrainFightRecordListResp resp = message as Trade.TradeTrainFightRecordListResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        #endregion
    }
}

using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnHeroBaoDi;
        [SerializeField] private UIButton m_btnHeroDrop;
        [SerializeField] private UIButton m_btnSelectFinalHero;
        [SerializeField] private UIButton m_btnFinalHero;
        [SerializeField] private UIButton m_btnSurvivalBaoDi;
        [SerializeField] private UIButton m_btnSurvivalHelp;
        [SerializeField] private UIButton m_btnLegendBaoDi;
        [SerializeField] private UIButton m_btnLegendDrop;
        [SerializeField] private UIButton m_btnSwitch;
        [SerializeField] private UIButton m_btnOne;
        [SerializeField] private UIButton m_btnHundred;
        [SerializeField] private UIButton m_btnTen;
        [SerializeField] private UIButton m_btnOneFree;
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnDescClose;

        [SerializeField] private UIText m_txtHeroPre;
        [SerializeField] private UIText m_txtWish;
        [SerializeField] private UIText m_txtTimer;
        [SerializeField] private UIText m_txtFreeTimer;
        [SerializeField] private UIText m_txtDesc;

        [SerializeField] private UIImage m_imgHeroIcon;
        [SerializeField] private UIImage m_imgHeroPre;
        [SerializeField] private UIImage m_imgFinalHero;

        [SerializeField] private Slider m_sliderWish;
        [SerializeField] private UIToggle m_togSkip;

        [SerializeField] private GameObject m_goHero;
        [SerializeField] private Spine.Unity.SkeletonGraphic m_spuiRole;
        [SerializeField] private GameObject m_goPreview;
        [SerializeField] private GameObject m_goHeroPre;
        [SerializeField] private Transform m_transPreRoot;
        [SerializeField] private GameObject m_goWishNode;
        [SerializeField] private GameObject m_goWishRedDot;
        [SerializeField] private GameObject m_goSurvival;
        [SerializeField] private GameObject m_goLegend;
        [SerializeField] private GameObject m_goGift;
        [SerializeField] private GameObject m_goGiftRoot;
        [SerializeField] private GameObject m_goContent;
        [SerializeField] private GameObject m_goDiamond;
        [SerializeField] private GameObject m_goTopItem;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goTagItem;
        [SerializeField] private GameObject m_goRewardItem;
        [SerializeField] private GameObject m_goPreNode;
        [SerializeField] private GameObject m_goDescCheck;
        [SerializeField] private GameObject m_goDescNode;

        void InitBind()
        {
            m_btnHeroBaoDi.onClick.AddListener(OnBtnHeroBaoDiClick);
            m_btnHeroDrop.onClick.AddListener(OnBtnHeroDropClick);
            m_btnSelectFinalHero.onClick.AddListener(OnBtnSelectFinalHeroClick);
            m_btnFinalHero.onClick.AddListener(OnBtnFinalHeroClick);
            m_btnSurvivalBaoDi.onClick.AddListener(OnBtnSurvivalBaoDiClick);
            m_btnSurvivalHelp.onClick.AddListener(OnBtnSurvivalHelpClick);
            m_btnLegendBaoDi.onClick.AddListener(OnBtnLegendBaoDiClick);
            m_btnLegendDrop.onClick.AddListener(OnBtnLegendDropClick);
            m_btnSwitch.onClick.AddListener(OnBtnSwitchClick);
            m_btnOne.onClick.AddListener(OnBtnOneClick);
            m_btnHundred.onClick.AddListener(OnBtnHundredClick);
            m_btnTen.onClick.AddListener(OnBtnTenClick);
            m_btnOneFree.onClick.AddListener(OnBtnOneFreeClick);
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnDescClose.onClick.AddListener(OnBtnDescCloseClick);
        }
    }
}

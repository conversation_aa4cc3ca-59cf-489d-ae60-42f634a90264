using System;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class HUDWorldMapTown : HUDItem
    {
        [SerializeField] private UIText m_txtName;


        protected override void OnInit(object param)
        {
            base.OnInit(param);
            if(param!=null && param is GridTownData data)
            {
                m_txtName.text = "Name：" + data.RoleId;
            }
            
        }

        protected override Vector3 GetOffset()
        {
            return new Vector3(0.5f, 0.5f, 0.5f);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
        }
    }
}
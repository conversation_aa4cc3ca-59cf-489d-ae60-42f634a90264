using System;
using System.Linq;
using Game.Hotfix.Config;
using Roledata;
using UnityEngine;
using UnityEngine.Events;
using Vip;

namespace Game.Hotfix
{
    public class VipData
    {
        private PushVipChange VipMsg;//vip数据缓存

        private int recordLevel;//记录上一次vip等级，用于比较判断是否vip升级
        
        //邮件配置数据
        private vip_set vipSet; 
        public vip_set VipSet
        {
            get
            {
                if (vipSet == null)
                {
                    if (ToolScriptExtend.GetTable<vip_set>(out var config))
                    {
                        vipSet = config[0];
                    }
                }
                return vipSet;
            }
        }
        
        
        public void Init(RoleAttr attr)
        {
            VipMsg = new PushVipChange();
            VipMsg.VipLevel = attr.VipLevel;
            VipMsg.VipExp = attr.VipExp;
            VipMsg.VipEndTime = attr.VipEndTime;
            VipMsg.IsVipDailyPointReceive = attr.IsVipDailyPointReceive;
            VipMsg.IsVipDailyGiftReceive = attr.IsVipDailyGiftReceive;
            VipMsg.VipTodayBuyPoints = attr.VipTodayBuyPoints;
            
            recordLevel = VipMsg.VipLevel;
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushVipChange, message =>
            {
                var data = (PushVipChange)message;
                OnVipMsgChange(data);
            });
        }
        
        #region 红点逻辑

        //获取vip系统红点数
        public int GetVipRedDot()
        {
            if (!IsVipUnlock()) return 0;
            var dotCount = 0;
            var isReceived = IsVipDailyPointReceive();
            var level = GetVipLevel();
            var status = GetFreeGiftStatus(level);
            if (!isReceived)
            {
                dotCount++;
            }
            if (status == 1)
            {
                dotCount++;
            }
            return dotCount;
        }
        #endregion
        
        #region 导表配置

        public vip_level GetVipConfigById(int levelId)
        {
            if (ToolScriptExtend.GetConfigById<vip_level>(levelId, out var data))
            {
                return data;
            }
            return null;
        }

        
        //获取最大的vip等级
        public int GetMaxLevel()
        {
            var list = GameEntry.LDLTable.GetTable<vip_level>();
            if (list != null)
            {
                var data = list.LastOrDefault();
                if (data != null)
                {
                    return data.id;
                }
            }
            return 0;
        }
        
        #endregion
        
        #region 协议
        //推送vip信息变化
        private void OnVipMsgChange(PushVipChange resp)
        {
            if (resp == null)
            {
                Debug.Log("推送vip信息变化数据为空！");
                return;
            }
            ColorLog.ProtoLog(false, "推送vip信息变化", resp.ToString());
            VipMsg = resp;
            
            //判断是否vip升级
            if (recordLevel > 0 && VipMsg.VipLevel > recordLevel)
            {
                recordLevel = VipMsg.VipLevel;
                
                var form = GameEntry.UI.GetUIForm(EnumUIForm.UIVipForm);
                if (form != null)
                {
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UIVipForm,2);
                }
            }
            GameEntry.Event.Fire(VipChangeEventArgs.EventId, VipChangeEventArgs.Create());
        }
        
        /// <summary>
        /// 请求领取每日免费VIP点数
        /// </summary>
        /// <param name="action">成功响应回调</param>
        public void C2SVipReceiveDailyPoints(UnityAction<VipReceiveDailyPointsResp> callback = null)
        {
            var req = new VipReceiveDailyPointsReq();
            ColorLog.ProtoLog(true, "领取每日免费VIP点数", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.VipReceiveDailyPoints, req, (message) =>
            {
                var resp = (VipReceiveDailyPointsResp)message;
                ColorLog.ProtoLog(false, "领取每日免费VIP点数", resp.ToString());
                callback?.Invoke(resp);

                GameEntry.UI.OpenUIForm(EnumUIForm.UIVipPointTipForm);
            });
        }
        
        /// <summary>
        /// 请求领取VIP等级每日免费礼包
        /// </summary>
        /// <param name="id">商品id,对应 store_type 表中的id; 0为全部商店</param>
        /// <param name="action">成功响应回调</param>
        public void C2SVipReceiveDailyGift(UnityAction<VipReceiveDailyGiftResp> callback = null)
        {
            var req = new VipReceiveDailyGiftReq();
            ColorLog.ProtoLog(true, "领取VIP等级每日免费礼包", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.VipReceiveDailyGift, req, (message) =>
            {
                var resp = (VipReceiveDailyGiftResp)message;
                ColorLog.ProtoLog(false, "领取VIP等级每日免费礼包", resp.ToString());
                callback?.Invoke(resp);
                ToolScriptExtend.DisplayRewardGet(resp.Articles.ToList());
            });
        }
        
        #endregion

        //vip是否激活
        public bool IsVipActive()
        {
            var endTime = GetVipEndTime();
            if (endTime == 0)
            {
                return false;
            }

            return !TimeComponent.IsEnd(endTime);
        }
        
        //获取当前vip等级
        public int GetVipLevel()
        {
            return VipMsg?.VipLevel ?? 0;
        }
        
        //获取当前vip经验
        public int GetVipExp()
        {
            return VipMsg?.VipExp ?? 0;
        }
        
        //获取当前vip结束时间
        public long GetVipEndTime()
        {
            return VipMsg?.VipEndTime ?? 0;
        }
        
        //是否领取了每日免费vip点数
        public bool IsVipDailyPointReceive()
        {
            return VipMsg?.IsVipDailyPointReceive ?? false;
        }
        
        //是否领取了每日免费vip礼包
        public bool IsVipDailyGiftReceive()
        {
            return VipMsg?.IsVipDailyGiftReceive ?? false;
        }
        
        //Vip今日已购买的点数
        public int GetVipTodayBuyPoints()
        {
            return VipMsg?.VipTodayBuyPoints ?? 0;
        }
        
        //是否可以继续购买点数
        public bool IsCanBuyPoints(itemid id,out string tip)
        {
            tip = "";
            //判断是否将要超过每日限购VIP点数
            // if(!ToolScriptExtend.GetConfigById<vip_setting>(1004,out var baseData)) return false;
            var boughtPoints = GetVipTodayBuyPoints();
            if(!ToolScriptExtend.GetConfigById<item_config>((int)id,out var itemData)) return false;
            var addValue = int.Parse(itemData.use_value[0]);
            var limitValue = GameEntry.LogicData.VipData.VipSet.daily_buy_VIP_points_point_limit;
            if (addValue + boughtPoints > limitValue)
            {
                tip = ToolScriptExtend.GetLangFormat(1100352, boughtPoints.ToString(), limitValue.ToString());
                return false;
            }
            return true;
        }
        
        /// <summary>
        /// 判断免费礼包状态
        /// 0:已过期 1：可领取 2：已领取 3：未激活 4：未达标 
        /// </summary>
        /// <returns></returns>
        public int GetFreeGiftStatus(int checkLevel)
        {
            if (!IsVipActive())
            {
                return 3;
            }
            var status = 3;
            var curLevel = GetVipLevel();
            if (checkLevel < curLevel)
            {
                status = 0;
            }
            else if (checkLevel == curLevel)
            {
                var isReceived = IsVipDailyGiftReceive();
                status = isReceived ? 2:1;
            }
            else
            {
                status = 4;
            }
            return status;
        }
        
        /// <summary>
        /// 判断专属礼包状态
        /// 1：未解锁 2：可购买 3：已购买
        /// </summary>
        /// <returns></returns>
        public int GetExclusiveGiftStatus(int checkLevel,bool isSoldOut)
        {
            if (isSoldOut)
            {
                return 3;
            }
            else
            {
                var curLevel = GetVipLevel();
                if (checkLevel > curLevel)
                {
                    return 1;
                }
            }
            return 2;
        }
        
        //获取vip激活剩余时间
        public int GetVipRemainTime()
        {
            var endTime = GetVipEndTime();
            var time = (ulong)endTime - TimeComponent.Now;
            return time <= 0 ? 0 : (int)time;
        }
        
        //获取vip重置免费礼包倒计时
        public int GetFreeResetTime()
        {
            var endTime = GetNextDayResetTimestamp((long)TimeComponent.Now);
            var time = (ulong)endTime - TimeComponent.Now;
            return time <= 0 ? 0 : (int)time;
        }
        
        public long GetNextDayResetTimestamp(long inputTimestamp)
        {
            // 将时间戳转换为 DateTime（假设是 UTC 时间）
            DateTime dateTime = DateTimeOffset.FromUnixTimeSeconds(inputTimestamp).UtcDateTime;
            // 获取当天的 00:00:00
            DateTime sameDayMidnight = dateTime.Date;
            
            // 获取第二天的 00:00:00
            DateTime nextDayMidnight = sameDayMidnight.AddDays(1);
            // // 获取第二天的 10:00:00
            // DateTime nextDay10Am = nextDayMidnight.AddHours(10);
        
            // 转换为时间戳
            DateTimeOffset dateTimeOffset = new DateTimeOffset(nextDayMidnight);
            long timestamp = dateTimeOffset.ToUnixTimeSeconds();
        
            return timestamp;
        }
        
        
        //计算连续登录几天可领取的vip点数
        public int GetContinuePoints(int days)
        {
            if (days < 1) return 0;
            var setData = GameEntry.LogicData.VipData.VipSet;
            var value = setData.daily_free_VIP_points_base;
            days--;
            value += days * setData.daily_free_VIP_points_accumulated;
            var limitValue = setData.daily_free_VIP_points_point_limit;
            return value >= limitValue ? limitValue : value;
        }
        
        //vip功能是否解锁
        public bool IsVipUnlock()
        {
            // if (!ToolScriptExtend.GetConfigById<vip_setting>(1007, out var unlockData)) return false;
            var unlockId = GameEntry.LogicData.VipData.VipSet.VIP_open_conditions;
            var isUnlock = ToolScriptExtend.GetDemandUnlock(unlockId);
            return isUnlock;
        }

        public void DebugVipMsg()
        {
            ColorLog.ProtoLog(false, "vip基础信息", VipMsg.ToString());
        }
        
        //检测是否有延迟的vip升级界面弹出
        public void SpecialTrigger()
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIVipForm);
            if (form != null)
            {
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIVipForm,3);
            }
        }
    }
}



using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class UnionData
    {
        /// <summary>
        /// 联盟Id
        /// </summary>
        public ulong UnionId { get; set; }
        /// <summary>
        /// 联盟职位
        /// </summary>
        public int UnionPosition { get; set; }
        /// <summary>
        /// 联盟权限
        /// </summary>
        public int UnionPermission { get; set; }
        /// <summary>
        /// 离开联盟时间戳
        /// </summary>
        public long UnionLeaveTime { get; set; }
        /// <summary>
        /// 是否第一次加入联盟
        /// </summary>
        public bool IsFirstJoinUnion { get; set; }
        /// <summary>
        /// 是否显示特殊联盟界面（玩家未加入过联盟且解锁联盟功能在24小时内时，会弹出特殊界面）
        /// </summary>
        public bool ShowSpecialView { get; set; }
        /// <summary>
        /// 是否开启联盟（同盟中心是否建造）
        /// </summary>
        public bool IsOpenUnion { get; set; }
        /// <summary>
        /// 加入联盟时间戳
        /// </summary>
        public long UnionJoinTime { get; set; }

        /// <summary>
        /// 联盟名字
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 礼物等级
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 联盟旗帜
        /// </summary>
        public int Flag { get; set; }
        /// <summary>
        /// 联盟语言
        /// </summary>
        public int Lang { get; set; }
        /// <summary>
        /// 会长所在区服Id
        /// </summary>
        public uint LeaderServerId { get; set; }
        /// <summary>
        /// 会长id
        /// </summary>
        public ulong LeaderRoleId { get; set; }
        /// <summary>
        /// 联盟简称
        /// </summary>
        public string ShortName { get; set; }
        /// <summary>
        /// 联盟公告
        /// </summary>
        public string Notice { get; set; }
        /// <summary>
        /// 成员人数
        /// </summary>
        public int Members { get; set; }
        /// <summary>
        /// 礼物经验
        /// </summary>
        public long GiftExp { get; set; }
        /// <summary>
        /// 战力
        /// </summary>
        public long Power { get; set; }
        /// <summary>
        /// 是否允许自由加入
        /// </summary>
        public bool IsFreedomJoin { get; set; }
        /// <summary>
        /// 是否匿名发送礼物
        /// </summary>
        public bool IsAnonymousGift { get; set; }
        /// <summary>
        /// 加入联盟战力限制
        /// </summary>
        public long JoinPowerLimit { get; set; }
        /// <summary>
        /// 加入联盟等级限制
        /// </summary>
        public long JoinLevelLimit { get; set; }
        /// <summary>
        /// 联盟创建时间
        /// </summary>
        public long CreateTime { get; set; }
        /// <summary>
        /// 科技自动升级开关
        /// </summary>
        public bool AutoUpgradeSwitch { get; set; }

        private Dictionary<int, Union.UnionTech> unionTechDic = new(); // 科技数据
        private Dictionary<tech_effect_type, tech_attributes> unionTechAddDic = new(); // 科技加成数据
        private Dictionary<Union.UnionDonateType, Union.UnionTechDonateData> unionTechDonateDic = new(); // 科技捐献
        private Dictionary<int, Union.MileStone> mileStoneDic = new(); // 里程碑数据
        private Dictionary<long, Union.UnionGift> normalGiftDic = new(); // 普通礼物数据
        private Dictionary<long, Union.UnionGift> highGiftDic = new(); // 高级礼物数据
        private Dictionary<long, Union.UnionHelp> helpDic = new(); // 帮助数据

        public int recommendGroupId; // 推荐科技组
        private Dictionary<int, int> giftUnlockDic; // 礼物解锁功能
        public List<Union.PushUnionHelpTips> helpTipList = new();

        private int applyRedCount; // 申请红点
        public bool techLoginRed = true; // 科技登录红点

        public void Init(Roledata.RoleUnion roleUnion)
        {
            if (roleUnion != null)
            {
                SetRoleUnion(roleUnion);
            }

            InitRedPoint();

            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.UnionMemberOut, (message) =>
            {
                // 联盟成员退出通知（关闭所有联盟界面）
                GameEntry.LogicData.UnionData.CloseAllUnionForm();
                ColorLog.Pink("联盟成员退出通知（关闭所有联盟界面）", message);
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.UnionHaveBeenJoin, (message) =>
            {
                // 联盟玩家已经加入联盟，关闭创建界面
                GameEntry.LogicData.UnionData.CloseAllUnionForm();
                GameEntry.LogicData.UnionData.GoToUnion();
                ColorLog.Pink("联盟玩家已经加入联盟，关闭创建界面", message);
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.UnionTechAutoUpgradeNotify, (message) =>
            {
                // 联盟科技自动升级通知
                if (message != null)
                {
                    var data = (Union.UnionTechAutoUpgradeNotify)message;
                    AutoUpgradeSwitch = data.Flag;
                    ColorLog.Pink("联盟科技自动升级通知", message);
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.UnionApplyListChangePush, (message) =>
            {
                // 联盟申请列表变更推送
                if (message != null)
                {
                    var data = (Union.UnionApplyListChangePush)message;
                    var list = data.ApplyList;
                    for (int i = 0; i < list.Count; i++)
                    {
                        if (list[i].Status == 0) { applyRedCount++; }
                        else { applyRedCount--; }
                    }
                    RedPointManager.Instance.Dirty(EnumRed.Union_Apply.ToString());
                    ColorLog.Pink("联盟申请列表变更推送", message);
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.UnionRoleInfoChangePush, (message) =>
            {
                // 联盟成员信息变更推送
                if (message != null)
                {
                    var data = (Union.UnionRoleInfoChangePush)message;
                    SetRoleUnion(data.RoleInfo);
                    ColorLog.Pink("联盟成员信息变更推送", message);
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushUnionTechChange, (message) =>
            {
                // 联盟科技变化推送
                if (message != null)
                {
                    var data = (Union.PushUnionTechChange)message;
                    var techData = data.Tech;
                    SetUnionTechAddDic(techData.Id, techData);
                    SetUnionTechDic(techData.Id, techData);
                    ColorLog.Pink("联盟科技变化推送", message);
                    GameEntry.Event.Fire(UnionTechChangeEventArgs.EventId, UnionTechChangeEventArgs.Create());
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushMilestoneChange, (message) =>
            {
                // 联盟里程碑变化推送
                if (message != null)
                {
                    var data = (Union.PushMilestoneChange)message;
                    var milestoneData = data.Milestone;
                    SetMileStoneDic(milestoneData.Id, milestoneData);
                    ColorLog.Pink("联盟里程碑变化推送", message);
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushUnionChange, (message) =>
            {
                // 推送联盟变化信息
                if (message != null)
                {
                    var data = (Union.PushUnionChange)message;
                    var changeType = data.Type;
                    var unionBrief = data.Brief;
                    if (changeType == 0) // 全部
                    {
                        SetUnionBrief(unionBrief);
                    }
                    else if (changeType == 1) // 礼物
                    {
                        Level = unionBrief.Level;
                        GiftExp = unionBrief.GiftExp;
                    }
                    ColorLog.Pink("推送联盟变化信息", message);
                    GameEntry.Event.Fire(UnionChangeEventArgs.EventId, UnionChangeEventArgs.Create(changeType));
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushUnionHelp, (message) =>
            {
                // 联盟帮助列表通知
                if (message != null)
                {
                    var data = (Union.PushUnionHelp)message;
                    var _type = data.Type; // 0-请求帮助 1-帮助进度有变化
                    if (_type == 0) OnReqUnionHelpList();
                    GameEntry.Event.Fire(UnionHelpChangeEventArgs.EventId, UnionHelpChangeEventArgs.Create(_type));
                    ColorLog.Pink("联盟帮助列表通知", message);
                }
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushUnionHelpTips, (message) =>
           {
               // 联盟帮助提示
               if (message != null)
               {
                   var data = (Union.PushUnionHelpTips)message;
                   helpTipList.Add(data);

                   var herpData = GetHelpData((int)data.QueueId);
                   if (herpData != null) herpData.HelpTimes = data.HelpTimes;

                   GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionHelpTipForm);
                   GameEntry.Event.Fire(UnionHelpTipEventArgs.EventId, UnionHelpTipEventArgs.Create(data));
                   ColorLog.Pink("联盟帮助提示", message);
               }
           });
        }

        private void SetRoleUnion(Roledata.RoleUnion roleUnion)
        {
            var lastUnionId = UnionId;

            UnionId = roleUnion.UnionId;
            UnionPosition = roleUnion.UnionPosition;
            UnionPermission = roleUnion.UnionPermission;
            UnionLeaveTime = roleUnion.UnionLeaveTime;
            IsFirstJoinUnion = roleUnion.IsFirstJoinUnion;
            ShowSpecialView = roleUnion.ShowSpecialView;
            IsOpenUnion = roleUnion.IsOpenUnion;
            UnionJoinTime = roleUnion.UnionJoinTime;

            OnChangeUnionId(lastUnionId, UnionId);
        }

        private void SetUnionBrief(Union.UnionBrief unionBrief)
        {
            var lastUnionId = UnionId;

            UnionId = unionBrief.Id;
            Name = unionBrief.Name;
            Level = unionBrief.Level;
            Flag = unionBrief.Flag;
            Lang = unionBrief.Lang;
            LeaderServerId = unionBrief.LeaderServerId;
            LeaderRoleId = unionBrief.LeaderRoleId;
            ShortName = unionBrief.ShortName;
            Notice = unionBrief.Notice;
            Members = unionBrief.Members;
            GiftExp = unionBrief.GiftExp;
            Power = unionBrief.Power;
            IsFreedomJoin = unionBrief.IsFreedomJoin;
            IsAnonymousGift = unionBrief.IsAnonymousGift;
            CreateTime = unionBrief.CreateTime;

            JoinPowerLimit = 0;
            JoinPowerLimit = 0;
            var conditions = unionBrief.Conditions;
            for (int i = 0; i < conditions.Count; i++)
            {
                var cond = conditions[i];
                if (cond.Type == 1) { JoinPowerLimit = cond.Value; }
                else { JoinLevelLimit = cond.Value; }
            }

            OnChangeUnionId(lastUnionId, UnionId);
        }

        private void InitOtherInfo()
        {
            if (UnionId > 0)
            {
                // 科技请求
                OnReqUnionTechList();

                // 获取申请列表,判断红点
                var config = GameEntry.LogicData.UnionData.GetUnionPermission();
                if (config != null && config.is_free)
                {
                    OnReqUnionJoinApplyList();
                }

                // 里程碑
                if (GetMileStoneRemainTime() > 0)
                {
                    OnReqUnionMileStoneList();
                }

                // 礼物
                OnReqUnionGiftList(PbGameconfig.gift_tag_type._1);
                OnReqUnionGiftList(PbGameconfig.gift_tag_type._2);

                // 帮助
                OnReqUnionHelpList();
            }
        }

        private void OnChangeUnionId(ulong lastUnionId, ulong unionId)
        {
            if (lastUnionId != unionId)
            {
                InitOtherInfo();
                GameEntry.Event.Fire(UnionJoinChangeEventArgs.EventId, UnionJoinChangeEventArgs.Create());
            }
        }

        #region 红点处理

        private void InitRedPoint()
        {
            RedPointManager.Instance.AddNodeTo(EnumRed.Root.ToString(), EnumRed.Union.ToString(), () =>
            {
                return 0;
            });
            RedPointManager.Instance.AddNodeTo(EnumRed.Union.ToString(), EnumRed.Union_Apply.ToString(), () =>
            {
                var config = GameEntry.LogicData.UnionData.GetUnionPermission();
                if (config != null && config.is_free)
                {
                    return applyRedCount;
                }
                return 0;
            });
            RedPointManager.Instance.AddNodeTo(EnumRed.Union.ToString(), EnumRed.Union_Tech.ToString(), () =>
            {
                var techDonateData = GetTechDonateData(Union.UnionDonateType.Gold);
                if (techLoginRed && techDonateData.DonateTimes > 0) return 1;

                var config = GetUnionConst(25);
                var maxNum = config != null ? int.Parse(config[0]) : 0;
                return techDonateData.DonateTimes >= maxNum ? 1 : 0;
            });
            RedPointManager.Instance.AddNodeTo(EnumRed.Union.ToString(), EnumRed.Union_Gift.ToString(), () =>
            {
                return 0;
            });
            RedPointManager.Instance.AddNodeTo(EnumRed.Union_Gift.ToString(), EnumRed.Union_Gift_Normal.ToString(), () =>
            {
                foreach (var item in normalGiftDic)
                {
                    if (item.Value.Status == Common.RewardStatus.Receivable)
                        return 1;
                }
                return 0;
            });
            RedPointManager.Instance.AddNodeTo(EnumRed.Union_Gift.ToString(), EnumRed.Union_Gift_High.ToString(), () =>
            {
                foreach (var item in highGiftDic)
                {
                    if (item.Value.Status == Common.RewardStatus.Receivable)
                        return 1;
                }
                return 0;
            });
        }

        #endregion


        #region 外部接口

        /// <summary>
        /// 是否加入了联盟
        /// </summary>
        /// <returns></returns>
        public bool IsJoinUnion()
        {
            return UnionId > 0;
        }

        /// <summary>
        /// 是否是会长
        /// </summary>
        /// <returns></returns>
        public bool IsLeader()
        {
            return LeaderRoleId == GameEntry.RoleData.RoleID;
        }

        /// <summary>
        /// 前往联盟
        /// </summary>
        public void GoToUnion()
        {
            if (IsJoinUnion())
            {
                OnReqUnionBrief(UnionId, (unionBrief) =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionForm);
                });
            }
            else
            {
                if (IsFirstJoinUnion && ShowSpecialView)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionFristForm);
                }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionCreateForm);
                }
            }
        }

        /// <summary>
        /// 关闭联盟所有界面
        /// </summary>
        public void CloseAllUnionForm()
        {
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIUnionCreateForm))
                GameEntry.UI.CloseUIForm(EnumUIForm.UIUnionCreateForm);
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIUnionSettingForm))
                GameEntry.UI.CloseUIForm(EnumUIForm.UIUnionSettingForm);
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIUnionForm))
                GameEntry.UI.CloseUIForm(EnumUIForm.UIUnionForm);
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIUnionExitWarnForm))
                GameEntry.UI.CloseUIForm(EnumUIForm.UIUnionExitWarnForm);
        }

        /// <summary>
        /// 在线状态文本
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public string GetOnlineStr(long time)
        {
            if (time <= 0) { return ToolScriptExtend.GetLang(80000105); }
            else if (time < 3600) { return ToolScriptExtend.GetLangFormat(80000106, time / 60 + ""); }
            else if (time < 86400) { return ToolScriptExtend.GetLangFormat(80000107, time / 3600 + ""); }
            else { return ToolScriptExtend.GetLangFormat(80000108, time / 86400 + ""); }
        }

        #endregion

        #region 联盟科技

        /// <summary>
        /// 设置联盟科技数据
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="techData"></param>
        private void SetUnionTechDic(int groupId, Union.UnionTech techData)
        {
            if (!unionTechDic.TryAdd(groupId, techData))
            {
                unionTechDic[groupId] = techData;
            }
        }

        /// <summary>
        /// 设置联盟科技加成数据
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="techData"></param>
        private void SetUnionTechAddDic(int groupId, Union.UnionTech techData)
        {
            var lastTechId = GetTechId(groupId);
            var lastConfig = GetTechLvConfig(lastTechId);
            var lastNum = 0;
            if (lastConfig != null)
            {
                var attr = lastConfig.union_tech_attributes;
                lastNum = attr.value;
            }

            if (techData.Status != Union.UnionStatus.Studying)
            {
                var techId = techData.Id + techData.Level;
                var config = GetTechLvConfig(techId);
                if (config != null)
                {
                    var addData = config.union_tech_attributes;
                    unionTechAddDic.TryAdd(addData.types, new());
                    unionTechAddDic.TryGetValue(addData.types, out tech_attributes dic);
                    dic.value += addData.value - lastNum;
                }
            }
        }

        /// <summary>
        /// 获取当前科技Id
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public int GetTechId(int groupId)
        {
            if (unionTechDic.TryGetValue(groupId, out Union.UnionTech data))
            {
                return data.Id + data.Level;
            }
            else
            {
                return groupId;
            }
        }

        /// <summary>
        /// 根据科技组id获取联盟科技数据
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public Union.UnionTech GetTechData(int groupId)
        {
            unionTechDic.TryGetValue(groupId, out Union.UnionTech data);
            return data ?? new();
        }

        /// <summary>
        /// 根据等级id获取联盟科技数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Union.UnionTech GetTechDataById(int id)
        {
            var config = GetTechLvConfig(id);
            if (config != null)
            {
                return GetTechData(config.tech_group);
            }
            return new();
        }

        /// <summary>
        /// 获取联盟科技捐献数据
        /// </summary>
        /// <param name="_type"></param>
        /// <returns></returns>
        public Union.UnionTechDonateData GetTechDonateData(Union.UnionDonateType _type)
        {
            unionTechDonateDic.TryGetValue(_type, out Union.UnionTechDonateData data);
            return data ?? new();
        }

        /// <summary>
        /// 获取联盟科技加成数据
        /// </summary>
        /// <param name="_type"></param>
        /// <returns></returns>
        public tech_attributes GetTechAddData(tech_effect_type _type)
        {
            unionTechAddDic.TryGetValue(_type, out tech_attributes data);
            return data ?? new();
        }

        /// <summary>
        /// 获取科技解锁状态
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public bool GetTechUnlock(int groupId)
        {
            var techId = GetTechId(groupId);
            var levelConfig = GetTechLvConfig(techId);
            var list = levelConfig.pre_id;
            var isUnlock = true;
            for (int i = 0; i < list.Count; i++)
            {
                var id = list[i];
                var data = GetTechDataById(id);
                var curId = data.Id + data.Level;
                if (id > curId)
                {
                    isUnlock = false;
                    break;
                }
            }
            return isUnlock;
        }

        /// <summary>
        /// 获取当前科技解锁下个科技状态
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public bool GetTechUnlockNext(int groupId)
        {
            var techData = GetTechData(groupId);
            return GetTechUnlock(groupId) && techData.Level > 0;
        }

        #endregion

        #region 里程碑

        /// <summary>
        /// 设置里程碑数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="data"></param>
        private void SetMileStoneDic(int id, Union.MileStone data)
        {
            if (!mileStoneDic.TryAdd(id, data))
            {
                mileStoneDic[id] = data;
            }
        }

        /// <summary>
        /// 获取里程碑数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Union.MileStone GetMileStoneData(int id)
        {
            mileStoneDic.TryGetValue(id, out Union.MileStone data);
            return data ?? new();
        }

        /// <summary>
        /// 获取里程碑剩余时间
        /// </summary>
        /// <returns></returns>
        public long GetMileStoneRemainTime()
        {
            var time = (long)TimeComponent.Now - CreateTime;
            var totalTime = GetMileStoneEndTime() + GetMilestoneCloseTime();
            var remainTime = totalTime - time;
            remainTime = remainTime < 0 ? 0 : remainTime;
            return remainTime;
        }

        /// <summary>
        /// 获取里程碑结束时间
        /// </summary>
        /// <returns></returns>
        public int GetMileStoneEndTime()
        {
            var list = GameEntry.LDLTable.GetTable<union_common_milestone>();
            var count = list.Count;
            if (count > 0)
            {
                var config = list[count - 1];
                return config.countdown + config.duration;
            }
            return 0;
        }

        #endregion

        #region 礼物

        /// <summary>
        /// 修改礼物数据
        /// </summary>
        /// <param name="_type"></param>
        /// <param name="data"></param>
        private void SetGiftDic(PbGameconfig.gift_tag_type _type, Union.UnionGift data)
        {
            var dic = _type == PbGameconfig.gift_tag_type._1 ? normalGiftDic : highGiftDic;
            if (!dic.TryAdd(data.Id, data))
            {
                dic[data.Id] = data;
            }
        }

        /// <summary>
        /// 获取礼物数据
        /// </summary>
        /// <param name="_type"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public Union.UnionGift GetGiftData(PbGameconfig.gift_tag_type _type, long id)
        {
            var dic = _type == PbGameconfig.gift_tag_type._1 ? normalGiftDic : highGiftDic;
            dic.TryGetValue(id, out Union.UnionGift data);
            return data ?? new();
        }

        /// <summary>
        /// 获取礼物功能解锁状态
        /// </summary>
        /// <param name="funcId"></param>
        /// <returns></returns>
        public bool GetGiftFuncUnlock(int funcId)
        {
            if (giftUnlockDic == null)
            {
                giftUnlockDic = new();
                var config = GameEntry.LDLTable.GetTable<union_gift_lv>();
                for (int i = 0; i < config.Count; i++)
                {
                    var data = config[i];
                    if (data.function_id > 0) giftUnlockDic.TryAdd(data.function_id, data.id);
                }
            }
            giftUnlockDic.TryGetValue(funcId, out int unlockLv);
            return Level >= unlockLv;
        }

        /// <summary>
        /// 获取高级礼物经验值
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public int GetHighGiftExp(int itemId)
        {
            var list = GetUnionConst(43);
            var index = -1;
            var giftId = itemId.ToString();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i] == giftId)
                {
                    index = i + 1;
                    break;
                }
            }
            var num = 0;
            if (index >= 0 || index < list.Count)
                int.TryParse(list[index], out num);
            return num;
        }

        #endregion

        #region 帮助

        /// <summary>
        /// 设置帮助数据
        /// </summary>
        /// <param name="data"></param>
        private void SetHelpData(Union.UnionHelp data)
        {
            if (!helpDic.TryAdd(data.QueueId, data))
            {
                helpDic[data.QueueId] = data;
            }
        }

        /// <summary>
        /// 获取帮助数据
        /// </summary>
        /// <param name="queueId"></param>
        /// <returns></returns>
        public Union.UnionHelp GetHelpData(int queueId)
        {
            helpDic.TryGetValue(queueId, out Union.UnionHelp data);
            return data;
        }

        /// <summary>
        /// 当前是否有需要帮助的事件
        /// </summary>
        /// <returns></returns>
        public bool IsNeedHelp()
        {
            foreach (var item in helpDic)
            {
                var data = item.Value;
                if (data.RoleId != GameEntry.RoleData.RoleID && !data.IsHelp && data.HelpTimes < data.MaxHelpTimes)
                    return true;
            }
            return false;
        }

        #endregion

        #region 配置处理

        /// <summary>
        /// 联盟常量配置
        /// </summary>
        /// <param name="configId"></param>
        /// <returns></returns>
        public List<string> GetUnionConst(int configId)
        {
            var config = GameEntry.LDLTable.GetTableById<union_const_setting>(configId);
            return config?.value;
        }

        /// <summary>
        /// 联盟权限配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public union_permission GetUnionPermission(int id = -1)
        {
            id = id > -1 ? id : UnionPermission;
            id = id > 0 ? id : 101;
            return GameEntry.LDLTable.GetTableById<union_permission>(id);
        }

        /// <summary>
        /// 联盟职位配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public union_position GetUnionPosition(int id = -1)
        {
            id = id > -1 ? id : UnionPosition;
            if (id <= 0) return null;
            return GameEntry.LDLTable.GetTableById<union_position>(id);
        }

        /// <summary>
        /// 联盟人员人数上限
        /// </summary>
        /// <returns></returns>
        public int GetMaxMember()
        {
            var list = GetUnionConst(10);
            return list != null ? Convert.ToInt32(list[0]) : 0;
        }

        /// <summary>
        /// R4初始上限名额
        /// </summary>
        /// <returns></returns>
        public int GetR4Count()
        {
            var list = GetUnionConst(11);
            return list != null ? Convert.ToInt32(list[0]) : 0;
        }

        /// <summary>
        /// 获取里程碑关闭时间
        /// </summary>
        /// <returns></returns>
        public int GetMilestoneCloseTime()
        {
            var list = GetUnionConst(40);
            return list != null ? Convert.ToInt32(list[0]) : 0;
        }

        /// <summary>
        /// 获取旗帜的图片路径
        /// </summary>
        /// <param name="configId"></param>
        /// <returns></returns>
        public string GetFlagImgPath(int configId)
        {
            var config = GameEntry.LDLTable.GetTableById<union_flag>(configId);
            return config != null ? config.res_location : "Sprite/ui_lianmeng/lmqizhi_icon1.png";
        }

        /// <summary>
        /// 获取联盟科技等级配置
        /// </summary>
        /// <param name="configId"></param>
        /// <returns></returns>
        public union_tech_lv GetTechLvConfig(int configId)
        {
            return GameEntry.LDLTable.GetTableById<union_tech_lv>(configId);
        }

        /// <summary>
        /// 获取联盟科技显示配置
        /// </summary>
        /// <param name="configId"></param>
        /// <returns></returns>
        public union_tech_show GetTechShowConfig(int configId)
        {
            return GameEntry.LDLTable.GetTableById<union_tech_show>(configId);
        }

        /// <summary>
        /// 获取里程碑配置
        /// </summary>
        /// <param name="configId"></param>
        /// <returns></returns>
        public union_common_milestone GetMilestoneConfig(int configId)
        {
            return GameEntry.LDLTable.GetTableById<union_common_milestone>(configId);
        }

        #endregion

        #region 协议处理

        /// <summary>
        /// 创建联盟请求
        /// </summary>
        /// <param name="name"></param>
        /// <param name="short_name"></param>
        /// <param name="lang"></param>
        /// <param name="flag"></param>
        /// <param name="action"></param>
        public void OnReqUnionCreate(string name, string short_name, int lang, int flag, Action action = null)
        {
            var req = new Union.UnionCreateReq
            {
                Name = name,
                ShortName = short_name,
                Lang = lang,
                Flag = flag,
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionCreate, req, (errorCode, message) =>
            {
                var resp = (Union.UnionCreateResp)message;
                if (resp != null && resp.UnionBrief != null)
                {
                    SetUnionBrief(resp.UnionBrief);
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 退出联盟请求（主动）
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionQuit(Action action = null)
        {
            var req = new Union.UnionQuitReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionQuit, req, (errorCode, message) =>
            {
                ColorLog.Pink("OnReqUnionQuit message:", message);
                UnionId = 0;
                action?.Invoke();
            });
        }

        /// <summary>
        /// 获取联盟简略信息
        /// </summary>
        /// <param name="union_id"></param>
        /// <param name="action"></param>
        public void OnReqUnionBrief(ulong union_id, Action<Union.UnionBrief> action = null)
        {
            var req = new Union.UnionBriefReq
            {
                UnionId = union_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionBrief, req, (errorCode, message) =>
            {
                var resp = (Union.UnionBriefResp)message;
                if (resp != null && resp.UnionBrief != null)
                {
                    var unionBrief = resp.UnionBrief;
                    if (unionBrief != null && unionBrief.Id == UnionId)
                    {
                        SetUnionBrief(resp.UnionBrief);
                    }
                    action?.Invoke(unionBrief);
                }
            });
        }

        /// <summary>
        /// 获取联盟列表
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionList(Action<Union.UnionListResp> action = null)
        {
            var req = new Union.UnionListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionListResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟申请请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionJoinApply(ulong union_id, Action<ulong> action = null)
        {
            var req = new Union.UnionJoinApplyReq
            {
                UnionId = union_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionJoinApply, req, (errorCode, message) =>
            {
                var resp = (Union.UnionJoinApplyResp)message;
                if (resp != null)
                {
                    action?.Invoke(resp.UnionId);

                    if (resp.UnionBrief != null)
                    {
                        SetUnionBrief(resp.UnionBrief);
                        GameEntry.LogicData.UnionData.CloseAllUnionForm();
                        GameEntry.LogicData.UnionData.GoToUnion();
                    }
                }
            });
        }

        /// <summary>
        /// 联盟申请列表请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionJoinApplyList(Action<Union.UnionJoinApplyListResp> action = null)
        {
            var req = new Union.UnionJoinApplyListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionJoinApplyList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionJoinApplyListResp)message;
                if (resp != null)
                {
                    var list = resp.List;
                    applyRedCount = list != null ? list.Count : 0;
                    RedPointManager.Instance.Dirty(EnumRed.Union_Apply.ToString());

                    action?.Invoke(resp);
                }
            });
        }

        /// <summary>
        /// 联盟申请同意请求
        /// </summary>
        /// <param name="role_id"></param>
        /// <param name="action"></param>
        public void OnReqUnionJoinApplyAgree(ulong role_id, Action<ulong> action = null)
        {
            var req = new Union.UnionJoinApplyAgreeReq
            {
                RoleId = role_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionJoinApplyAgree, req, (errorCode, message) =>
            {
                var resp = (Union.UnionJoinApplyAgreeResp)message;
                if (resp != null)
                {
                    action?.Invoke(resp.RoleId);
                }
            });
        }

        /// <summary>
        /// 联盟申请拒绝请求
        /// </summary>
        /// <param name="role_id"></param>
        /// <param name="action"></param>
        public void OnReqUnionJoinApplyRefuse(ulong role_id, Action<ulong> action = null)
        {
            var req = new Union.UnionJoinApplyRefuseReq
            {
                RoleId = role_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionJoinApplyRefuse, req, (errorCode, message) =>
            {
                var resp = (Union.UnionJoinApplyRefuseResp)message;
                if (resp != null)
                {
                    action?.Invoke(resp.RoleId);
                }
            });
        }

        /// <summary>
        /// 模糊搜索请求
        /// </summary>
        /// <param name="name"></param>
        /// <param name="action"></param>
        public void OnReqUnionSearch(string name, Action<Union.UnionSearchResp> action = null)
        {
            var req = new Union.UnionSearchReq
            {
                Name = name
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionSearch, req, (errorCode, message) =>
            {
                var resp = (Union.UnionSearchResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟一键入盟
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionOneKeyJoin(ulong recommend_union_id, Action action = null)
        {
            var req = new Union.UnionOneKeyJoinReq
            {
                RecommendUnionId = recommend_union_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionOneKeyJoin, req, (errorCode, message) =>
            {
                var resp = (Union.UnionOneKeyJoinResp)message;
                if (resp != null && resp.Brief != null)
                {
                    SetUnionBrief(resp.Brief);
                    action?.Invoke();
                    GameEntry.LogicData.UnionData.CloseAllUnionForm();
                    GameEntry.LogicData.UnionData.GoToUnion();
                }
            });
        }

        /// <summary>
        /// 联盟解散请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionDisband(Action action = null)
        {
            var req = new Union.UnionDisbandReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionDisband, req, (errorCode, message) =>
            {
                ColorLog.Pink("OnReqUnionDisband", errorCode, message);
                if (errorCode == 0)
                {
                    UnionId = 0;
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟踢出请求（踢人）
        /// </summary>
        /// <param name="role_id"></param>
        /// <param name="action"></param>
        public void OnReqUnionKickOut(ulong role_id, Action<ulong> action = null)
        {
            var req = new Union.UnionKickOutReq
            {
                RoleId = role_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionKickOut, req, (errorCode, message) =>
            {
                var resp = (Union.UnionKickOutResp)message;
                if (resp != null)
                    action?.Invoke(resp.RoleId);
            });
        }

        /// <summary>
        /// 联盟编辑公告请求
        /// </summary>
        /// <param name="notice"></param>
        /// <param name="action"></param>
        public void OnReqUnionEditNotify(string notice, Action action = null)
        {
            var req = new Union.UnionEditNotifyReq
            {
                Notice = notice
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionEditNotify, req, (errorCode, message) =>
            {
                var resp = (Union.UnionEditNotifyResp)message;
                if (resp != null && resp.Notice != null)
                {
                    Notice = resp.Notice;
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟旗帜修改请求
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangeFlag(int flag, Action<int> action = null)
        {
            var req = new Union.UnionChangeFlagReq
            {
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangeFlag, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangeFlagResp)message;
                if (resp != null)
                {
                    Flag = resp.Flag;
                    action?.Invoke(Flag);
                }
            });
        }

        /// <summary>
        /// 联盟改名请求
        /// </summary>
        /// <param name="name"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangeName(string name, Action<string> action = null)
        {
            var req = new Union.UnionChangeNameReq
            {
                Name = name
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangeName, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangeNameResp)message;
                if (resp != null && resp.Name != null)
                {
                    Name = resp.Name;
                    action?.Invoke(Name);
                }
            });
        }

        /// <summary>
        /// 联盟简称改名请求
        /// </summary>
        /// <param name="short_name"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangeShortName(string short_name, Action<string> action = null)
        {
            var req = new Union.UnionChangeShortNameReq
            {
                ShortName = short_name
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangeShortName, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangeShortNameResp)message;
                if (resp != null && resp.ShortName != null)
                {
                    ShortName = resp.ShortName;
                    action?.Invoke(ShortName);
                }
            });
        }

        /// <summary>
        /// 转让盟主
        /// </summary>
        /// <param name="role_id"></param>
        /// <param name="action"></param>
        public void OnReqUnionTransferLeader(ulong role_id, Action<ulong> action = null)
        {
            var req = new Union.UnionTransferLeaderReq
            {
                RoleId = role_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionTransferLeader, req, (errorCode, message) =>
            {
                var resp = (Union.UnionTransferLeaderResp)message;
                if (resp != null)
                {
                    LeaderRoleId = resp.RoleId;
                    action?.Invoke(LeaderRoleId);
                }
            });
        }

        /// <summary>
        /// 联盟邀请请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionInvite(Action action = null)
        {
            var req = new Union.UnionInviteReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionInvite, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟集结点请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionRallyPoint(Action action = null)
        {
            var req = new Union.UnionRallyPointReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionRallyPoint, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟邮件发送
        /// </summary>
        /// <param name="receivers"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="action"></param>
        public void OnReqUnionSendMail(List<int> receivers, string title, string content, Action action = null)
        {
            var req = new Union.UnionSendMailReq
            {
                Title = title,
                Content = content
            };
            req.Receivers.AddRange(receivers);

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionSendMail, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟权限调整
        /// </summary>
        /// <param name="role_id"></param>
        /// <param name="_type"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangePermission(ulong role_id, int _type, Action<Union.UnionChangePermissionResp> action = null)
        {
            var req = new Union.UnionChangePermissionReq
            {
                RoleId = role_id,
                Type = _type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangePermission, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangePermissionResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟加入条件修改
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangeJoinCondition(Union.UnionJoinCondition condition, Action<Union.UnionJoinCondition> action = null)
        {
            var req = new Union.UnionChangeJoinConditionReq
            {
                Conditions = condition
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangeJoinCondition, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangeJoinConditionResp)message;
                if (resp != null)
                {
                    var cond = resp.Conditions;
                    if (cond.Type == 1) { JoinPowerLimit = cond.Value; }
                    else { JoinLevelLimit = cond.Value; }
                    action?.Invoke(cond);
                }
            });
        }

        /// <summary>
        /// 联盟宣战
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionDeclareWar(Action action = null)
        {
            var req = new Union.UnionDeclareWarReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionDeclareWar, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟移除驻军
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionRemoveGarrison(Action action = null)
        {
            var req = new Union.UnionRemoveGarrisonReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionRemoveGarrison, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟放弃城市
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionGiveUpCity(Action action = null)
        {
            var req = new Union.UnionGiveUpCityReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionGiveUpCity, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟开放招募
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionOpenRecruit(Action action = null)
        {
            var req = new Union.UnionOpenRecruitReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionOpenRecruit, req, (errorCode, message) =>
            {
                action?.Invoke();
            });
        }

        /// <summary>
        /// 联盟日志
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionRecord(int log_type, Action<Union.UnionRecordResp> action = null)
        {
            var req = new Union.UnionRecordReq
            {
                Type = (PbGameconfig.log_type)log_type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionRecord, req, (errorCode, message) =>
            {
                var resp = (Union.UnionRecordResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟自由加入
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionFreeJoin(int flag, Action action = null)
        {
            var req = new Union.UnionFreeJoinReq
            {
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionFreeJoin, req, (errorCode, message) =>
            {
                var resp = (Union.UnionFreeJoinResp)message;
                if (resp != null)
                {
                    IsFreedomJoin = resp.Flag == 1;
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟礼物列表
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionGiftList(PbGameconfig.gift_tag_type _type, Action<List<long>> action = null)
        {
            var req = new Union.UnionGiftListReq
            {
                Type = _type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionGiftList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionGiftListResp)message;
                if (resp != null)
                {
                    var dic = resp.Type == PbGameconfig.gift_tag_type._1 ? normalGiftDic : highGiftDic;
                    dic.Clear();

                    var list = resp.Gifts;
                    List<long> idList = new();
                    for (int i = 0; i < list.Count; i++)
                    {
                        var data = list[i];
                        SetGiftDic(resp.Type, data);
                        idList.Add(data.Id);
                    }

                    var redId = resp.Type == PbGameconfig.gift_tag_type._1 ? EnumRed.Union_Gift_Normal : EnumRed.Union_Gift_High;
                    RedPointManager.Instance.Dirty(redId.ToString());

                    action?.Invoke(idList);
                }
            });
        }

        /// <summary>
        /// 联盟清除不活跃成员
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="action"></param>
        public void OnReqUnionCleanInactive(int flag, Action<bool> action = null)
        {
            var req = new Union.UnionCleanInactiveReq
            {
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionCleanInactive, req, (errorCode, message) =>
            {
                var resp = (Union.UnionCleanInactiveResp)message;
                if (resp != null)
                    action?.Invoke(resp.Flag == 1);
            });
        }

        /// <summary>
        /// 联盟清除所有不活跃成员(邮件)
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionCleanAllInactive(Action<Union.UnionCleanAllInactiveResp> action = null)
        {
            var req = new Union.UnionCleanAllInactiveReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionCleanAllInactive, req, (errorCode, message) =>
            {
                var resp = (Union.UnionCleanAllInactiveResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟礼物领取
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionGiftReceive(PbGameconfig.gift_tag_type _type, long id, Action<itemid> action = null)
        {
            var req = new Union.UnionGiftReceiveReq
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionGiftReceive, req, (errorCode, message) =>
            {
                if (errorCode != 0) return;

                var resp = (Union.UnionGiftReceiveResp)message;
                if (resp != null)
                {
                    SetGiftDic(_type, resp.Gift);

                    var redId = _type == PbGameconfig.gift_tag_type._1 ? EnumRed.Union_Gift_Normal : EnumRed.Union_Gift_High;
                    RedPointManager.Instance.Dirty(redId.ToString());

                    var list = resp.Gift.Article;
                    var rewardId = list.Count > 0 ? list[0].Code : 0;
                    action?.Invoke((itemid)rewardId);
                }
            });
        }

        /// <summary>
        /// 联盟礼物一键领取
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionGiftReceiveAll(PbGameconfig.gift_tag_type _type, Action<List<Article.Article>> action = null)
        {
            var req = new Union.UnionGiftReceiveAllReq
            {
                Type = _type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionGiftReceiveAll, req, (errorCode, message) =>
            {
                var resp = (Union.UnionGiftReceiveAllResp)message;
                if (resp != null)
                {
                    var list = resp.Article.ToList();
                    action?.Invoke(list);
                }
            });
        }

        /// <summary>
        /// 联盟职位调整
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionChangePosition(ulong role_id, int _type, Action<Union.UnionChangePositionResp> action = null)
        {
            var req = new Union.UnionChangePositionReq
            {
                RoleId = role_id,
                Type = _type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangePosition, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangePositionResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟成员列表
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionMemberList(ulong unionId, Action<Union.UnionMemberListResp> action = null)
        {
            var req = new Union.UnionMemberListReq
            {
                UnionId = unionId
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionMemberList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionMemberListResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟排行榜请求
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionRankList(Action<Union.UnionRankListResp> action = null)
        {
            var req = new Union.UnionRankListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionRankList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionRankListResp)message;
                if (resp != null)
                    action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟匿名礼物开关
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionAnonymousGiftSwitch(int flag, Action<bool> action = null)
        {
            var req = new Union.UnionAnonymousGiftSwitchReq
            {
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionAnonymousGiftSwitch, req, (errorCode, message) =>
            {
                var resp = (Union.UnionAnonymousGiftSwitchResp)message;
                if (resp != null)
                {
                    IsAnonymousGift = resp.Flag == 1;
                    action?.Invoke(IsAnonymousGift);
                }
            });
        }

        /// <summary>
        /// 联盟随机简称
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionRandomShortName(Action<string> action = null)
        {
            var req = new Union.UnionRandomShortNameReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionRandomShortName, req, (errorCode, message) =>
            {
                var resp = (Union.UnionRandomShortNameResp)message;
                if (resp != null)
                    action?.Invoke(resp.ShortName);
            });
        }

        /// <summary>
        /// 联盟校验名称
        /// </summary>
        /// <param name="name"></param>
        /// <param name="_type"></param>
        /// <param name="action"></param>
        public void OnReqUnionCheckName(string name, int _type, Action<Union.UnionCheckNameResp> action = null)
        {
            var req = new Union.UnionCheckNameReq
            {
                Name = name,
                Type = _type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionCheckName, req, (errorCode, message) =>
            {
                var resp = (Union.UnionCheckNameResp)message;
                action?.Invoke(resp);
            });
        }

        /// <summary>
        /// 联盟科技列表
        /// </summary>
        /// <param name="_type"></param>
        /// <param name="action"></param>
        public void OnReqUnionTechList(Action action = null)
        {
            var req = new Union.UnionTechListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionTechList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionTechListResp)message;
                if (resp != null)
                {
                    unionTechDic.Clear();
                    unionTechAddDic.Clear();
                    unionTechDonateDic.Clear();

                    var techs = resp.Techs.ToList();
                    for (int i = 0; i < techs.Count; i++)
                    {
                        var data = techs[i];
                        SetUnionTechDic(data.Id, data);

                        if (data.Status != Union.UnionStatus.Studying)
                        {
                            var techId = data.Id + data.Level;
                            var config = GetTechLvConfig(techId);
                            if (config != null)
                            {
                                var addData = config.union_tech_attributes;
                                unionTechAddDic.TryAdd(addData.types, new());
                                unionTechAddDic.TryGetValue(addData.types, out tech_attributes dic);
                                dic.value += addData.value;
                            }
                        }

                        if (data.Recommend)
                            recommendGroupId = data.Id;
                    }

                    var list = resp.DonateData.ToList();
                    for (int i = 0; i < list.Count; i++)
                    {
                        var data = list[i];
                        if (!unionTechDonateDic.TryAdd(data.Type, data))
                        {
                            unionTechDonateDic[data.Type] = data;
                        }
                    }

                    AutoUpgradeSwitch = resp.AutoUpgradeSwitch;

                    RedPointManager.Instance.Dirty(EnumRed.Union_Tech.ToString());
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟科技研究
        /// </summary>
        /// <param name="id"></param>
        /// <param name="action"></param>
        public void OnReqUnionTechStudy(int id, Action action = null)
        {
            var req = new Union.UnionTechStudyReq
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionTechStudy, req, (errorCode, message) =>
            {
                if (errorCode != 0) return;

                var resp = (Union.UnionTechStudyResp)message;
                if (resp != null)
                {
                    var techData = resp.Tech;
                    SetUnionTechAddDic(techData.Id, techData);
                    SetUnionTechDic(techData.Id, techData);
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟科技推荐
        /// </summary>
        /// <param name="techId"></param>
        /// <param name="flag"></param>
        /// <param name="action"></param>
        public void OnReqUnionSetRecommendTech(int techId, bool flag, Action action = null)
        {
            var req = new Union.UnionSetRecommendTechReq
            {
                TechId = techId,
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionSetRecommendTech, req, (errorCode, message) =>
            {
                var resp = (Union.UnionSetRecommendTechResp)message;
                if (resp != null)
                {
                    var lastData = GetTechData(recommendGroupId);
                    lastData.Recommend = false;

                    var techData = GetTechData(resp.TechId);
                    techData.Recommend = resp.Flag;
                    recommendGroupId = techData.Id;

                    if (techData.Id == 0)
                    {
                        techData.Id = resp.TechId;
                        SetUnionTechAddDic(techData.Id, techData);
                        SetUnionTechDic(techData.Id, techData);
                    }

                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟科技自动升级开关
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="action"></param>
        public void OnReqUnionTechAutoUpgradeSwitch(bool flag, Action action = null)
        {
            var req = new Union.UnionTechAutoUpgradeSwitchReq
            {
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionTechAutoUpgradeSwitch, req, (errorCode, message) =>
            {
                var resp = (Union.UnionTechAutoUpgradeSwitchResp)message;
                if (resp != null)
                {
                    AutoUpgradeSwitch = resp.Flag;
                    action?.Invoke();
                    ColorLog.Pink("OnReqUnionTechAutoUpgradeSwitch ", AutoUpgradeSwitch);
                }
            });
        }

        /// <summary>
        /// 联盟科技捐献响应
        /// </summary>
        /// <param name="id"></param>
        /// <param name="_type"></param>
        /// <param name="action"></param>
        public void OnReqUnionTechDonate(int id, int _type, Action<int> action = null)
        {
            var req = new Union.UnionTechDonateReq
            {
                Id = id,
                Type = (Union.UnionDonateType)_type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionTechDonate, req, (errorCode, message) =>
            {
                if (errorCode != 0) return;

                var resp = (Union.UnionTechDonateResp)message;
                if (resp != null)
                {
                    var tech = resp.Tech;
                    if (tech != null)
                    {
                        SetUnionTechAddDic(tech.Id, tech);
                        SetUnionTechDic(tech.Id, tech);

                        var data = resp.DonateData;
                        if (!unionTechDonateDic.TryAdd(data.Type, data))
                        {
                            unionTechDonateDic[data.Type] = data;
                        }
                        RedPointManager.Instance.Dirty(EnumRed.Union_Tech.ToString());
                        action?.Invoke(resp.Multiple);
                    }
                }
            });
        }

        /// <summary>
        /// 联盟获取清除不活跃成员开关状态
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionGetCleanSwitch(Action<bool> action = null)
        {
            var req = new Union.UnionGetCleanInactiveSwitchReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionGetCleanInactiveSwitch, req, (errorCode, message) =>
            {
                var resp = (Union.UnionGetCleanInactiveSwitchResp)message;
                if (resp != null)
                    action?.Invoke(resp.Flag == 1);
            });
        }

        /// <summary>
        /// 联盟语言修改
        /// </summary>
        /// <param name="langType"></param>
        /// <param name="action"></param>
        public void OnReqUnionChangeLang(int langType, Action<int> action = null)
        {
            var req = new Union.UnionChangeLangReq
            {
                Lang = langType
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionChangeLang, req, (errorCode, message) =>
            {
                var resp = (Union.UnionChangeLangResp)message;
                if (resp != null)
                {
                    Lang = resp.Lang;
                    action?.Invoke(Lang);
                }
            });
        }

        /// <summary>
        /// 联盟里程碑列表
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionMileStoneList(Action action = null)
        {
            var req = new Union.UnionMileStoneListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionMileStoneList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionMileStoneListResp)message;
                if (resp != null)
                {
                    var list = resp.MileStones;
                    for (int i = 0; i < list.Count; i++)
                    {
                        var data = list[i];
                        SetMileStoneDic(data.Id, data);
                    }
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟里程碑奖励领取
        /// </summary>
        /// <param name="id"></param>
        /// <param name="action"></param>
        public void OnReqUnionMileStoneReward(int id, Action action = null)
        {
            var req = new Union.UnionMileStoneRewardReq
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionMileStoneReward, req, (errorCode, message) =>
            {
                var resp = (Union.UnionMileStoneRewardResp)message;
                if (resp != null)
                {
                    var data = resp.Milestone;
                    SetMileStoneDic(data.Id, data);
                    action?.Invoke();
                }
            });
        }

        /// <summary>
        /// 联盟帮助列表
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionHelpList(Action<int, List<Union.UnionHelp>> action = null)
        {
            var req = new Union.UnionHelpListReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionHelpList, req, (errorCode, message) =>
            {
                var resp = (Union.UnionHelpListResp)message;
                if (resp != null)
                {
                    helpDic.Clear();

                    var list = resp.HelpList.ToList();
                    for (int i = 0; i < list.Count; i++)
                    {
                        SetHelpData(list[i]);
                    }

                    action?.Invoke(resp.Score, list);
                }
            });
        }

        /// <summary>
        /// 联盟帮助全部
        /// </summary>
        /// <param name="action"></param>
        public void OnReqUnionHelpAll(Action<int, List<Union.UnionHelp>> action = null)
        {
            var req = new Union.UnionHelpAllReq();

            GameEntry.LDLNet.Send(Protocol.MessageID.UnionHelpAll, req, (errorCode, message) =>
            {
                var resp = (Union.UnionHelpAllResp)message;
                if (resp != null)
                {
                    helpDic.Clear();

                    var list = resp.HelpList.ToList();
                    for (int i = 0; i < list.Count; i++)
                    {
                        SetHelpData(list[i]);
                    }

                    action?.Invoke(resp.Score, list);
                }
            });
        }

        /// <summary>
        /// 排行榜列表
        /// </summary>
        /// <param name="_type"></param>
        /// <param name="sub_type"></param>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <param name="action"></param>
        public void OnReqRankList(int _type, int sub_type = 0, int from = 0, int to = -1, Action<Rank.RankListResp> action = null)
        {
            var req = new Rank.RankListReq
            {
                Type = (Rank.RankType)_type,
                SubType = (Rank.RankSubType)sub_type,
                From = from,
                To = to
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RankList, req, (errorCode, message) =>
            {
                var resp = (Rank.RankListResp)message;
                if (resp != null)
                {
                    action?.Invoke(resp);
                }
            });
        }

        #endregion
    }
}

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using GameFramework.Event;
using Game.Hotfix.Config;
using DG.Tweening;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattlePlanForm : UGuiFormEx
    {
        readonly List<Team.TeamType> teamTypeAttack = new()
        {
            Team.TeamType.TradeTrainAttack1,
            Team.TeamType.TradeTrainAttack2,
            Team.TeamType.TradeTrainAttack3,
        };

        readonly List<Team.TeamType> teamTypeDefend = new()
        {
            Team.TeamType.TradeTrainDefend1,
            Team.TeamType.TradeTrainDefend2,
            Team.TeamType.TradeTrainDefend3,
        };

        readonly List<Vector2> heroPos = new()
        {
            new(-128f, 74f),
            new(128, 74f),
            new(-128, -55f),
            new(0, -55f),
            new(128, -55f),
        };

        readonly List<Vector2> teamPos = new()
        {
            new(0f, 447f),
            new(0f, 36f),
            new(0f, -352f),
        };

        Dictionary<int, string> bgDict = new()
        {
            { 1, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang1.png" },
            { 2, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang2.png" },
            { 3, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang3.png" },
        };

        Trade.TradeCargoTransport trainData;
        bool isPlayingAnim;

        string animNameVS = "UITradeTrainBattlePlanFormPlayVSEffect";
        string animNameNext = "UITradeTrainBattlePlanFormPlayNextAnim";

        Sequence sequence;
        Sequence wheelSequence;

        int healthLeft;
        int healthRight;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Trade.TradeCargoTransport data)
            {
                trainData = data;
            }

            healthLeft = 3;
            healthRight = 3;
            RefreshHealth(m_transPlayerLeft, healthLeft);
            RefreshHealth(m_transPlayerRight, healthRight);

            HideDefault();
            RefreshPanel();
            ResetTeam(m_transTeamLeft);
            ResetTeam(m_transTeamRight);
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);
            Timers.Instance.Remove(animNameVS);
            isPlayingAnim = false;

            sequence?.Kill();
            sequence = null;
            wheelSequence?.Kill();
            wheelSequence = null;
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            SetParticleSystemSortingOrder(m_goVSEffect, Depth);
            SetParticleSystemSortingOrder(m_goCrashEffect, Depth);

            foreach (Transform item in m_transTeamLeft)
            {
                SetParticleSystemSortingOrder(item.gameObject, Depth);
            }

            foreach (Transform item in m_transTeamRight)
            {
                SetParticleSystemSortingOrder(item.gameObject, Depth);
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {

            }

            RefreshTeam(m_transTeamLeft);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnFightClick()
        {
            PlayFightAnim();
            return;

            if (trainData == null)
            {
                ColorLog.Red("火车数据为空");
                return;
            }

            List<Team.TeamType> attack = new();
            List<Team.TeamType> defend = new();

            foreach (var item in teamTypeAttack)
            {
                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(item);
                if (teamData != null && teamData.Count > 0)
                {
                    attack.Add(item);
                }
            }

            GameEntry.TradeTruckData.RequestTrainStartFight(trainData.Id, attack, defend, (result) =>
            {
                ColorLog.Pink("火车开始战斗", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattleResultForm, trainData);
            });
        }

        private void OnBtnExchange1Click()
        {

        }

        private void OnBtnExchange2Click()
        {

        }

        void InitPanel()
        {

        }

        void HideDefault()
        {
            m_goVSEffect.SetActive(false);
            m_goCrashEffect.SetActive(false);
        }

        void ResetTeam(Transform parent)
        {
            foreach (Transform teamParent in parent)
            {
                teamParent.DOKill();
                int index = int.Parse(teamParent.gameObject.name.Substring(teamParent.gameObject.name.Length - 1, 1));
                GameObject battle_huoche_shaoguang = teamParent.Find("battle_huoche_shaoguang").gameObject;
                battle_huoche_shaoguang.SetActive(false);
                GameObject battle_huoche_xuanzhong = teamParent.Find("battle_huoche_xuanzhong").gameObject;
                battle_huoche_xuanzhong.SetActive(false);
                SetTeamGrey(teamParent, false, parent == m_transTeamLeft);
                RectTransform rect = teamParent.GetComponent<RectTransform>();
                rect.anchoredPosition = teamPos[index - 1];
                if (index == 1) teamParent.localScale = new Vector3(1.1f, 1.1f, 1f);
                else teamParent.localScale = Vector3.one;

                teamParent.localRotation = Quaternion.identity;
            }

            Transform team1 = parent.Find("team1");
            Transform team2 = parent.Find("team2");
            Transform team3 = parent.Find("team3");
            team1.SetSiblingIndex(2);
            team2.SetSiblingIndex(1);
            team3.SetSiblingIndex(0);
        }

        void RefreshPanel()
        {
            RefreshPlayer(m_transPlayerLeft);
            RefreshPlayer(m_transPlayerRight);
            RefreshTeam(m_transTeamLeft);
            RefreshTeam(m_transTeamRight);
        }

        void RefreshPlayer(Transform parent)
        {
            UIText txtName = parent.Find("txtName").GetComponent<UIText>();
            UIText txtPower = parent.Find("txtPower").GetComponent<UIText>();

            ulong roleID = 0;
            if (parent == m_transPlayerLeft)
            {
                roleID = GameEntry.RoleData.RoleID;
            }
            else if (parent == m_transPlayerRight && trainData != null)
            {
                roleID = trainData.RoleId;
            }

            if (roleID != 0)
            {
                GameEntry.RoleData.RequestRoleQueryLocalSingle(roleID, (roleBrief) =>
                {
                    ColorLog.Pink("查询玩家信息", roleBrief);
                    if (roleBrief != null)
                    {
                        txtName.text = roleBrief.Name;
                        txtPower.text = ToolScriptExtend.FormatNumberWithUnit(roleBrief.Power);
                    }
                });
            }
        }

        void RefreshTeam(Transform parent)
        {
            foreach (Transform teamParent in parent)
            {
                int index = int.Parse(teamParent.gameObject.name.Substring(teamParent.gameObject.name.Length - 1, 1));
                Team.TeamType teamType = Team.TeamType.TradeTrainAttack1;
                if (parent == m_transTeamLeft)
                {
                    teamType = teamTypeAttack[index - 1];
                }
                else if (parent == m_transTeamRight)
                {
                    teamType = teamTypeDefend[index - 1];
                }

                UIButton btnBg = teamParent.Find("bg").GetComponent<UIButton>();
                UIImage bg = teamParent.Find("bg").GetComponent<UIImage>();
                UIText txtNumber = teamParent.Find("txtNumber").GetComponent<UIText>();
                UIText txtPower = teamParent.Find("txtPower").GetComponent<UIText>();
                Transform heroParent = teamParent.Find("hero");

                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);

                txtPower.gameObject.SetActive(false);
                txtPower.text = string.Empty;

                if (teamData != null)
                {
                    ulong powerTotal = 0;
                    foreach (var data in teamData)
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)data.HeroId);
                        powerTotal += heroVo.power;
                    }
                    txtPower.text = ToolScriptExtend.FormatNumberWithUnit(powerTotal);
                    txtPower.gameObject.SetActive(powerTotal > 0);
                }

                for (int i = 0; i < 5; i++)
                {
                    Transform transHeroItem;
                    if (i < heroParent.childCount)
                    {
                        transHeroItem = heroParent.GetChild(i);
                    }
                    else
                    {
                        transHeroItem = Instantiate(m_transHeroItem, heroParent);
                    }
                    RectTransform rect = transHeroItem.GetComponent<RectTransform>();
                    rect.anchoredPosition = heroPos[i];
                    transHeroItem.gameObject.SetActive(true);
                    GameObject empty = transHeroItem.Find("empty").gameObject;
                    UIHeroItem item = transHeroItem.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    if (teamData != null)
                    {
                        Fight.FormationHero heroData = null;
                        foreach (var hero in teamData)
                        {
                            if (hero.Pos == i + 1)
                            {
                                heroData = hero;
                            }
                        }

                        if (heroData != null)
                        {
                            HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                            item.Refresh(heroModule);
                            item.HideTeamBg();
                            item.gameObject.SetActive(true);
                            empty.SetActive(false);
                        }
                        else
                        {
                            item.gameObject.SetActive(false);
                            empty.SetActive(true);
                        }
                    }
                    else
                    {
                        item.gameObject.SetActive(false);
                        empty.SetActive(true);
                    }
                }

                btnBg.onClick.RemoveAllListeners();
                btnBg.onClick.AddListener(() =>
                {
                    if (isPlayingAnim) return;
                    if (parent == m_transTeamRight) return;
                    UITeamFormParam param = new()
                    {
                        Index = index - 1,
                        TeamFormType = UITeamFormType.TradeTrainAttack
                    };
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
                    m_goVSEffect.SetActive(false);
                    m_goCrashEffect.SetActive(false);
                    Timers.Instance.Remove(animNameVS);
                });
            }
        }

        void PlayFightAnim()
        {
            if (isPlayingAnim)
            {
                ColorLog.Yellow("正在播放动画");
                return;
            }

            isPlayingAnim = true;

            m_goVSEffect.SetActive(true);
            m_goCrashEffect.SetActive(false);
            m_btnExchange1.gameObject.SetActive(false);
            m_btnExchange2.gameObject.SetActive(false);

            Timers.Instance.Remove(animNameVS);

            Timers.Instance.Add(animNameVS, 1f, (param) =>
            {
                m_goVSEffect.SetActive(false);
                PlayCrashAnim();
            }, 1);
        }

        void PlayCrashAnim()
        {
            Transform teamLeft = m_transTeamLeft.GetChild(2);
            Transform teamRight = m_transTeamRight.GetChild(2);

            float duration1 = 0.3f;
            float duration2 = 0.05f;

            sequence?.Kill();
            sequence = null;

            sequence = DOTween.Sequence();
            sequence.Insert(0f, teamLeft.DOLocalMoveX(-460f, duration1).SetEase(Ease.InQuad));
            sequence.Insert(0f, teamRight.DOLocalMoveX(460f, duration1).SetEase(Ease.InQuad));
            sequence.Insert(duration1, teamLeft.DOLocalMoveX(0f, duration2).SetEase(Ease.InQuad));
            sequence.Insert(duration1, teamRight.DOLocalMoveX(0f, duration2).SetEase(Ease.InQuad));
            sequence.AppendCallback(() =>
            {
                m_goCrashEffect.SetActive(true);
            });
            sequence.Insert(duration1 + duration2, teamLeft.DOPunchPosition(new Vector3(0f, 70f, 0), 0.4f, 10, 1f));
            sequence.Insert(duration1 + duration2, teamRight.DOPunchPosition(new Vector3(0f, 70f, 0), 0.4f, 10, 1f));
            sequence.AppendCallback(() =>
            {
                m_goCrashEffect.SetActive(false);
                SetTeamGrey(teamLeft, true, true);
                PlayWinEffect(teamRight);
            });
            sequence.AppendInterval(1f);
            sequence.OnComplete(() =>
            {
                healthLeft--;
                RefreshHealth(m_transPlayerLeft, healthLeft);
                RefreshHealth(m_transPlayerRight, healthRight);

                if (healthLeft <= 0 || healthRight <= 0)
                {
                    // isPlayingAnim = false;
                    return;
                }

                PlayWheelAnim();
            });
        }

        void SetTeamGrey(Transform parent, bool isGrey, bool isLeft)
        {
            UIImage bg = parent.Find("bg").GetComponent<UIImage>();
            UIImage powerIcon = parent.Find("txtPower/Image").GetComponent<UIImage>();
            Transform heroParent = parent.Find("hero");
            if (isGrey)
            {
                bg.SetImage(bgDict[1]);
                powerIcon.SetImageGray(true);
                foreach (Transform item in heroParent)
                {
                    UIHeroItem heroItem = item.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    heroItem.SetGrey(true);
                }
            }
            else
            {
                if (isLeft)
                {
                    bg.SetImage(bgDict[3]);
                }
                else
                {
                    bg.SetImage(bgDict[2]);
                }
                foreach (Transform item in heroParent)
                {
                    UIHeroItem heroItem = item.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    heroItem.SetGrey(false);
                }
            }
        }

        void PlayWinEffect(Transform parent)
        {
            Transform effect = parent.Find("battle_huoche_shaoguang");
            effect.gameObject.SetActive(false);
            effect.gameObject.SetActive(true);
        }

        void HideWinEffect(Transform parent)
        {
            foreach (Transform item in parent)
            {
                Transform effect = item.Find("battle_huoche_shaoguang");
                effect.gameObject.SetActive(false);
            }
        }

        void PlaySelectedEffect(Transform parent)
        {
            Transform effect = parent.Find("battle_huoche_xuanzhong");
            effect.gameObject.SetActive(false);
            effect.gameObject.SetActive(true);
        }

        void HideSelectedEffect(Transform parent)
        {
            foreach (Transform item in parent)
            {
                Transform effect = item.Find("battle_huoche_xuanzhong");
                effect.gameObject.SetActive(false);
            }
        }

        void PlayWheelAnim()
        {
            Transform teamLeft1 = m_transTeamLeft.GetChild(2);
            Transform teamLeft2 = m_transTeamLeft.GetChild(1);
            Transform teamLeft3 = m_transTeamLeft.GetChild(0);

            Transform teamRight1 = m_transTeamRight.GetChild(2);
            Transform teamRight2 = m_transTeamRight.GetChild(1);
            Transform teamRight3 = m_transTeamRight.GetChild(0);

            if (teamLeft1 == null || teamLeft2 == null || teamLeft3 == null)
                return;
            if (teamRight1 == null || teamRight2 == null || teamRight3 == null)
                return;

            // 记录初始位置和旋转
            Vector3 team1InitPos = teamLeft1.localPosition;
            Vector3 team2InitPos = teamLeft2.localPosition;
            Vector3 team3InitPos = teamLeft3.localPosition;
            Vector3 team1InitRotation = teamLeft1.localEulerAngles;

            // 动画持续时间
            float duration = 0.8f;

            wheelSequence?.Kill();
            wheelSequence = null;

            // 创建动画序列
            wheelSequence = DOTween.Sequence();

            // teamLeft1 沿曲线轨迹移动到 teamLeft3 的位置，同时处理旋转
            Vector3[] curvePath = new Vector3[]
            {
                team1InitPos,
                new Vector3(team1InitPos.x - 100f, (team1InitPos.y + team3InitPos.y) * 0.5f, team1InitPos.z), // 向左偏移的中间点
                team3InitPos
            };

            // 先向右倾斜，然后在移动过程中恢复
            wheelSequence.Join(teamLeft1.DOLocalRotate(new Vector3(0, 0, -45f), duration * 0.3f).SetEase(Ease.OutQuart));
            wheelSequence.Join(teamLeft1.DOLocalPath(curvePath, duration, PathType.CatmullRom).SetEase(Ease.OutQuart));
            wheelSequence.Join(teamLeft1.DOScale(1f, 0.2f).SetEase(Ease.OutQuart));
            wheelSequence.Join(teamLeft1.DOLocalRotate(team1InitRotation, duration * 0.7f).SetEase(Ease.OutQuart).SetDelay(duration * 0.3f));

            // teamLeft2 向上移动到 teamLeft1 的位置
            wheelSequence.Join(teamLeft2.DOLocalMove(team1InitPos, duration).SetEase(Ease.OutQuart));
            wheelSequence.Join(teamLeft2.DOScale(1.1f, 0.2f).SetEase(Ease.OutQuart));

            // teamLeft3 向上移动到 teamLeft2 的位置
            wheelSequence.Join(teamLeft3.DOLocalMove(team2InitPos, duration).SetEase(Ease.OutQuart));

            // 动画完成后的回调
            wheelSequence.OnComplete(() =>
            {
                // 重新排列子物体的顺序，使其在层级中的顺序与视觉位置匹配
                teamLeft2.SetSiblingIndex(2); // team2 现在在 team1 的原位置
                teamLeft3.SetSiblingIndex(1); // team3 现在在 team2 的原位置
                teamLeft1.SetSiblingIndex(0); // team1 现在在 team3 的原位置

                PlaySelectedEffect(teamLeft2);
                CheckNextAnim();
            });

            // 播放动画
            wheelSequence.Play();
        }

        void CheckNextAnim()
        {
            if (healthLeft <= 0 || healthRight <= 0)
            {
                HideWinEffect(m_transTeamLeft);
                HideWinEffect(m_transTeamRight);
                HideSelectedEffect(m_transTeamLeft);
                HideSelectedEffect(m_transTeamRight);
                // isPlayingAnim = false;
                return;
            }

            Timers.Instance.Remove(animNameNext);

            Timers.Instance.Add(animNameNext, 1f, (param) =>
            {
                HideWinEffect(m_transTeamLeft);
                HideWinEffect(m_transTeamRight);
                HideSelectedEffect(m_transTeamLeft);
                HideSelectedEffect(m_transTeamRight);
                PlayCrashAnim();
            }, 1);
        }

        void RefreshHealth(Transform parent, int curHealth)
        {
            Transform health = parent.Find("health");
            for (int i = 0; i < health.childCount; i++)
            {
                if (i < curHealth)
                {
                    health.GetChild(i).gameObject.SetActive(true);
                }
                else
                {
                    health.GetChild(i).gameObject.SetActive(false);
                }
            }
        }
    }
}

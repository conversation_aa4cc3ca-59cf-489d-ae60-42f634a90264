using System.Collections.Generic;
using GameFramework.Event;
using UnityEngine;
using Wmcamera;

namespace Game.Hotfix
{
    public class WorldMapComponent : BaseSceneComponent
    {
        public delegate void CameraMoveDelegate();
        public delegate void CameraZoomDelegate();
        public delegate void GridDataDirtyDelegate(HashSet<Vector2Int> data);
        public delegate void TroopDirtyDelegate();
        
        public WorldMapLOD CurLODLevel => m_CurLODLevel;
        
        public Vector3[] IntersectionPoints => m_IntersectionPoints;

        public Vector2Int WorldMapSize = new Vector2Int(1000, 1000);

        private Dictionary<Vector3Int, List<Vector2Int>> m_InShowCacheGrids = new Dictionary<Vector3Int, List<Vector2Int>>();
        
        // private 
        private WorldMapOperationCtrl m_MapOpCtrl;
        private WMCmpLogicCameraCtrl m_LogicCameraCtrl;
        private WMCmpGroundDrawer m_GroundDrawer;
        private WMCmpBuildableGroundDrawer m_BuildableGroundDrawer;
        private WMCmpUnBuildableGroundDrawer m_UnBuildableGroundDrawer;
        private WMCmpTreeDrawer m_TreeDrawer;
        private WMCmpAreaBorderDrawer m_AreaBorderDrawer;
        private WMCmpPreviewDrawer m_PreviewDrawer;
        private WMCmpCityDrawer m_CityDrawer;
        private WMCmpMonsterDrawer m_MonsterDrawer;
        private WMCmpFortsDrawer m_FortsDrawer;
        private WMCmpMineDrawer m_MineDrawer;
        private WMCmpTownDrawer m_TownDrawer;
        private WMCmpMyTownDrawer m_MyTownDrawer;
        private WMCmpTroopDrawer m_TroopDrawer;
        
        //相机相关
        private Camera m_CacheCamera;
        private Plane m_ProjectionPlane = new Plane(Vector3.up, new Vector3(0, 0, 0));
        private Vector3[] m_ViewportCorners;
        private Vector3[] m_IntersectionPoints = new Vector3[4];
        private Vector2Int[] m_IntersectionGrids = new Vector2Int[4];
        private bool[] m_HasIntersection = new bool[4];

        private bool hasDirtyMove = false;
        private bool hasDirtyZoom = false;

        private int? m_PreviewUISerialId;

        public CameraMoveDelegate OnCameraMoveCall = null;
        public CameraZoomDelegate OnCameraZoomCall = null;
        public GridDataDirtyDelegate OnGridDataDirty = null;
        public TroopDirtyDelegate OnTroopDirty = null;
        
        private WorldMapLOD m_CurLODLevel = WorldMapLOD.LevelNone;
        
        //地面小标识
        private int? m_GridClickEntityId;
        
        protected override int GetSceneId()
        {
            return (int)SceneDefine.WorldMapScene;
        }

        protected override void OnStart()
        {
            base.OnStart();

            GameEntry.LogicData.WorldMapData.ClearAllData(true);
            
            //相机相关
            m_ViewportCorners = new Vector3[4];
            m_ViewportCorners[0] = new Vector3(0, 0, 0); // Bottom-left
            m_ViewportCorners[1] = new Vector3(1, 0, 0); // Bottom-right
            m_ViewportCorners[2] = new Vector3(0, 1, 0); // Top-left
            m_ViewportCorners[3] = new Vector3(1, 1, 0); // Top-right
            
            
            //控制器
            m_MapOpCtrl = new WorldMapOperationCtrl();
            m_LogicCameraCtrl = new WMCmpLogicCameraCtrl(this);
            
            m_GroundDrawer = new WMCmpGroundDrawer(this);
            m_BuildableGroundDrawer = new WMCmpBuildableGroundDrawer(this);
            m_UnBuildableGroundDrawer = new WMCmpUnBuildableGroundDrawer(this);
            m_TreeDrawer = new WMCmpTreeDrawer(this);
            m_AreaBorderDrawer = new WMCmpAreaBorderDrawer(this);
            m_PreviewDrawer = new WMCmpPreviewDrawer(this);
            m_CityDrawer = new WMCmpCityDrawer(this);
            m_MonsterDrawer = new WMCmpMonsterDrawer(this);
            m_FortsDrawer = new WMCmpFortsDrawer(this);
            m_MineDrawer = new WMCmpMineDrawer(this);
            m_TownDrawer = new WMCmpTownDrawer(this);
            m_MyTownDrawer = new WMCmpMyTownDrawer(this);
            m_TroopDrawer = new WMCmpTroopDrawer(this);
            
            m_LogicCameraCtrl.Init();
            
            m_GroundDrawer.Init();
            m_BuildableGroundDrawer.Init();
            m_UnBuildableGroundDrawer.Init();
            m_TreeDrawer.Init();
            m_AreaBorderDrawer.Init();
            m_PreviewDrawer.Init();
            m_CityDrawer.Init();
            m_MonsterDrawer.Init();
            m_FortsDrawer.Init();
            m_MineDrawer.Init();
            m_TownDrawer.Init();
            m_MyTownDrawer.Init();
            m_TroopDrawer.Init();

            var homePos = GameEntry.LogicData.WorldMapData.HomePos;
            GameEntry.Camera.LookAtPosition(new Vector3(homePos.x, 0, homePos.y),
                GameDefine.WorldMapCameraDefaultDistance, null, 0);
            
            GameEntry.Event.Subscribe(OnWorldMapCameraMoveArgs.EventId,OnWorldMapCameraMove);
            GameEntry.Event.Subscribe(OnWorldMapCameraZoomArgs.EventId,OnWorldMapCameraZoom);
            GameEntry.Event.Subscribe(OnWorldMapTapEventArgs.EventId,OnWorldMapTapEvent);
            GameEntry.Event.Subscribe(OnOpModeChangeEventArgs.EventId,OnOpModeChangeEvent);
            GameEntry.Event.Subscribe(OnWorldMapGridDataDirtyArgs.EventId,OnWorldMapGridDataDirty);
        }

        private void OnWorldMapGridDataDirty(object sender, GameEventArgs e)
        {
            if (e is OnWorldMapGridDataDirtyArgs args)
            {
                OnGridDataDirty?.Invoke(args.DirtySet);
                hasDirtyMove = true;    
            }
        }

        private void OnOpModeChangeEvent(object sender, GameEventArgs e)
        {
            if (e is OnOpModeChangeEventArgs args)
            {
                if (args.oldOp == MapOpType.TOWN_MOVE_MODE && args.newOp == MapOpType.DEFAULT_MODE)
                    TryShowUIWorldMapPreview();
            }
        }

        private void OnWorldMapTapEvent(object sender, GameEventArgs e)
        {
            if (e is OnWorldMapTapEventArgs args)
            {
                TryHideGridClickEntity();
                var entityLogics = args.EntityLogic;
                if (entityLogics.Count>0)
                {
                    if (entityLogics.Count <= 1)
                    {
                        entityLogics[0]?.OnClick();
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapSelectionConfirm, entityLogics);
                    }
                }
                else
                {
                    TryShowGridClickEntity(args);    
                }
            }
        }

        private void OnWorldMapCameraZoom(object sender, GameEventArgs e)
        {
            hasDirtyZoom = true;
        }

        private void OnWorldMapCameraMove(object sender, GameEventArgs e)
        {
            hasDirtyMove = true;
        }

        protected override void OnDestroy()
        {
            GameEntry.Event.Unsubscribe(OnWorldMapCameraMoveArgs.EventId,OnWorldMapCameraMove);
            GameEntry.Event.Unsubscribe(OnWorldMapCameraZoomArgs.EventId,OnWorldMapCameraZoom);
            GameEntry.Event.Unsubscribe(OnWorldMapTapEventArgs.EventId,OnWorldMapTapEvent);
            GameEntry.Event.Unsubscribe(OnOpModeChangeEventArgs.EventId,OnOpModeChangeEvent);
            GameEntry.Event.Unsubscribe(OnWorldMapGridDataDirtyArgs.EventId,OnWorldMapGridDataDirty);
            
            m_LogicCameraCtrl.UnInit();
            
            m_GroundDrawer.UnInit();
            m_BuildableGroundDrawer.UnInit();
            m_UnBuildableGroundDrawer.UnInit();
            m_TreeDrawer.UnInit();
            m_AreaBorderDrawer.UnInit();
            m_PreviewDrawer.UnInit();
            m_CityDrawer.UnInit();
            m_MonsterDrawer.UnInit();
            m_FortsDrawer.UnInit();
            m_MineDrawer.UnInit();
            m_TownDrawer.UnInit();
            m_MyTownDrawer.UnInit();
            m_TroopDrawer.UnInit();
            
            if (m_MapOpCtrl != null)
            {
                m_MapOpCtrl.Destroy();
                m_MapOpCtrl = null;
            }
            base.OnDestroy();
            GameEntry.WorldMap = null;
        }
        
        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            m_MapOpCtrl?.Update();

            if (hasDirtyMove || hasDirtyZoom)
            { 
                m_InShowCacheGrids.Clear();//移动之前清空缓存的 屏幕视野
                
                if(hasDirtyMove)
                {
                    hasDirtyMove = false;
                }

                if (hasDirtyZoom)
                {
                    hasDirtyZoom = false;
                    OnCameraZoom();
                }
                OnCameraMove();

                if(CameraComponent.ZoomLevel != WorldMapLOD.Level1)
                    TryHideGridClickEntity();
            }
        }

        protected override void OnLateUpdate()
        {
            base.OnLateUpdate();
            m_MapOpCtrl?.LateUpdate();
        }

        private void OnCameraZoom()
        {
            if (m_CurLODLevel != CameraComponent.ZoomLevel)
            {
                var oldLODLevel = m_CurLODLevel;
                m_CurLODLevel = CameraComponent.ZoomLevel;
                GameEntry.Event.Fire(this, OnWorldMapChangeLODArgs.Create(m_CurLODLevel));

                // m_AreaBorderDrawer.OnCameraZoom();
                // m_PreviewDrawer.OnCameraZoom();

                CameraLayer oldLayer = MapGridUtils.GetLayerByLod(oldLODLevel);
                CameraLayer newLayer = MapGridUtils.GetLayerByLod(m_CurLODLevel);
                if (oldLayer == CameraLayer._1 && newLayer == CameraLayer._2)
                {
                    //清除图层
                    // GameEntry.LogicData.WorldMapData.ClearAllData();
                }else if (oldLayer == CameraLayer._2 && newLayer == CameraLayer._1)
                {
                    //清除除主城以外数据
                    // GameEntry.LogicData.WorldMapData.ClearAllDataExceptTown();
                }
                
                OnCameraZoomCall?.Invoke();
                //尝试弹出UI
                if (m_CurLODLevel<=WorldMapLOD.Level1)
                {
                    //隐藏UI
                    if (m_PreviewUISerialId!=null && GameEntry.UI.HasUIForm(m_PreviewUISerialId.Value))
                    {
                        GameEntry.UI.CloseUIForm(m_PreviewUISerialId.Value);
                    }
                    m_PreviewUISerialId = 0;
                }
                else
                {
                    //显示UI
                    TryShowUIWorldMapPreview();
                }
                
            }
        }

        /// <summary>
        /// 尝试显示
        /// </summary>
        private void TryShowUIWorldMapPreview()
        {
            if (GetMapOpType() == MapOpType.DEFAULT_MODE && m_CurLODLevel >= WorldMapLOD.Level1 && !GameEntry.UI.HasUIForm(EnumUIForm.UIWorldMapPreview))
            {
                m_PreviewUISerialId = GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapPreview);
            }
        }
        
        private void OnCameraMove()
        {
            if (m_CacheCamera == null)
            {
                m_CacheCamera = GameEntry.Camera.WorldMapCamera;
            }

            if (m_CacheCamera == null) return;
            
            int validIntersections = 0;
            
            for (int i = 0; i < 4; i++)
            {
                Ray ray = m_CacheCamera.ViewportPointToRay(m_ViewportCorners[i]);
                float enterDistance;

                // 射线与平面求交
                if (m_ProjectionPlane.Raycast(ray, out enterDistance))
                {
                    // 仅当交点在射线正方向时才有效（Raycast 默认处理）
                    // 并且交点在摄像机前方（enterDistance > 0）
                    if (enterDistance > 0)
                    {
                        m_IntersectionPoints[i] = ray.GetPoint(enterDistance);
                        m_IntersectionGrids[i] = MapGridUtils.GetGrid(m_IntersectionPoints[i]);
                        m_HasIntersection[i] = true;
                        validIntersections++;
                    }
                    else
                    {
                        m_HasIntersection[i] = false; // 交点在射线反方向或摄像机在平面上/后方且看向平面
                    }
                }
                else
                {
                    m_HasIntersection[i] = false; // 射线平行于平面或背离平面
                }
            }

            if (validIntersections >= 4)
            {
                OnCameraMoveCall?.Invoke();
            }
        }

        public void GetInShowLIstByGrid(Vector2Int size, int border, WorldMapLOD min, WorldMapLOD max,
            out List<Vector2Int> list)
        {
            GetInShowLIstByGrid(size.x, size.y, border, min, max, out list);
        }
        
        
        public void GetInShowLIstByGrid(int size, int border, WorldMapLOD min, WorldMapLOD max,
            out List<Vector2Int> list)
        {
            GetInShowLIstByGrid(size, size, border, min, max, out list);
        }

        public void GetInShowLIstByGrid(int sizeX, int sizeY, int border, WorldMapLOD min, WorldMapLOD max,
            out List<Vector2Int> list)
        {
            if (m_CurLODLevel < min || m_CurLODLevel > max)
            {
                list = new List<Vector2Int>();//TODO 性能
                return;
            }

            Vector3Int key = new Vector3Int(sizeX, sizeY, border);

            if (!m_InShowCacheGrids.ContainsKey(key))
            {
                var points = m_IntersectionPoints;
                var p0 = MapGridUtils.GetGrid(points[0], (ushort)sizeX, (ushort)sizeY);
                var p1 = MapGridUtils.GetGrid(points[1], (ushort)sizeX, (ushort)sizeY);
                var p2 = MapGridUtils.GetGrid(points[2], (ushort)sizeX, (ushort)sizeY);
                var p3 = MapGridUtils.GetGrid(points[3], (ushort)sizeX, (ushort)sizeY);

                int minX = p2.x;
                int maxX = p3.x;
                int minY = p0.y;
                int maxY = p2.y;

                List<Vector2Int> showListNew = new List<Vector2Int>();

                for (int x = minX - border; x <= maxX + border; x++)
                {
                    for (int y = minY - border; y <= maxY + border; y++)
                    {
                        showListNew.Add(new Vector2Int(x, y));
                    }
                }

                m_InShowCacheGrids.Add(key, showListNew);
            }

            m_InShowCacheGrids.TryGetValue(key, out list);
        }

        public void TryHideGridClickEntity()
        {
            if (m_GridClickEntityId!=null)
            {
                var entity = GameEntry.Entity.GetEntity(m_GridClickEntityId.Value);
                if (entity != null)
                {
                    GameEntry.Entity.HideEntity(m_GridClickEntityId.Value);
                }
                m_GridClickEntityId = null;
            }
        }

        private void TryShowGridClickEntity(OnWorldMapTapEventArgs args)
        {
            if (args.EntityLogic is null or { Count: <= 0 } && CameraComponent.ZoomLevel == WorldMapLOD.Level1)
            {
                var gridPos = MapGridUtils.GetGrid(args.WorldPos);
                if (gridPos.x < 0 || gridPos.x > 999 || gridPos.y < 0 || gridPos.y > 999)
                    return;

                ED_WorldMapGridClick param =
                    new ED_WorldMapGridClick(Game.GameEntry.Entity.GenerateSerialId(), gridPos);
                param.Position = new Vector3(gridPos.x, 0.2f, gridPos.y);

                m_GridClickEntityId = GameEntry.Entity.ShowWorldMapDisplay(GameDefine.WorldMapClickGridRes, param,
                    typeof(EL_WorldMapGridClick));
            }
        }

        /// <summary>
        /// 获取当前摄像头 视野内的 grid 尺寸
        /// </summary>
        /// <returns></returns>
        public Vector2Int GetCameraLookGridBorderSize()
        {
            return new Vector2Int(
                Mathf.RoundToInt((m_IntersectionGrids[3].x - m_IntersectionGrids[2].x) / 2),
                Mathf.RoundToInt((m_IntersectionGrids[2].y - m_IntersectionGrids[0].y) / 2)
            );
        }
        
        #region 迁城模式
        public bool TryEnterMoveTownMode(Vector2Int targetGridPos)
        {
            return m_MapOpCtrl.TryEnterMoveTownMode(targetGridPos);
        }
        
        public void MoveTownConfirmed()
        {
            m_MapOpCtrl.MoveTownConfirmed();
        }

        public void MoveTownCanceled()
        {
            m_MapOpCtrl.MoveTownCanceled();
        }

        public MapOpType? GetMapOpType()
        {
            return m_MapOpCtrl.CurOpMode;
        }
        
        public void GetPreviewPos(out Vector2Int pos)
        {
            m_MapOpCtrl.GetPreviewPos(out pos);
        }

        public int? GetPreviewUid()
        {
            return m_MapOpCtrl.GetPreviewUid();
        }
        
        public bool CanPutTownHere(Vector2Int pos)
        {
            return m_UnBuildableGroundDrawer.CanPut(pos);
        }
        #endregion

        #region 部队相关

        public void GetInShowTroopList(WorldMapLOD min, WorldMapLOD max, out List<ulong> list)
        {
            // if (m_CurLODLevel < min || m_CurLODLevel > max)
            // {
            //     list = new List<ulong>();//TODO 性能
            //     return;
            // }
            GameEntry.LogicData.WorldMapData.GetInShowTroopList(out list);
        }

        #endregion 
        
    }
}
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace Game.Hotfix
{
    public class WmpTownDrawer : WmpDrawerBase<WmpTownUiHud>
    {
        private Dictionary<Vector2Int, List<WmpTownUiHud>> m_ShowListDic = new Dictionary<Vector2Int, List<WmpTownUiHud>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();
        
        private WorldMapComponent m_WorldMapComponent;
        
        private ulong? m_PlayerId = null;
        
        public void Init(WorldMapComponent worldMapComponent)
        {
            m_PlayerId = GameEntry.LogicData.UserData.uuid;
            
            m_WorldMapComponent = worldMapComponent;
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty += OnGridDataDirty;
        }

        public void UnInit()
        {
            RemoveAll();            
            
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty -= OnGridDataDirty;
            m_WorldMapComponent = null;
        }

        private void OnGridDataDirty(HashSet<Vector2Int> dirtyData)
        {
            foreach (var t in dirtyData)
            {
                Remove(t);
            }
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                WorldMapLOD.Level4, out List<Vector2Int> showListNew);
            
            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
            
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    var list = gridData.TownData;
                    if (list != null)
                    {
                        var hudList = new List<WmpTownUiHud>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            GridTownData data = list[i];
                            if (data.RoleId == m_PlayerId)
                                continue;
                            var item = SpawnItem();
                            item.SetData(data);
                            hudList.Add(item);
                        }
                    
                        m_ShowListDic.Add(grid, hudList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var hudList))
                {
                    for (int i = 0; i < hudList.Count; i++)
                    {
                        WmpTownUiHud hud = hudList[i];
                        DespawnItem(hud);
                    }
            
                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private void RemoveAll()
        {
            m_ShowList.Clear();
            foreach (var item in m_ShowListDic)
            {
                foreach (WmpTownUiHud uiHud in item.Value)
                {
                    DespawnItem(uiHud);
                }
            }

            m_ShowListDic.Clear();

        }
        
    }
}
using System;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class WmpMyTownUiHud : WmpUiHudBase
    {
        [SerializeField] public GameObject goA;

        [SerializeField] public GameObject goLevelItem;

        [SerializeField] public UIText txtLevel;


        private Camera m_MainCamera;
        private RectTransform m_SelfRect;
        private RectTransform m_ParentRect;
        private Vector2 m_TempV2;
        private Vector2Int m_TempV2Int;

        private Vector2Int m_Pos;
        private ulong m_PlayerId;

        private WorldMapComponent m_WorldMap;
        
        private bool m_IsMoveMode = false;
        
        public void SetData(GridTownData data)
        {
            m_PlayerId = data.RoleId;
            m_Pos = new Vector2Int((int)data.PosX, (int)data.PosY);

            txtLevel.text = data.Level.ToString();

            ResetDisplay();
        }

        public override void Depool()
        {
            if (m_MainCamera == null)
                m_MainCamera = GameEntry.Camera.WorldMapCamera;
            if (m_ParentRect == null)
                m_ParentRect = transform.parent.GetComponent<RectTransform>();
            if (m_SelfRect == null)
                m_SelfRect = transform.GetComponent<RectTransform>();

            GameEntry.Event?.Subscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
            GameEntry.Event?.Subscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);

            m_WorldMap = GameEntry.WorldMap;
            m_IsMoveMode = m_WorldMap.GetMapOpType() == MapOpType.TOWN_MOVE_MODE;
        }

        private void OnOpModeChangeEvent(object sender, GameEventArgs e)
        {
            if (e is OnOpModeChangeEventArgs args)
            {
                if (args.newOp == MapOpType.TOWN_MOVE_MODE)
                {
                    m_IsMoveMode = true;
                }
                else
                {
                    m_IsMoveMode = false;
                }
            }
        }

        private void OnWorldMapChangeLOD(object sender, GameEventArgs e)
        {
            ResetDisplay();
        }

        private void ResetDisplay()
        {
            var curLevel = CameraComponent.ZoomLevel;
            goA.SetActive(curLevel >= WorldMapLOD.Level2);
            
            goLevelItem.SetActive(curLevel <= WorldMapLOD.Level3);
        }

        public override void Repool()
        {
            GameEntry.Event?.Unsubscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
            GameEntry.Event?.Unsubscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);
        }

        private void LateUpdate()
        {
            if (m_IsMoveMode)
            {
                m_WorldMap?.GetPreviewPos(out m_TempV2Int);
                ResetPos(m_TempV2Int.x, m_TempV2Int.y);
            }
            else
            {
                ResetPos(m_Pos.x,m_Pos.y);    
            }
        }

        private void ResetPos(float x,float y)
        {
            var screenPoint =
                RectTransformUtility.WorldToScreenPoint(m_MainCamera, new Vector3(x, 0, y) + GameDefine.HalfOffset);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRect, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            m_SelfRect.anchoredPosition = m_TempV2;
        }
    }
}

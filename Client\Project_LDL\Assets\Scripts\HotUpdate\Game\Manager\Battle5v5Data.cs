using System;
using System.Collections.Generic;
using Battle;
using Common;
using Dungeon;
using Fight;
using GameFramework.Event;
using Google.Protobuf;
using Google.Protobuf.Collections;
using PbGameconfig;
using Roledata;
using UnityEngine;
using Action = System.Action;

namespace Game.Hotfix
{
    public enum EnumBattle5v5Type
    {
        None,
        Debug,//调试
        Dungeon,//关卡
        TradeTruck,//货车抢夺战
        NoviceTraining,//新兵挑战
        PeakRank,//巅峰竞技场
        Replay,//普通回放
    }
    
    public class Battle5v5Data
    {
        public EnumBattle5v5Type CurBattleType => m_CurBattleType;
        
        private EnumBattle5v5Type m_CurBattleType = EnumBattle5v5Type.None;

        private Battle5v5ParamBase m_ParamBase;
        private Battle5v5ParamBase m_ParamBaseNoticed;
        
        public void Init()
        {
            GameEntry.Event.Subscribe(ChangeSceneEventArgs.EventId,ChangeSceneEvent);
        }

        private void ChangeSceneEvent(object sender, GameEventArgs e)
        {
            if (e is ChangeSceneEventArgs args)
            {
                if (args.SceneId == (int)SceneDefine.MainScene)
                {
                    if (m_ParamBase != null && m_ParamBase.IsFinish() && m_ParamBaseNoticed != m_ParamBase)
                    {
                        m_ParamBaseNoticed = m_ParamBase;
                        GameEntry.Event.Fire(On5V5BattleBackEventArgs.EventId,
                            On5V5BattleBackEventArgs.Create(m_ParamBase));
                    }
                }
            }
        }

        public T GetBattle5V5Param<T>() where T : Battle5v5ParamBase
        {
            if (m_ParamBase != null && m_ParamBase is T param)
            {
                return param;
            }

            return null;
        }

        private void SetBattle5V5Record(Battle.Report report)
        {
            if (m_CurBattleType == EnumBattle5v5Type.Dungeon && m_ParamBase is Battle5v5ParamDungeon param)
            {
                param.SetRecord(report);
            }
        }
        
        /// <summary>
        /// 进入debug战斗
        /// </summary>
        public void GoBattleDebug()
        {
            m_CurBattleType = EnumBattle5v5Type.Debug;
            m_ParamBase = new Battle5v5ParamDebug();
            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureMain procedure)
            {
                procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
            }
        }

        /// <summary>
        /// 进入关卡
        /// </summary>
        /// <param name="config">关卡配置</param>
        public void GoBattleDungeon(Config.dungeon config,PvePathModule pvePathModule)
        {
            if (config!=null)
            {
                m_CurBattleType = EnumBattle5v5Type.Dungeon;
                m_ParamBase = new Battle5v5ParamDungeon(config,pvePathModule);
                
                var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
                if (tCurrentProcedure is ProcedureMain procedure)
                {
                    procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
                }
            }
        }
        
        public void GoBattleTradeTruck(List<TeamHero> defHeros, long truckId = 0)
        {
            m_CurBattleType = EnumBattle5v5Type.TradeTruck;
            m_ParamBase = new Battle5v5ParamTradeTruck(defHeros, truckId);

            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureWorldMap procedure)
            {
                procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
            }
        }

        public void GoBattleNoviceTraining(List<TeamHero> defHeros,uint roleId,uint rank)
        {
            m_CurBattleType = EnumBattle5v5Type.NoviceTraining;
            m_ParamBase = new Battle5v5ParamNoviceTraining(defHeros,roleId,rank);

            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureMain procedure)
            {
                procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
            }
        }

        public void GoBattlePeakRank(List<TeamHero> defHeros,ulong roleId,uint rank)
        {
            m_CurBattleType = EnumBattle5v5Type.PeakRank;
            m_ParamBase = new Battle5v5ParamPeakRank(defHeros,roleId,rank);

            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureMain procedure)
            {
                procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
            }
        }

        public void GoBattleReplay(string reportId)
        {
            DoBattleRecordReadReq(reportId, (resp) =>
            {
                Report report = Report.Descriptor.Parser.ParseFrom(resp.Report) as Report;
                m_CurBattleType = EnumBattle5v5Type.Replay;
                m_ParamBase = new Battle5v5ParamReplay(report);
                var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
                if (tCurrentProcedure is ProcedureMain procedure)
                {
                    procedure.GotoBattle((int)SceneDefine.Battle5v5Scene);
                }
            });
        }

        public void GoBackMainCity()
        {
            m_CurBattleType = EnumBattle5v5Type.None;
            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is Procedure5v5Battle procedure)
            {
                procedure.GotoMainCity();
            }
        }
        
        public void GoBackWorldMap()
        {
            m_CurBattleType = EnumBattle5v5Type.None;
            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is Procedure5v5Battle procedure)
            {
                procedure.GoToWorldMap();
            }
        }

        public void BattleDebugBeginReq(DebugTeam attacker, DebugTeam defender, int level, int star, int hpTimes,bool ignoreDefender, Action<BattleDebugResp> callBack = null)
        {
            BattleDebugReq battleDebugReq = new BattleDebugReq();
            battleDebugReq.Attacker = attacker;
            battleDebugReq.Defender = defender;
            battleDebugReq.Level = level;
            battleDebugReq.StarStage = star;
            battleDebugReq.HpTimes = hpTimes;

            if (ignoreDefender)
            {
                battleDebugReq.Args = new BattleArgs();
                battleDebugReq.Args.IsIgnoreDefender = true;    
            }

            GameEntry.LDLNet.Send(Protocol.MessageID.BattleDebug, battleDebugReq, (message) =>
            {
                BattleDebugResp resp = (BattleDebugResp)message;
                if (resp != null)
                {
                    callBack?.Invoke(resp);
                }
            });

            //测试数据
            // BattleDebugResp resp = new BattleDebugResp();
            // resp.ReportId = "1";
            // callBack?.Invoke(resp);
        }

        public void DoBattleRecordReadReq(string reportId,Action<BattleRecordReadResp> callBack = null)
        {
            BattleRecordReadReq battleRecordReadReq = new BattleRecordReadReq();
            battleRecordReadReq.ReportId = reportId;
            GameEntry.LDLNet.Send(Protocol.MessageID.BattleRecordRead, battleRecordReadReq, (message) =>
            {
                BattleRecordReadResp resp = (BattleRecordReadResp)message;
                if (resp != null && resp.Report.Length != 0)
                {
                    Report report = Report.Descriptor.Parser.ParseFrom(resp.Report) as Report;
                    SetBattle5V5Record(report);
                    
                    callBack?.Invoke(resp);
                }
            });
            
            //测试数据
            // BattleRecordReadResp resp = new BattleRecordReadResp();
            // resp.Report = CreateTestReport().ToByteString();
            // callBack?.Invoke(resp);
            //
            // Report message = Report.Descriptor.Parser.ParseFrom(resp.Report) as Report;
            // SetBattle5V5Record(message);
        }

        /// <summary>
        /// 打关卡
        /// </summary>
        /// <param name="id"></param>
        /// <param name="callback"></param>
        public void DungeonFightBeginReq(int id,Action<DungeonFightResp> callback = null)
        {
            DungeonFightReq req = new DungeonFightReq();
            req.Id = id;
            GameEntry.LDLNet.Send(Protocol.MessageID.DungeonFight,req, (message) =>
            {
                DungeonFightResp resp = (DungeonFightResp)message;
                if (resp!=null && resp.ReportId.Length != 0)
                {
                    callback?.Invoke(resp);
                }
            });
        }

        public void DungeonFightSettle(int id,Action<DungeonSettleResp> callback = null)
        {
            DungeonSettleReq req = new DungeonSettleReq();
            req.Id = id;
            GameEntry.LDLNet.Send(Protocol.MessageID.DungeonSettle,req, (message) =>
            {
                DungeonSettleResp resp = (DungeonSettleResp)message;
                if (resp!=null && resp.Id != 0)
                {
                    GameEntry.LogicData.DungeonData.SetDungeonId(resp.Id, resp.AccumulatedRewardAt);
                    callback?.Invoke(resp);
                }
            });
        }
        
        #region 测试数据
        
        private Report CreateTestReport()
        {
            Report report = new Report();
            report.ReportId = "1";
            report.Attacker = CreateTestTeam(EnumBattleSide.Left);
            report.Defender = CreateTestTeam(EnumBattleSide.Right);
            report.BattleType = battle_types._1;
            report.Result = BattleResult.AttackerWin;

            int totalRound = 1;
            int interval = 24*10;
            for (int i = 0; i < totalRound; i++)
            {
                report.Actions.Add(CreateTestAction(EnumBattleSide.Left,interval*i,ActionType.Skill,ChangeType.ChangeNil,11301100));//释放技能
                report.Actions.Add(CreateTestAction(EnumBattleSide.Right,interval*i,ActionType.Attr,ChangeType.AttrHp,-10));//扣血
                
                // report.Actions.Add(CreateTestAction(EnumBattleSide.Right,24*(i+2),ActionType.Skill,ChangeType.ChangeNil,11301100));//释放技能
                // report.Actions.Add(CreateTestAction(EnumBattleSide.Left,24*(i+2),ActionType.Attr,ChangeType.AttrHp,-10));//扣血
            }
            report.Actions.Add(CreateTestAction(EnumBattleSide.Right,interval*(totalRound+0),ActionType.Special,ChangeType.Die,0));//死亡
            report.Actions.Add(CreateTestAction(EnumBattleSide.Right,interval*(totalRound+1),ActionType.Result,ChangeType.ChangeNil,1));//结算

            return report;
        }

        private Battle.Team CreateTestTeam(EnumBattleSide side)
        {
            Battle.Team team = new Battle.Team();
            team.Role = CreateTestRole();
            team.Heroes.Add(CreateTestHero(11301, side));
            team.IsMonster = side == EnumBattleSide.Right;
            return team;
        }

        private TeamRole CreateTestRole()
        {
            var role = new TeamRole();
            role.ServerId = 1;
            role.ServerName = "";
            role.Id = 1;
            role.Name = "role1";
            role.Level = 50;
            role.Power = 90000;
            return role;
        }

        private TeamHero CreateTestHero(int heroId,EnumBattleSide side)
        {
            TeamHero hero = new TeamHero();
            hero.Code = (itemid)heroId;
            hero.Pos = side == EnumBattleSide.Left ? (int)EnumBattlePos.PosL1 : (int)EnumBattlePos.PosR1;
            hero.Level = 3;
            hero.StarStage = 1;
            hero.Power = 9999;
            hero.Skills.Add(CreateTestSkill());
            // hero.Attrs.Add(CreateTestAttr(attributes_type.Hp,1000));
            return hero;
        }

        private TeamHeroSkill CreateTestSkill()
        {
            TeamHeroSkill skill = new TeamHeroSkill();
            skill.Id = 11301100;
            skill.Level = 1;
            return skill;
        }

        // private Attr CreateTestAttr(attributes_type type,long value)
        // {
        //     Attr attr = new Attr();
        //     attr.Type = type;
        //     attr.Value = value;
        //     return attr;
        // }

        private Battle.Action CreateTestAction(EnumBattleSide side,int frame,ActionType actionType,ChangeType changeType,long value)
        {
            Battle.Action action = new Battle.Action();
            action.Frame = frame;
            action.Caster = side == EnumBattleSide.Left ? (int)EnumBattlePos.PosL1 : (int)EnumBattlePos.PosR1;
            action.Target.Add(side == EnumBattleSide.Left ? (int)EnumBattlePos.PosR1 : (int)EnumBattlePos.PosL1);
            action.Action_ = actionType;
            action.Change = changeType;
            action.Value = value;
            return action;
        }
        #endregion
        
        
    }
}
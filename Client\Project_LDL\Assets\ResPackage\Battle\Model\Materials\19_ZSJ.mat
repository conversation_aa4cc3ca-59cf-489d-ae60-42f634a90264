%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 19_ZSJ
  m_Shader: {fileID: 4800000, guid: 767e660b49a03bc4484fe0b9ebda1d02, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RIMLIGHTING_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: fc307ebab67749545aea18a5d1d640ec, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: 9cc8191b9f260154fb01b7385e2efa2d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 3805b5bf87d67ac448c9253802cf974b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalRGMap:
        m_Texture: {fileID: 2800000, guid: a22bd01031570d743961031ebadddbeb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecCube0:
        m_Texture: {fileID: 8900000, guid: 549f680d252199a4a953ad57810f66d1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _CubeIntensity: 0.5
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DistanceAttenuation: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.14142135
    - _GlossyReflections: 1
    - _Metallic: 1
    - _Mode: 0
    - _OcclusionStrength: 0.488
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimDir: 0
    - _RimDown: 0
    - _RimIntensity: 0.66
    - _RimLeft: 0
    - _RimLighting: 1
    - _RimLightingCam: 0
    - _RimOuterThickness: 0.042
    - _RimPower: 10.6
    - _RimRight: 0
    - _RimUp: 0
    - _Smoothness: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _UVSec: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _AuxiliaryLightColor: {r: 0.7490196, g: 0.38984835, b: 0.010599316, a: 1}
    - _AuxiliaryLightDirection: {r: 0.31, g: -0.49, b: -0.26, a: 0}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 0.79073066, g: 0.79073066, b: 0.79073066, a: 1}
    - _CustomLightColor: {r: 1.7207953, g: 1.7207953, b: 1.7207953, a: 1}
    - _CustomLightDirection: {r: -0.6791203, g: 0.496974, b: -0.5401967, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimInnerColor: {r: 1, g: 0.6259299, b: 0.10849059, a: 1}
    - _RimOuterColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimView: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1

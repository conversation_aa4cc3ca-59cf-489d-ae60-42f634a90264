using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Resource;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    enum UAVViewType
    {
        Attributes,     //属性
        Compontent,     //组件
        CombatBoost,    //战斗进阶
        SkillChip,      //技能晶片
    }
    public partial class UIBuildingUAVForm : UGuiFormEx
    {
        private UAVModule m_CurUavModule;
        private List<long> uavAttrList;
        private uav_level uavLevelConfig;
        private uav_level uavNextLevelConfig;
        private GameObject uavObj;
        private List<uav_skin> uavSkinConfigs;
        private List<Transform> uavSkinObjList = new List<Transform>();
        private int selectIndex = -1;
        private PageView m_PageView;
        private UIDragXYDir m_SkinDrag;
        private UAVViewType m_CurSelectViewType;
        private List<Transform> uavSkinAttrObjList = new List<Transform>();
        private RectTransform uavSkinAttrConent;
        private RectTransform uavComponentAttrConent;
        private Dictionary<uint, uint> m_UavComponents;        
        private List<Transform> uavComponentAttrObjList = new List<Transform>();

        
        private float startDragHorizontal;
        float startPosition = 0;
        private float rotateX;
        private float rotateSpeed = 0.5f;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            m_PageView = m_scrollviewskinList.transform.GetComponent<PageView>();
            m_SkinDrag = m_transSkinDrag.GetComponent<UIDragXYDir>();
            InitToggle();
            uavSkinAttrConent = m_goSkinAttr.transform.GetComponent<RectTransform>();
            uavComponentAttrConent = m_scrollviewComponentAttrList.content;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_CurUavModule = GameEntry.LogicData.UAVData.UavModule;
            if (m_CurUavModule == null)
            {
                return;
            }

            if (userData != null)
            {
                m_CurSelectViewType = (UAVViewType)userData;
            }
            else
            {
                m_CurSelectViewType = UAVViewType.Attributes;
            }
            
            selectIndex = -1;
            uavSkinConfigs = GameEntry.LDLTable.GetTable<uav_skin>();
            uavLevelConfig = GameEntry.LogicData.UAVData.GetUavLevelConfig((int)m_CurUavModule.Id);
            uavNextLevelConfig = GameEntry.LogicData.UAVData.GetUavLevelConfig((int)m_CurUavModule.Id + 1);
            CheckShowSkinIndex();
            ResetUI();
            InitPageView();
            m_goUAVSkinInfo.gameObject.SetActive(false);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_CurUavModule = null;
            selectIndex = -1;
            //m_goAttrGroup.gameObject.DestroyAllChild();
            Destroy(uavObj);
            uavObj = null;
            m_scrollviewskinList.content.gameObject.DestroyAllChild();
            uavSkinObjList.Clear();
            uavSkinAttrObjList.Clear();
            startPosition = 0;
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            
            UIBuildingUAVFormParams refreshParams = userData as UIBuildingUAVFormParams;
            if (refreshParams == null)
            {
                return;
            }

            if (refreshParams.m_RefreshType == UIUavFormRefreshType.UpgradeRefresh)
            {
                ResetAttributesBtn();
                ResetAttributesInfo();
                ResetExpInfo();
                ResetSkillInfo();
                //ResetSkinInfo();
            }
            else if (refreshParams.m_RefreshType == UIUavFormRefreshType.OneClickEquipComponentRefresh)
            {
                ResetComponentUI();
            }
        }
        
        void InitToggle()
        {
            m_togAttributes.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_CurSelectViewType = UAVViewType.Attributes;
                    SwitchViewByType();
                }
            });
            m_togComponent.onValueChanged.AddListener((isOn)=>
            {
                if (isOn)
                {
                    m_CurSelectViewType = UAVViewType.Compontent;
                    SwitchViewByType();
                }
            });
        }
        
        private void SwitchViewByType()
        {
            m_goAttrbutesGroup.gameObject.SetActive(m_CurSelectViewType == UAVViewType.Attributes);
            m_goCompontPart.gameObject.SetActive(m_CurSelectViewType == UAVViewType.Compontent);

            if (m_CurSelectViewType == UAVViewType.Attributes)
            {
                ResetAttributes();
            }
            else if (m_CurSelectViewType == UAVViewType.Compontent)
            {
                ResetComponentUI();
            }
        }

        private void ResetUI()
        {
            if (m_CurSelectViewType == UAVViewType.Attributes)
            {
                if (m_togAttributes.isOn)
                {
                    ResetAttributes();
                }
                else
                {
                    m_togAttributes.isOn = true;
                }
                
            }
            else if (m_CurSelectViewType == UAVViewType.Compontent)
            {
                m_togComponent.isOn = true;
            }
            
        }

        private void ResetAttributes()
        {
            ResetUAVDisplay();
            ResetAttributesBtn();
            ResetAttributesInfo();
            ResetExpInfo();
            ResetSkillInfo();
        }

        private void ResetAttributesBtn()
        {
            m_transDrag.gameObject.SetActive(false);
            m_imgToggleBg.gameObject.SetActive(true);
            m_btnExit.gameObject.SetActive(true);
            m_goUAVInfo.gameObject.SetActive(true);
            m_btnUpgradePreview.gameObject.SetActive(true);
            m_btnSkin.gameObject.SetActive(true);
            m_btnBack.gameObject.SetActive(false);
            m_goUAVSkinInfo.gameObject.SetActive(false);
        }

        private void ResetUAVDisplay()
        {
            string uavDisplayAssetPath = m_CurUavModule.GetUAVDisplayAssetPath();
            if (uavObj != null)
                Destroy(uavObj);

            GameEntry.Resource.LoadAsset(uavDisplayAssetPath, new LoadAssetCallbacks((assetName, asset, duration, userData) =>
            {
                var prefab = asset as GameObject;
                uavObj = Instantiate(prefab, m_transUavDisplay);
                uavObj.transform.SetLayer(6);
                OnResetUavObj();
            }));
        }
        
        private void OnResetUavObj()
        {
            if (uavObj == null) return;

            uavObj.transform.SetLocalPosition(-9, -189, 0);

            uavObj.transform.SetLocalScale(80, 80, 80);

            uavObj.transform.eulerAngles = new Vector3(-13, -50.5f, 24);
        }

        private void ResetSkillInfo()
        {
            uav_skill skillConfig = GameEntry.LDLTable.GetTableById<uav_skill>(m_CurUavModule.SkillStar + 1);
            if (skillConfig.picture != string.Empty)
            {
                m_imgSkillIcon.SetImage(skillConfig.picture);
                m_imgTipSkillIcon.SetImage(skillConfig.picture);

                m_txtSkillName.text = ToolScriptExtend.GetLang(skillConfig.skill_name);
                float skillStr = (float)uavLevelConfig.skill_coefficient / 100;
                m_txtSkillDetail.text = ToolScriptExtend.GetLangFormat(1100390,skillStr.ToString("0.00"));
            }

            RefreshStarInfo();
            ResetSkillStarInfo();
        }
        
        protected void RefreshStarInfo()
        {
            int starNum = m_CurUavModule.SkillStar;
            var count = m_goSkillStar.transform.childCount;
            for (int i = 0; i < count; i++)
            {
                var starSp = m_goSkillStar.transform.GetChild(i).GetComponent<UIImage>();
                starSp.gameObject.SetActive(i < starNum);
                string pathStr;                
                if (i < starNum)
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star1.png";
                }
                else
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star2.png";
                }
                var tipStar = m_goTipSkillStar.transform.GetChild(i).GetComponent<UIImage>();
                tipStar.SetImage(pathStr);
            }
        }

        private void ResetSkillStarInfo()
        {
            int starNum = m_CurUavModule.SkillStar;
            var count = m_goStarDetail.transform.childCount;
            int starIndex = 2;
            for (int i = 0; i < count; i++)
            {
                var starSp = m_goStarDetail.transform.GetChild(i).GetComponentInChildren<UIImage>();
                var txtSkillDesc = m_goStarDetail.transform.GetChild(i).GetComponent<UIText>();
                string pathStr;                
                string skillStarStr = String.Empty;
                uav_skill skillConfig = GameEntry.LDLTable.GetTableById<uav_skill>(starIndex);
                string skillStarDetail = string.Empty;
                if (skillConfig.star_desc != 0)
                {
                    skillStarDetail = ToolScriptExtend.GetLang(skillConfig.star_desc);
                }
                string skillDesc = String.Empty;
                if (i < starNum)
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star1.png";
                    skillDesc = $"<color='#61f686'>{skillStarDetail}</color>";
                }
                else
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star2.png";
                    string unlock = ToolScriptExtend.GetLangFormat(1236, skillConfig.star_demand.ToString());
                    skillDesc = $"{skillStarDetail}<color='#f66f76'>{unlock}</color>";
                }
                starSp.SetImage(pathStr);
                txtSkillDesc.text = skillDesc;
                starIndex += 1;
            }
        }

        private void ResetAttributesInfo()
        {
            uavAttrList = GetUavAttrConfigList();
            m_txtPower.text = uavLevelConfig.power.ToString();
            for (int i = 0; i < uavAttrList.Count; i++)
            {
                GameObject itemObj;
                if (m_goAttrGroup.transform.childCount <= i)
                {
                    itemObj = Instantiate(m_goAttrItem,m_goAttrGroup.transform);
                    itemObj.SetActive(true);
                }
                else
                {
                    itemObj = m_goAttrGroup.transform.GetChild(i).gameObject;
                }

                UpdateAttrList(i, itemObj);
            }
        }

        private void UpdateAttrList(int index, GameObject obj)
        {
            Transform itemTransform    = obj.transform;
            UIImage attrIcon           = itemTransform.Find("btnAttr/attrIcon").GetComponent<UIImage>();
            UIImage jiantou            = itemTransform.Find("btnAttr/jiantou").GetComponent<UIImage>();
            UIText txtCurValue         = itemTransform.Find("btnAttr/txtCurValue").GetComponent<UIText>();
            UIText txtNextValue        = itemTransform.Find("btnAttr/txtNextValue").GetComponent<UIText>();
            UIImage attrIcon2          = itemTransform.Find("btnAddition/attrIcon2").GetComponent<UIImage>();
            UIText txtCurPreValue      = itemTransform.Find("btnAddition/txtCurPreValue").GetComponent<UIText>();
            UIButton btnAttr           = itemTransform.Find("btnAttr").GetComponent<UIButton>();
            UIButton btnAddition       = itemTransform.Find("btnAddition").GetComponent<UIButton>();
            
            string iconPath = index switch
            {
                0 => "Sprite/ui_jianzhu_wurenji/wurenji_jineng1.png",
                1 => "Sprite/ui_jianzhu_wurenji/wurenji_jineng2.png",
                _ => "Sprite/ui_jianzhu_wurenji/wurenji_jineng3.png",
            };

            attrIcon.SetImage(iconPath);
            attrIcon2.SetImage(iconPath);
            long uavAttr = uavAttrList[index];
            txtCurValue.text = $"+{uavAttr}";

            bool isMax = uavNextLevelConfig == null;
            jiantou.gameObject.SetActive(!isMax);
            txtNextValue.gameObject.SetActive(!isMax);
            if (!isMax)
            {
                long nextValue = index switch
                {
                    0 => uavNextLevelConfig.life,
                    1 => uavNextLevelConfig.attack,
                    _ => uavNextLevelConfig.defense,
                };
                txtNextValue.text = $"+{nextValue}";
            }

            int additionValue = index switch
            {
                0 => uavLevelConfig.proportion_life,
                1 => uavLevelConfig.proportion_attack,
                _ => uavLevelConfig.proportion_defense,
            };
            txtCurPreValue.text = $"+{((float)additionValue / 100):0.0}%";
            
            btnAttr.onClick.RemoveAllListeners();
            btnAttr.onClick.AddListener(() =>
            {
                if (m_goSkillTip.activeSelf)
                {
                    m_goSkillTip.gameObject.SetActive(false);
                    return;
                }
                int langId = index switch
                {
                    0 => 1100395,
                    1 => 1100396,
                    _ => 1100397,
                };
                m_txtAttrTip.text = ToolScriptExtend.GetLang(langId);
                m_goTipAttr.transform.SetParent(btnAttr.transform);
                m_goTipAttr.transform.localPosition = new Vector3(0,20,0);
                m_goTipAttr.SetActive(true);
                PlayAttrTipsTween();
            });     
            
            btnAddition.onClick.RemoveAllListeners();
            btnAddition.onClick.AddListener(() =>
            {
                int langId = index switch
                {
                    0 => 1100398,
                    1 => 1100399,
                    _ => 1100400,
                };
                long addValue = Mathf.FloorToInt((float)additionValue / 10000 * uavAttr);
                m_txtAttrTip.text = ToolScriptExtend.GetLangFormat(langId,addValue.ToString());
                m_goTipAttr.transform.SetParent(btnAddition.transform);
                m_goTipAttr.transform.localPosition = new Vector3(0,20,0);
                m_goTipAttr.SetActive(true);
                PlayAttrTipsTween();
            });
        }

        private List<long> GetUavAttrConfigList()
        {
            List<long> list = new List<long>();
            
            if (uavLevelConfig != null)
            {
                list.Add(uavLevelConfig.life);
                list.Add(uavLevelConfig.attack);
                list.Add(uavLevelConfig.defense);
            }
            return list;
        }

        private void ResetExpInfo()
        {
            m_txtUAVLevel.text = $"Lv.{m_CurUavModule.Level}";
            bool isInUpgradeStage = m_CurUavModule.GetIsInUpgradeStage();
            bool isMax = uavNextLevelConfig == null;
            m_goExpGroup.SetActive(!isMax);
            m_goUpgradeRoot.SetActive(!isMax);
            m_btnUpgrade.gameObject.SetActive(!isMax);
            m_sliderUpgrade.gameObject.SetActive(!isInUpgradeStage);
            m_goUpgradeStageExp.gameObject.SetActive(isInUpgradeStage);
            m_goUpgradeStage.gameObject.SetActive(isInUpgradeStage);
            if (isMax)
            {
                return;
            }
            if (!isInUpgradeStage)
            {
                long consumeTotal = uavNextLevelConfig.consume_total;
                uint exp = m_CurUavModule.Exp;
                m_txtExp.text = $"{exp}/{consumeTotal}";
                m_sliderUpgrade.value = (float)exp / consumeTotal;
            }
            else
            {
                int costPart = uavNextLevelConfig.consume_part;
                long partCount = GameEntry.LogicData.BagData.GetAmountById(itemid.itemid_1010041);
                string stageColorStr = partCount >= costPart ? "58ff87" : "FF1717";
                m_txtUpgradeStageExp.text = $"<color=#{stageColorStr}>{partCount}</color>/{costPart}";
                m_txtExp.text = ToolScriptExtend.GetLangFormat(1173,m_CurUavModule.Stage.ToString());
                m_imgStageItem.SetImage(ToolScriptExtend.GetItemIcon(itemid.itemid_1010041));
                RefreshStageSlider();
            }

            long oneConsume = uavNextLevelConfig.consume;
            long amount = GameEntry.LogicData.BagData.GetAmountById(itemid.itemid_1010042);
            string colorStr = amount >= oneConsume ? "58ff87" : "FF1717";
            string amountStr = ToolScriptExtend.FormatNumberWithUnit(amount);
            string costStr = ToolScriptExtend.FormatNumberWithUnit(oneConsume);
            m_txtUpgradeExp.text = $"<color=#{colorStr}>{amountStr}</color>/{costStr}";
        }

        private void PlayAttrTipsTween()
        {
            RectTransform attrTipRect = m_goTipAttr.transform.GetChild(0).GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(attrTipRect);
            m_goTipAttr.transform.localScale = Vector3.zero;
            m_goTipAttr.transform.DOScale(1, 0.1f).SetEase(Ease.InBounce);
        }
        
        private void PlaySkillTipsTween()
        {
            m_goSkillTip.transform.localScale = Vector3.zero;
            m_goSkillTip.transform.DOScale(1, 0.1f).SetEase(Ease.InBounce).OnComplete(() =>
            {
                RectTransform skillDescRect = m_goStarDetail.transform.GetChild(0).GetComponent<RectTransform>();
                LayoutRebuilder.ForceRebuildLayoutImmediate(skillDescRect);
                LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewSkillTip.content);
            });
        }

        private void RefreshStageSlider()
        {
            for (int i = 0; i < m_goUpgradeStage.transform.childCount; i++)
            {
                var sliderStage = m_goUpgradeStage.transform.GetChild(i).GetComponent<Slider>();
                if (i < m_CurUavModule.Stage)
                {
                    sliderStage.value = 1;
                }
                else
                {
                    sliderStage.value = 0;
                }
            }
        }

        #region 皮肤预览

        private void CheckShowSkinIndex()
        {
            bool unLock = false;
            for (int i = 0; i < uavSkinConfigs.Count; i++)
            {
                var uavSkinConfig = uavSkinConfigs[i];
                string[] demandList = uavSkinConfig.unclock.Split("|");
                if (demandList.Length > 0)
                {
                    int demandType = int.Parse(demandList[0]);
                    if (demandType == 1)
                    {
                        int demandValue = int.Parse(demandList[1]);
                        unLock = m_CurUavModule.Level >= demandValue;
                    }
                    else if (demandType == 2)
                    {
                        unLock = false;
                    }

                    if (unLock)
                    {
                        selectIndex = i;
                    }
                }
            }

        }
        
        private void ResetSkinInfo()
        {
            for (int i = 0; i < uavSkinConfigs.Count; i++)
            {
                Transform curItemTransform;
                if (uavSkinObjList.Count < i + 1)
                {
                    GameObject itemObj = Instantiate(m_goSkinItem, m_scrollviewskinList.content);
                    itemObj.SetActive(true);
                    curItemTransform = itemObj.transform;
                    uavSkinObjList.Add(itemObj.transform);
                }
                else
                {
                    curItemTransform = uavSkinObjList[i];
                }
                //curItemTransform.SetAsFirstSibling();
                UpdateSkinList(i,curItemTransform);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewskinList.content);
            m_PageView.CreatePageView(uavSkinConfigs.Count);
        }

        private void UpdateSkinList(int index, Transform itemTrans)
        {
            uav_skin uavSkinConfig = uavSkinConfigs[index];
            if (uavSkinConfig == null)
            {
                return;
            }
            var rectTransform         = itemTrans.GetComponent<RectTransform>();
            Transform root            = itemTrans.Find("root");
            Transform objLock         = itemTrans.Find("root/lock");
            UIImage imgBg             = itemTrans.Find("root/imgBg").GetComponent<UIImage>();
            UIImage isUse             = itemTrans.Find("root/isUse").GetComponent<UIImage>();
            UIImage skinIcon          = itemTrans.Find("root/imgBg/skinIcon").GetComponent<UIImage>();
            UIButton btnClickSkin     = itemTrans.Find("root/btnClickSkin").GetComponent<UIButton>();

            root.localScale = new Vector3(0.9f, 0.9f, 0.9f);
            bool unLock = false;
            string[] demandList = uavSkinConfig.unclock.Split("|");
            if (demandList.Length > 0)
            {
                int demandType = int.Parse(demandList[0]);
                if (demandType == 1)
                {
                    int demandValue = int.Parse(demandList[1]);
                    unLock = m_CurUavModule.Level >= demandValue;
                }
                else if (demandType == 2)
                {
                    unLock = false;
                }
            }
            isUse.gameObject.SetActive(m_CurUavModule.Skin == uavSkinConfig.id);
            
            string bgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_fieji1.png";
            if (uavSkinConfig.uav_quality == quality.quality_purple)
            {
                bgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_fieji2.png";
            }
            else if (uavSkinConfig.uav_quality == quality.quality_orange)
            {
                bgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_fieji3.png";
            }
            imgBg.SetImage(bgPath);
            
            objLock.gameObject.SetActive(!unLock);
            if (!string.IsNullOrEmpty(uavSkinConfig.picture))
            {
                skinIcon.SetImage(uavSkinConfig.picture);
            }
            btnClickSkin.onClick.RemoveAllListeners();
            btnClickSkin.onClick.AddListener((() =>
            {
                m_PageView.pageTo(index);
            }));
        }

        private void InitPageView()
        {
            if (uavSkinConfigs.Count == 0)
            {
                return;
            }

            m_PageView.OnUpdatePageViewCell = (index) =>
            {

            };
            //页签发生改变
            m_PageView.OnPageChanged = (i, o) =>
            {
                ShowNowItem(i,o);
            };
            
            m_SkinDrag.m_BeginDrag = (eventData, go, x, y) =>
            {
                startPosition = x;
            };
            m_SkinDrag.m_EndDrag = (eventData, go, x, y) =>
            {
                float posX = x;
                if (startPosition - posX > 0)
                {
                    PageToNext();
                }
                else
                {
                    PageToLast();
                }
            };
            var uiDrag = m_transDrag.GetComponent<UIDragXYDir>();
            uiDrag.m_BeginDrag = (eventData, go, x, y) =>
            {
                rotateX = x;
            };
            uiDrag.m_OnDrag = (eventData, go, x, y) =>
            {
                if (uavObj != null && rotateX != x)
                {
                    uavObj.transform.Rotate(Vector3.forward, (rotateX - x) * rotateSpeed);
                    rotateX = x;
                }
            };
        }

        private void ShowNowItem(int index,GameObject nowObj = null)
        {
            if (selectIndex != -1)
            {
                if (selectIndex < uavSkinObjList.Count)
                {
                    Transform uavSkinObj = uavSkinObjList[selectIndex];
                    Transform root = uavSkinObj.Find("root");
                    // root.localScale = new Vector3(0.9f, 0.9f, 0.9f);
                    root.DOScale(0.9f,0.2f);
                    if (uavSkinObj != null)
                    {                    
                        Canvas canvas = uavSkinObj.transform.GetComponent<Canvas>();
                        if (canvas != null)
                        {
                            Destroy(canvas);
                        }
                    }
                }
            }

            selectIndex = index;
            m_PageView.pageTo(index);
            if (nowObj != null)
            {
                Transform nowObjRoot = nowObj.transform.Find("root");
                // nowObjRoot.localScale = new Vector3(1.3f, 1.3f, 1.3f);
                nowObjRoot.DOScale(1.3f, 0.2f);
                Canvas canvas = nowObj.GetOrAddComponent<Canvas>();
                canvas.enabled = true;
                canvas.overrideSorting = true;
                if (canvas != null)
                {
                    canvas.sortingOrder = this.Depth + 100;
                }
            }

            ResetUAVSkinInfo();
        }

        private void ResetUAVSkinInfo()
        {
            m_txtUnLockDemamd.gameObject.SetActive(false);
            m_btnUnLock.gameObject.SetActive(false);
            if (selectIndex < uavSkinConfigs.Count)
            {
                uav_skin uavSkinConfig = uavSkinConfigs[selectIndex];
                string nameColor = "#70e6f1";
                string nameBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_light1.png";
                string nameOutLineColor = "#000000";
                if (uavSkinConfig.uav_quality == quality.quality_purple)
                {
                    nameColor = "#f496fa";
                    nameBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_light2.png";
                    nameOutLineColor = "#701596";
                }
                else if (uavSkinConfig.uav_quality == quality.quality_orange)
                {
                    nameColor = "#e89836";
                    nameBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_shuxing_light3.png";
                    nameOutLineColor = "#6c2400";
                }
                string skinName = ToolScriptExtend.GetLang(uavSkinConfig.name);
                m_txtSkinName.text = $"<color={nameColor}>{skinName}</color>";
                m_imgSkinNameBg.SetImage(nameBgPath);
                ToolScriptExtend.SetOutlineColorAndUIShadow(m_txtSkinName,nameOutLineColor,nameOutLineColor);
                string[] demandList = uavSkinConfig.unclock.Split("|");
                bool unLock = false;
                if (demandList.Length > 0)
                {
                    int demandType = int.Parse(demandList[0]);
                    if (demandType == 1)
                    {
                        int demandValue = int.Parse(demandList[1]);
                        unLock = m_CurUavModule.Level >= demandValue;
                        m_txtUnLockDemamd.gameObject.SetActive(!unLock);
                        m_txtUnLockDemamd.text = ToolScriptExtend.GetLangFormat(1242,demandValue.ToString());
                    }
                    else if (demandType == 2)
                    {
                        m_btnUnLock.gameObject.SetActive(true);
                    }
                }

                m_btnUseSkin.gameObject.SetActive(unLock);
                if (unLock)
                {
                    bool isUse = m_CurUavModule.Skin == uavSkinConfig.id;
                    m_btnUseSkin.SetButtonGray(isUse);
                    m_btnUseSkin.interactable = !isUse;
                    UIImage btnImg = m_btnUseSkin.transform.GetChild(0).GetComponent<UIImage>();
                    btnImg.SetImageGray(isUse);
                    string useSkinStr = ToolScriptExtend.GetLang(1052);
                    if (!isUse)
                    {
                        useSkinStr = ToolScriptExtend.GetLang(1100001);
                    }

                    m_txtUseSkin.text = useSkinStr;
                }

                ResetUAVSkinDisplay(uavSkinConfig.model);
                m_goSkinAttr.gameObject.SetActive(uavSkinConfig.skin_attribute.Count > 0);
                if (uavSkinConfig.skin_attribute.Count > 0)
                {
                    RefreshSkinAttrInfo(uavSkinConfig);
                }
                
            }
        }

        private void ResetUAVSkinDisplay(string displayPath)
        {
            if (string.IsNullOrEmpty(displayPath))
            {
                return;
            }
            
            if (uavObj != null)
                Destroy(uavObj);

            GameEntry.Resource.LoadAsset(displayPath, new LoadAssetCallbacks((assetName, asset, duration, userData) =>
            {
                var prefab = asset as GameObject;
                uavObj = Instantiate(prefab, m_transUavDisplay);
                uavObj.transform.SetLayer(6);
                OnResetUavObj();
                //uavObj.transform.DOMove();
                Vector3 endValue = new Vector3(m_transUavDisplay.transform.localPosition.x,m_transUavDisplay.transform.localPosition.y,m_transUavDisplay.transform.localPosition.z);
                m_transUavDisplay.transform.localPosition = new Vector3(m_transUavDisplay.transform.localPosition.x + 180,m_transUavDisplay.transform.localPosition.y + 130,m_transUavDisplay.transform.localPosition.z);
                m_transUavDisplay.transform.DOLocalMove(endValue, 0.5f).SetEase(Ease.OutQuad);
            }));
        }

        private void RefreshSkinAttrInfo(uav_skin uavSkinConfig)
        {
            if (uavSkinConfig == null)
            {
                return;
            }

            if (uavSkinAttrObjList.Count > 0)
            {
                for (int i = 0; i < uavSkinAttrObjList.Count; i++)
                {
                    uavSkinAttrObjList[i].gameObject.SetActive(false);
                }
            }
            
            List<attributes> attributesList = uavSkinConfig.skin_attribute;
            for (int i = 0; i < attributesList.Count; i++)
            {
                Transform curItemTransform;
                if (uavSkinAttrObjList.Count < i + 1)
                {
                    GameObject itemObj = Instantiate(m_goSkinAttrItem, uavSkinAttrConent);
                    itemObj.SetActive(true);
                    curItemTransform = itemObj.transform;
                    uavSkinAttrObjList.Add(itemObj.transform);
                }
                else
                {
                    curItemTransform = uavSkinAttrObjList[i];
                    uavSkinAttrObjList[i].gameObject.SetActive(true);
                }
                //curItemTransform.SetAsFirstSibling();
                UpdateSkinAttrList(i,curItemTransform,attributesList[i]);
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewskinList.content);
        }

        private void UpdateSkinAttrList(int index, Transform itemTrans, attributes skinAttrCfg)
        {
            UIText txtAttrName   = itemTrans.Find("txtAttrName").GetComponent<UIText>();
            UIText txtAttrValue   = itemTrans.Find("txtAttrName/txtAttrValue").GetComponent<UIText>();
            
            attributes_type buildAttrType = skinAttrCfg.attributes_type;
            txtAttrName.text = ToolScriptExtend.GetNameByAttrbuteType(buildAttrType);
            float attribute = skinAttrCfg.value_type switch
            {
                valuetype.valuetype_1 => skinAttrCfg.value,
                valuetype.valuetype_2 => (float)skinAttrCfg.value/10000,
                _ => 0
            };
            string valueStr = string.Empty;
            if (skinAttrCfg.value_type == valuetype.valuetype_2)
            {
                valueStr = $"+{Mathf.FloorToInt(attribute * 100)}%";
            }
            else if (buildAttrType == attributes_type.attributes_type_55 ||
                     buildAttrType == attributes_type.attributes_type_56 ||
                     buildAttrType == attributes_type.attributes_type_63 ||
                     buildAttrType == attributes_type.attributes_type_73)
            {
                valueStr = TimeHelper.FormatGameTimeWithDays((int)attribute);
            }
            else
            {
                valueStr = $"+{ToolScriptExtend.FormatNumberWithSeparator(attribute)}";
            }
            txtAttrValue.text = valueStr;
        }

        private void PageToLast()
        {
            if (selectIndex - 1 < 0)
            {
                return;
            }
            m_PageView.pageToLast();
        }

        private void PageToNext()
        {
            if (selectIndex + 1 > uavSkinConfigs.Count - 1)
            {
                return;
            }
            m_PageView.pageToNext();
        }
        
        #endregion

        #region 组件

        private void ResetComponentUI()
        {
            m_txtComponentPower.text = uavLevelConfig.power.ToString();
            m_UavComponents = m_CurUavModule.UavComponents;
            bool isNoComponents = m_UavComponents.Count == 0;
            m_goExtraAttributes.gameObject.SetActive(!isNoComponents);
            RefreComponentList();
            RefreComponentAttrList();

            bool isHaveBatter = GameEntry.LogicData.UAVData.CheckAllComponentIsHaveBatter();
            m_btnOneClickEquip.gameObject.SetActive(isHaveBatter);
        }

        private void RefreComponentList()
        {
            int componentCount = m_goEquip.transform.childCount;
            for (int i = 0; i < componentCount; i++)
            {
                Transform equipObj = m_goEquip.transform.GetChild(i);
                UpdateComponentItems(i, equipObj);
            }
        }

        private void UpdateComponentItems(int index, Transform itemTrans)
        {
            var rectTransform        = itemTrans.GetComponent<RectTransform>();
            Transform partBg         = itemTrans.Find("partBg");
            UIImage normalEquipIcon  = partBg.Find("normalEquipIcon").GetComponent<UIImage>();
            Transform itemRoot       = partBg.Find("itemRoot");
            Transform itemPos        = partBg.Find("itemRoot/itemPos");
            Transform itemMsk        = partBg.Find("itemRoot/itemMsk");
            Transform componentRed   = partBg.Find("componentRed");
            Text level               = partBg.Find("itemRoot/level").GetComponent<Text>();
            UIButton btnClick        = partBg.Find("btnClick").GetComponent<UIButton>();

            componenttype cType = (componenttype)index + 1;
            uint componentId = GameEntry.LogicData.UAVData.UavModule.GetComponentByType(cType);
            if (componentId != 0)
            {
                itemRoot.gameObject.SetActive(true);
                level.gameObject.SetActive(true);
                itemid componentItemId = (itemid)componentId;
                if(itemPos.transform.childCount <= 0)
                {
                    BagManager.CreatItem(itemPos.transform,componentItemId,1,(itemModule)=>{
                        itemModule.transform.localScale = new Vector3(0.76f, 0.76f, 1f);
                        itemModule.GetComponent<UIButton>().useTween = false;
                        itemModule.extra_desc.gameObject.SetActive(false);
                        item_config itemConfig = itemModule.GetItemConfig();
                        level.text = ToolScriptExtend.GetLangFormat(711379, itemConfig.use_value[0]);
                    });
                }
                else
                {
                    UIItemModule itemModule = itemPos.transform.GetChild(0).GetComponent<UIItemModule>();
                    itemModule.SetData(componentItemId,1);
                    itemModule.DisplayInfo();         
                    itemModule.extra_desc.gameObject.SetActive(false);
                    item_config itemConfig = itemModule.GetItemConfig();
                    level.text = ToolScriptExtend.GetLangFormat(711379, itemConfig.use_value[0]);
                }
            }
            else
            {
                itemRoot.gameObject.SetActive(false);
            }

            bool isFindBatter = GameEntry.LogicData.UAVData.FindIsHaveBatterComponentByType(cType);
            bool isCanCompound = GameEntry.LogicData.UAVData.GetComponentCanCompoundByType(cType);
            componentRed.gameObject.SetActive(isFindBatter || isCanCompound);

            btnClick.onClick.RemoveAllListeners();
            btnClick.onClick.AddListener((() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIUAVComponentUpForm, cType);
            }));
        }

        public List<attributes> GetComponentAttrList()
        {
            List<attributes> attributesList = new List<attributes>();
            foreach (KeyValuePair<uint,uint> uavComponent in m_CurUavModule.UavComponents)
            {
                itemid componentId = (itemid)uavComponent.Key;
                component_level componentLevelCfg = GameEntry.LDLTable.GetTableById<component_level>(componentId);
                for (int i = 0; i < componentLevelCfg.attribute_base.Count; i++)
                {
                    attributes attributes = componentLevelCfg.attribute_base[i];
                    attributesList.Add(attributes);
                }
            }
            
            attributesList.Sort((a, b) =>
            {

                int aWeight = ComputeAttrSortWeight(a);
                int bWeight = ComputeAttrSortWeight(b);

                return bWeight.CompareTo(aWeight);
            });

            return attributesList;
        }

        private int ComputeAttrSortWeight(attributes attributes)
        {
            int weight = 0;
            switch (attributes.attributes_type)
            {
                case attributes_type.attributes_type_hp:
                    weight += 10000;
                    break;
                case attributes_type.attributes_type_attack:
                    weight += 1000;
                    break;
                case attributes_type.attributes_type_defense:
                    weight += 100;
                    break;
            }

            switch (attributes.attributes_target)
            {
                case attributes_target.attributes_target_1:
                    weight -= 10;
                    break;
                case attributes_target.attributes_target_12:
                    weight += 10;
                    break;
            }

            return weight;
        }

        private void RefreComponentAttrList()
        {
            List<attributes> componentAttrList = GetComponentAttrList();
            for (int i = 0; i < componentAttrList.Count; i++)
            {
                GameObject itemObj;
                if (uavComponentAttrConent.transform.childCount <= i)
                {
                    itemObj = Instantiate(m_goExtraAttrItem,uavComponentAttrConent.transform);
                    itemObj.SetActive(true);
                }
                else
                {
                    itemObj = uavComponentAttrConent.transform.GetChild(i).gameObject;
                }

                UpdateComponentAttrList(i, itemObj, componentAttrList[i]);
            }
        }

        private void UpdateComponentAttrList(int index, GameObject obj, attributes cfgAttributes)
        {
            Transform itemTrans   = obj.transform;
            UIText txtAttrName    = itemTrans.Find("txtAttrName").GetComponent<UIText>();
            UIText txtAttrValue   = itemTrans.Find("txtAttrValue").GetComponent<UIText>();
            
            attributes_type uavAttrType = cfgAttributes.attributes_type;
            string attrName = ToolScriptExtend.GetNameByAttrbuteType(uavAttrType);
            int attrTargetLangId = cfgAttributes.attributes_target switch
            {
                attributes_target.attributes_target_1 => 1001,
                attributes_target.attributes_target_12 => 1174,
            };
            string attrTargetName = ToolScriptExtend.GetLang(attrTargetLangId);
            txtAttrName.text = $"{attrTargetName}{attrName}";
            float attribute = cfgAttributes.value_type switch
            {
                valuetype.valuetype_1 => cfgAttributes.value,
                valuetype.valuetype_2 => (float)cfgAttributes.value/10000,
                _ => 0
            };
            string valueStr = string.Empty;
            if (cfgAttributes.value_type == valuetype.valuetype_2)
            {
                valueStr = $"<color=#5fef87>+{attribute * 100}%</color>";
            }
            else if (uavAttrType == attributes_type.attributes_type_55 ||
                     uavAttrType == attributes_type.attributes_type_56 ||
                     uavAttrType == attributes_type.attributes_type_63 ||
                     uavAttrType == attributes_type.attributes_type_73)
            {
                valueStr = TimeHelper.FormatGameTimeWithDays((int)attribute);
            }
            else
            {
                valueStr = $"+{ToolScriptExtend.FormatNumberWithSeparator(attribute)}";
            }
            txtAttrValue.text = valueStr;
        }

        #endregion
        
        private void OnBtnHelpClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 14); 
        }

        private void OnBtnUpgradePreviewClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUAVLevelUpPreViewForm);
        }

        private void OnBtnSkinClick()
        {
            m_transDrag.gameObject.SetActive(true);
            m_imgToggleBg.gameObject.SetActive(false);
            m_goUAVInfo.gameObject.SetActive(false);
            m_goUAVSkinInfo.gameObject.SetActive(true);
            m_btnUpgradePreview.gameObject.SetActive(false);
            m_btnSkin.gameObject.SetActive(false);
            m_btnExit.gameObject.SetActive(false);
            m_btnBack.gameObject.SetActive(true);
            ResetSkinInfo();
            // m_PageView.pageTo(selectIndex,false);
            Transform uavSkinObj = uavSkinObjList[selectIndex];
            ShowNowItem(selectIndex,uavSkinObj.gameObject);
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnAttrHelpClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUAVAttributeTipForm, uavLevelConfig);
        }

        private void OnBtnSkillIconClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUAVSkillTip,new UIUAVSkillTipParams(uavLevelConfig,m_CurUavModule.SkillStar,m_btnSkillIcon.gameObject));
        }

        private void OnBtnUpgradeClick()
        {
            bool isInUpgradeStage = m_CurUavModule.GetIsInUpgradeStage();
            bool isMax = uavNextLevelConfig == null;
            if (isMax)
            {
                return;
            }

            long oneConsume = uavNextLevelConfig.consume;
            bool resoureIsEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemid.itemid_1010042,oneConsume);
            if (!resoureIsEnough)
            {
                ItemModule itemModule = new ItemModule(itemid.itemid_1010042);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(oneConsume));
                return;
            }
            if (isInUpgradeStage)
            {
                int costPart = uavNextLevelConfig.consume_part;
                bool costPartIsEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemid.itemid_1010041,costPart);
                if (!costPartIsEnough)
                {
                    ItemModule itemModule = new ItemModule(itemid.itemid_1010041);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costPart));
                    return;
                }
            }
            // todo 验证升级无人机
            GameEntry.LogicData.UAVData.UavUpgradeReq();
        }

        private void OnBtnClickBgClick()
        {
            if (m_goTipAttr.activeSelf)
            {
                m_goTipAttr.gameObject.SetActive(false);
            }
            if (m_goSkillGroup.activeSelf)
            {
                m_goSkillTip.gameObject.SetActive(false);
            }
        }

        private void OnBtnLeftClick()
        {
            PageToLast();
        }

        private void OnBtnRightClick()
        {
            PageToNext();
        }

        private void OnBtnBackClick()
        {
            m_transDrag.gameObject.SetActive(false);
            m_imgToggleBg.gameObject.SetActive(true);
            m_btnExit.gameObject.SetActive(true);
            m_goUAVInfo.gameObject.SetActive(true);
            m_btnUpgradePreview.gameObject.SetActive(true);
            m_btnSkin.gameObject.SetActive(true);
            m_btnBack.gameObject.SetActive(false);
            m_goUAVSkinInfo.gameObject.SetActive(false);
            OnResetUavObj();
        }

        private void OnBtnUnLockClick()
        {
            
        }

        private void OnBtnOneClickEquipClick()
        {
            GameEntry.LogicData.UAVData.UavOneClickEquipComponentReq();
        }

        private void OnBtnUseSkinClick()
        {
            
        }
    }
}

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &985454508586027389
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5005700874647386286}
  m_Layer: 9
  m_Name: Bone010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5005700874647386286
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 985454508586027389}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.43827987, y: 0.000000057220458, z: -0.000000019960861}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7279176474420307329}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2991243614487454194
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6107636561824540227}
  m_Layer: 9
  m_Name: slot_hurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6107636561824540227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2991243614487454194}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.8, z: 0.5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 296859606395690564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3151509416378711938
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8486946720002320582}
  m_Layer: 9
  m_Name: Bone006(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8486946720002320582
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151509416378711938}
  serializedVersion: 2
  m_LocalRotation: {x: 2.5777066e-14, y: -0.00000010811685, z: 0.00000023841858, w: 1}
  m_LocalPosition: {x: -0.6509375, y: 0.569319, z: 0.89461493}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3180403867426735291}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3212073374237068345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7392628325773162112}
  m_Layer: 9
  m_Name: Bone012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7392628325773162112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3212073374237068345}
  serializedVersion: 2
  m_LocalRotation: {x: 6.123234e-17, y: -6.123234e-17, z: 6.123234e-17, w: 1}
  m_LocalPosition: {x: -0.64009184, y: 0.00000022888183, z: -4.823778e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1432803313787334815}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3556667736021441264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8440303698227518596}
  m_Layer: 9
  m_Name: Bone008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8440303698227518596
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3556667736021441264}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000023841858, y: 3.162489e-14, z: 1, w: -0.00000013315805}
  m_LocalPosition: {x: 0.6436933, y: 0.5693191, z: -0.84098285}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3180403867426735291}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3833940172509249266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7873593803957294839}
  m_Layer: 9
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7873593803957294839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3833940172509249266}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0071895174, y: -0.01103433, z: 0.7161534, w: 0.69781864}
  m_LocalPosition: {x: -0.37365395, y: -0.08209921, z: -0.8008449}
  m_LocalScale: {x: 1.0000433, y: 0.99999964, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1432803313787334815}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4242866118692367782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4119845389458671052}
  m_Layer: 9
  m_Name: slot_fire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4119845389458671052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4242866118692367782}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.8, y: 0, z: 1.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2265172451671613741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4997378123753163844
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8943454966064225094}
  m_Layer: 9
  m_Name: Bone007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8943454966064225094
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4997378123753163844}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -1.2246469e-16, w: 1}
  m_LocalPosition: {x: -0.521439, y: 0, z: 0.000000045585676}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 354472247309105034}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5055046907420959015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3180403867426735291}
  m_Layer: 9
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3180403867426735291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5055046907420959015}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9193632205995925149}
  - {fileID: 354472247309105034}
  - {fileID: 8486946720002320582}
  - {fileID: 8440303698227518596}
  - {fileID: 3286355630585586243}
  m_Father: {fileID: 296859606395690564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5178406711488052433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 296859606395690564}
  - component: {fileID: 1709875281443757136}
  m_Layer: 9
  m_Name: 23_tank_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &296859606395690564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5178406711488052433}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6107636561824540227}
  - {fileID: 8319065021215915740}
  - {fileID: 3180403867426735291}
  m_Father: {fileID: 246400600013619098}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &1709875281443757136
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5178406711488052433}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: a94d2ef55225b1748b085b07dcff0437, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &5421561671347832558
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3405702250077731660}
  m_Layer: 9
  m_Name: Dummy003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3405702250077731660
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5421561671347832558}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0051272167, y: -0.005170258, z: 0.7097836, w: 0.7043822}
  m_LocalPosition: {x: -1.5051877, y: -0.007883148, z: -0.03798538}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2265172451671613741}
  m_Father: {fileID: 9193632205995925149}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5800725205277475194
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 246400600013619098}
  - component: {fileID: 2167876487373535556}
  - component: {fileID: 3621275770388287261}
  m_Layer: 9
  m_Name: role_12504_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &246400600013619098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5800725205277475194}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 296859606395690564}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2167876487373535556
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5800725205277475194}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1770034b84515b45a96b3f473aae6c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjTransform: {fileID: 2265172451671613741}
--- !u!114 &3621275770388287261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5800725205277475194}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1eb6e16bbb69d1478b3a81466b2d544, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots:
  - Slot: 0
    Transform: {fileID: 4119845389458671052}
  - Slot: 1
    Transform: {fileID: 7966223144054998214}
  - Slot: 2
    Transform: {fileID: 4963228750089736430}
  - Slot: 9
    Transform: {fileID: 6107636561824540227}
--- !u!1 &5948445157942052086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 354472247309105034}
  m_Layer: 9
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &354472247309105034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5948445157942052086}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000023841858, y: 3.162489e-14, z: 1, w: -0.00000013315805}
  m_LocalPosition: {x: 0.6436935, y: 0.5693193, z: 0.89461476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8943454966064225094}
  m_Father: {fileID: 3180403867426735291}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6146973844315372238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8319065021215915740}
  - component: {fileID: 6528581420090220833}
  m_Layer: 9
  m_Name: 23_tank_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8319065021215915740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6146973844315372238}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.00000021137427, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 296859606395690564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6528581420090220833
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6146973844315372238}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cd405a68ac142c84f91d1ac54cf33fba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7863909394315025725, guid: d7365b9cd80591f41a90205854c32b00, type: 3}
  m_Bones:
  - {fileID: 354472247309105034}
  - {fileID: 9193632205995925149}
  - {fileID: 8486946720002320582}
  - {fileID: 1432803313787334815}
  - {fileID: 7392628325773162112}
  - {fileID: 7279176474420307329}
  - {fileID: 6446208987789673554}
  - {fileID: 7873593803957294839}
  - {fileID: 5516325005313990119}
  - {fileID: 5005700874647386286}
  - {fileID: 8440303698227518596}
  - {fileID: 3286355630585586243}
  - {fileID: 8943454966064225094}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9193632205995925149}
  m_AABB:
    m_Center: {x: -0.94828147, y: -0.012547791, z: 0.3895434}
    m_Extent: {x: 1.4774344, y: 1.1955366, z: 1.5284885}
  m_DirtyAABB: 0
--- !u!1 &6356139190365632132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6446208987789673554}
  m_Layer: 9
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6446208987789673554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6356139190365632132}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007189422, y: -0.0110344235, z: 0.7161534, w: 0.69781864}
  m_LocalPosition: {x: -0.4135146, y: -0.091046475, z: 0.74996084}
  m_LocalScale: {x: 0.9999995, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1432803313787334815}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6377245671806256047
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5516325005313990119}
  m_Layer: 9
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5516325005313990119
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6377245671806256047}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: -9.7144515e-15, z: -0.00000016298145, w: 1}
  m_LocalPosition: {x: -1.1777667, y: -0.00000047683716, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9193632205995925149}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6571099493863356754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2265172451671613741}
  m_Layer: 9
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2265172451671613741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6571099493863356754}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.0006899911, y: 0.019043883, z: -0.011166915}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4119845389458671052}
  - {fileID: 7966223144054998214}
  - {fileID: 4963228750089736430}
  - {fileID: 7279176474420307329}
  - {fileID: 1432803313787334815}
  m_Father: {fileID: 3405702250077731660}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6837555743774445710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3286355630585586243}
  m_Layer: 9
  m_Name: Bone008(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3286355630585586243
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6837555743774445710}
  serializedVersion: 2
  m_LocalRotation: {x: 2.5777066e-14, y: -0.00000010811685, z: 0.00000023841858, w: 1}
  m_LocalPosition: {x: -0.6509375, y: 0.5693188, z: -0.84098274}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3180403867426735291}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8123925967065128148
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1432803313787334815}
  m_Layer: 9
  m_Name: Bone011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1432803313787334815
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8123925967065128148}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50885063, y: -0.49336883, z: 0.51201564, w: -0.4852816}
  m_LocalPosition: {x: -0.011095049, y: -0.31482315, z: 0.028914545}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6446208987789673554}
  - {fileID: 7873593803957294839}
  - {fileID: 7392628325773162112}
  m_Father: {fileID: 2265172451671613741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8643757847806629495
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9193632205995925149}
  m_Layer: 9
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9193632205995925149
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8643757847806629495}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000026263128, y: 0.0000026464388, z: -0.70980066, w: 0.7044026}
  m_LocalPosition: {x: -0.0036074233, y: 0.5423453, z: -0.3820118}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5516325005313990119}
  - {fileID: 3405702250077731660}
  m_Father: {fileID: 3180403867426735291}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9031935419454172187
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7279176474420307329}
  m_Layer: 9
  m_Name: Bone009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7279176474420307329
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9031935419454172187}
  serializedVersion: 2
  m_LocalRotation: {x: -0.025204657, y: -0.021385374, z: -0.7072451, w: 0.7061953}
  m_LocalPosition: {x: 0.17852087, y: 0.4159563, z: -0.093661115}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000008}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5005700874647386286}
  m_Father: {fileID: 2265172451671613741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9178268378870285123
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4963228750089736430}
  m_Layer: 9
  m_Name: slot_fire_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4963228750089736430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9178268378870285123}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 1.3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2265172451671613741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9179399488223140516
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7966223144054998214}
  m_Layer: 9
  m_Name: slot_fire_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7966223144054998214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9179399488223140516}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.75, y: 0, z: 1.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2265172451671613741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

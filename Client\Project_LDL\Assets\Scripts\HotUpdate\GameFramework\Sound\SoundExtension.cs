//------------------------------------------------------------
// Game Framework
// Copyright © 2013-2021 <PERSON>. All rights reserved.
// Homepage: https://gameframework.cn/
// Feedback: mailto:<EMAIL>
//------------------------------------------------------------

using Game.Hotfix.Config;
using GameFramework;
using GameFramework.DataTable;
using GameFramework.Sound;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public static class SoundExtension
    {
        /// <summary>
        /// 声音类型枚举
        /// </summary>
        public enum SoundType
        {
            Music = 1,       // 背景音乐
            UISound = 2,     // UI音效，2D
            Sound = 3,       // 普通音效，支持3D
            HeroVoice = 4,   // 英雄语音，2D
        }
        private const float FadeVolumeDuration = 1f;
        private static int? s_MusicSerialId = null;

        /// <summary>
        /// 播放声音的通用内部方法
        /// </summary>
        private static int? PlaySoundInternal(this SoundComponent soundComponent, int soundId, SoundType? forceSoundType = null, Entity bindingEntity = null, object userData = null)
        {
            if (!GameEntry.LDLTable.HaseTable<sound_config>())
            {
                return null;
            }

            var tbSound = GameEntry.LDLTable.GetTableById<sound_config>(soundId);
            if (tbSound == null)
            {
                Log.Warning("Can not load sound '{0}' from data table.", soundId.ToString());
                return null;
            }

            PlaySoundParams playSoundParams = PlaySoundParams.Create();

            // 确定声音类型：优先使用强制指定的类型，否则使用配置表中的类型
            SoundType soundType = forceSoundType ?? (SoundType)tbSound.music_type;

            string assetPath;
            string soundGroupName;
            int assetPriority;

            switch (soundType)
            {
                case SoundType.Music:
                    // 音乐使用固定参数，不依赖配置表
                    playSoundParams.Priority = 64;
                    playSoundParams.Loop = true;
                    playSoundParams.VolumeInSoundGroup = 1f;
                    playSoundParams.FadeInSeconds = FadeVolumeDuration;
                    playSoundParams.SpatialBlend = 0f;
                    assetPath = AssetUtility.GetMusicAsset(tbSound.assetname);
                    soundGroupName = "Music";
                    assetPriority = Constant.AssetPriority.MusicAsset;
                    break;

                case SoundType.Sound:
                    playSoundParams.Priority = tbSound.priority;
                    playSoundParams.Loop = tbSound.loop;
                    playSoundParams.VolumeInSoundGroup = tbSound.volume;
                    playSoundParams.SpatialBlend = tbSound.spatialblend;
                    playSoundParams.MaxDistance = tbSound.maxdistance;
                    assetPath = AssetUtility.GetSoundAsset(tbSound.assetname);
                    soundGroupName = "Sound";
                    assetPriority = Constant.AssetPriority.SoundAsset;
                    break;

                case SoundType.UISound:
                    playSoundParams.Priority = tbSound.priority;
                    playSoundParams.Loop = false;
                    playSoundParams.VolumeInSoundGroup = tbSound.volume;
                    playSoundParams.SpatialBlend = 0f;
                    assetPath = AssetUtility.GetUISoundAsset(tbSound.assetname);
                    soundGroupName = "UISound";
                    assetPriority = Constant.AssetPriority.UISoundAsset;
                    break;

                case SoundType.HeroVoice:
                    playSoundParams.Priority = tbSound.priority;
                    playSoundParams.Loop = false;
                    playSoundParams.VolumeInSoundGroup = tbSound.volume;
                    playSoundParams.SpatialBlend = 0f;
                    assetPath = AssetUtility.GetUISoundAsset(tbSound.assetname);
                    soundGroupName = "HeroVoice";
                    assetPriority = Constant.AssetPriority.UISoundAsset;
                    break;

                default:
                    Log.Error("Unsupported sound type: {0}", soundType);
                    return null;
            }

            return soundComponent.PlaySound(assetPath, soundGroupName, assetPriority, playSoundParams,
                bindingEntity != null ? bindingEntity.Entity : null, userData);
        }

        public static int? PlayMusic(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            soundComponent.StopMusic();

            s_MusicSerialId = soundComponent.PlaySoundInternal(soundId, SoundType.Music, null, userData);
            return s_MusicSerialId;
        }

        public static void StopMusic(this SoundComponent soundComponent)
        {
            if (!s_MusicSerialId.HasValue)
            {
                return;
            }

            soundComponent.StopSound(s_MusicSerialId.Value, FadeVolumeDuration);
            s_MusicSerialId = null;
        }

        public static int? PlaySound(this SoundComponent soundComponent, int soundId, Entity bindingEntity = null, object userData = null)
        {
            return soundComponent.PlaySoundInternal(soundId, null, bindingEntity, userData);
        }

        public static int? PlayUISound(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            return soundComponent.PlaySoundInternal(soundId, SoundType.UISound, null, userData);
        }

        public static int? PlayHeroVoice(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            return soundComponent.PlaySoundInternal(soundId, SoundType.HeroVoice, null, userData);
        }

        public static bool IsMuted(this SoundComponent soundComponent, string soundGroupName)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return true;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return true;
            }

            return soundGroup.Mute;
        }

        public static void Mute(this SoundComponent soundComponent, string soundGroupName, bool mute)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return;
            }

            soundGroup.Mute = mute;

            GameEntry.Setting.SetBool(Utility.Text.Format(Constant.Setting.SoundGroupMuted, soundGroupName), mute);
            GameEntry.Setting.Save();
        }

        public static float GetVolume(this SoundComponent soundComponent, string soundGroupName)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return 0f;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return 0f;
            }

            return soundGroup.Volume;
        }

        public static void SetVolume(this SoundComponent soundComponent, string soundGroupName, float volume)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return;
            }

            soundGroup.Volume = volume;

            GameEntry.Setting.SetFloat(Utility.Text.Format(Constant.Setting.SoundGroupVolume, soundGroupName), volume);
            GameEntry.Setting.Save();
        }
    }
}

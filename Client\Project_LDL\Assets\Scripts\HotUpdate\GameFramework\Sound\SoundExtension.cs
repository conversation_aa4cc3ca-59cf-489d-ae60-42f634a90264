//------------------------------------------------------------
// Game Framework
// Copyright © 2013-2021 <PERSON>. All rights reserved.
// Homepage: https://gameframework.cn/
// Feedback: mailto:<EMAIL>
//------------------------------------------------------------

using Game.Hotfix.Config;
using GameFramework;
using GameFramework.DataTable;
using GameFramework.Sound;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public static class SoundExtension
    {
        private const float FadeVolumeDuration = 1f;
        private static int? s_MusicSerialId = null;

        public static int? PlayMusic(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            soundComponent.StopMusic();

            if (GameEntry.LDLTable.HaseTable<sound_config>())
            {
                var tbSound = GameEntry.LDLTable.GetTableById<sound_config>(soundId);
                if (tbSound == null)
                {
                    Log.Warning("Can not load sound '{0}' from data table.", soundId.ToString());
                    return null;
                }
                
               PlaySoundParams playSoundParams = PlaySoundParams.Create();
               playSoundParams.Priority = 64;
               playSoundParams.Loop = true;
               playSoundParams.VolumeInSoundGroup = 1f;
               playSoundParams.FadeInSeconds = FadeVolumeDuration;
               playSoundParams.SpatialBlend = 0f;
               s_MusicSerialId = soundComponent.PlaySound(AssetUtility.GetMusicAsset(tbSound.assetname), "Music", Constant.AssetPriority.MusicAsset, playSoundParams, null, userData);
               return s_MusicSerialId;
            }
            
            return null;
        }

        public static void StopMusic(this SoundComponent soundComponent)
        {
            if (!s_MusicSerialId.HasValue)
            {
                return;
            }

            soundComponent.StopSound(s_MusicSerialId.Value, FadeVolumeDuration);
            s_MusicSerialId = null;
        }

        public static int? PlaySound(this SoundComponent soundComponent, int soundId, Entity bindingEntity = null, object userData = null)
        {
            if (GameEntry.LDLTable.HaseTable<sound_config>())
            {
                var tbSound = GameEntry.LDLTable.GetTableById<sound_config>(soundId);
                if (tbSound == null)
                {
                    Log.Warning("Can not load sound '{0}' from data table.", soundId.ToString());
                    return null;
                }
                
                PlaySoundParams playSoundParams = PlaySoundParams.Create();
                playSoundParams.Priority = tbSound.priority;
                playSoundParams.Loop = tbSound.loop;
                playSoundParams.VolumeInSoundGroup = tbSound.volume;
                playSoundParams.SpatialBlend = tbSound.spatialblend;
                playSoundParams.MaxDistance = tbSound.maxdistance;
                return soundComponent.PlaySound(AssetUtility.GetSoundAsset(tbSound.assetname), "Sound", Constant.AssetPriority.SoundAsset, playSoundParams, bindingEntity != null ? bindingEntity.Entity : null, userData);
            }

            return null;
        }

        public static int? PlayUISound(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            if (GameEntry.LDLTable.HaseTable<sound_config>())
            {
                var tbSound = GameEntry.LDLTable.GetTableById<sound_config>((int)soundId);
                if (tbSound == null)
                {
                    Log.Warning("Can not load sound '{0}' from data table.", soundId.ToString());
                    return null;
                }
            
                PlaySoundParams playSoundParams = PlaySoundParams.Create();
                playSoundParams.Priority = tbSound.priority;
                playSoundParams.Loop = false;
                playSoundParams.VolumeInSoundGroup = tbSound.volume;
                playSoundParams.SpatialBlend = 0f;
                return soundComponent.PlaySound(AssetUtility.GetUISoundAsset(tbSound.assetname), "UISound", Constant.AssetPriority.UISoundAsset, playSoundParams, userData);
            }

            return null;
        }

        public static int? PlayHeroVoice(this SoundComponent soundComponent, int soundId, object userData = null)
        {
            if (GameEntry.LDLTable.HaseTable<sound_config>())
            {
                var tbSound = GameEntry.LDLTable.GetTableById<sound_config>((int)soundId);
                if (tbSound == null)
                {
                    Log.Warning("Can not load sound '{0}' from data table.", soundId.ToString());
                    return null;
                }
            
                PlaySoundParams playSoundParams = PlaySoundParams.Create();
                playSoundParams.Priority = tbSound.priority;
                playSoundParams.Loop = false;
                playSoundParams.VolumeInSoundGroup = tbSound.volume;
                playSoundParams.SpatialBlend = 0f;
                return soundComponent.PlaySound(AssetUtility.GetUISoundAsset(tbSound.assetname), "HeroVoice", Constant.AssetPriority.UISoundAsset, playSoundParams, userData);
            }

            return null;
        }

        public static bool IsMuted(this SoundComponent soundComponent, string soundGroupName)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return true;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return true;
            }

            return soundGroup.Mute;
        }

        public static void Mute(this SoundComponent soundComponent, string soundGroupName, bool mute)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return;
            }

            soundGroup.Mute = mute;

            GameEntry.Setting.SetBool(Utility.Text.Format(Constant.Setting.SoundGroupMuted, soundGroupName), mute);
            GameEntry.Setting.Save();
        }

        public static float GetVolume(this SoundComponent soundComponent, string soundGroupName)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return 0f;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return 0f;
            }

            return soundGroup.Volume;
        }

        public static void SetVolume(this SoundComponent soundComponent, string soundGroupName, float volume)
        {
            if (string.IsNullOrEmpty(soundGroupName))
            {
                Log.Warning("Sound group is invalid.");
                return;
            }

            ISoundGroup soundGroup = soundComponent.GetSoundGroup(soundGroupName);
            if (soundGroup == null)
            {
                Log.Warning("Sound group '{0}' is invalid.", soundGroupName);
                return;
            }

            soundGroup.Volume = volume;

            GameEntry.Setting.SetFloat(Utility.Text.Format(Constant.Setting.SoundGroupVolume, soundGroupName), volume);
            GameEntry.Setting.Save();
        }
    }
}

using JetBrains.Annotations;
using UnityEngine;

namespace Game.Hotfix
{
    public class Battle5v5ParamReplay : Battle5v5ParamBase
    {
        public Battle.Report Report => m_Report;
        
        [CanBeNull] private Battle.Report m_Report;

        public Battle5v5ParamReplay(Battle.Report report)
        {
            m_Report = report;
        }

        public override bool IsFinish()
        {
            return m_Report != null;
        }
    }
}
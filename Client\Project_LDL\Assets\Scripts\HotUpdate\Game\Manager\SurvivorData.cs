using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using Roledata;
using Survivor;

namespace Game.Hotfix
{
    public class SurvivorData
    {
        private List<SurvivorMoudle> m_SurvivorList = new List<SurvivorMoudle>();
        public List<SurvivorMoudle> SurvivorMoudleList => m_SurvivorList;
        private Dictionary<int, List<uint>> m_BuildSurvivorDic = new Dictionary<int, List<uint>>();

        public void Init(RoleSurvivor roleSurvivor)
        {
            if (roleSurvivor == null)
            {
                return;
            }
            foreach (Survivor.Survivor survivor in roleSurvivor.Survivors)
            {
                SurvivorMoudle survivorMoudle = new SurvivorMoudle(survivor);
                m_SurvivorList.Add(survivorMoudle);
                if (survivorMoudle.BuildingId != 0)
                {
                    GameEntry.LogicData.BuildingData.BuildingInsertSurvivor((int)survivorMoudle.BuildingId, survivorMoudle.Id);
                }
            }

            BindRedDotLogic();
            InitEvent();
        }

        public void InitEvent()
        {
            if (!GameEntry.Event.Check(ItemChangeEventArgs.EventId, OnItemChange))
                GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
        }
        
        public void BindRedDotLogic()
        {
            var rootFlag = EnumRed.Survivor.ToString();
            //幸存者根节点
            RedPointManager.Instance.AddNodeTo(EnumRed.Root.ToString(), rootFlag);
            //升星 橙色
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Survivor_UpStar_Orange.ToString(), () =>
            {
                if (CheckRedDotByQuality(quality.quality_orange))
                {
                    return 1;
                }
                return 0;
            });
            //升星 紫色
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Survivor_UpStar_Purple.ToString(), () =>
            {
                if (CheckRedDotByQuality(quality.quality_purple))
                {
                    return 1;
                }
                return 0;
            });
        }

        #region 外部调用

        public void OnItemChange(object sender, GameEventArgs e)
        {
            if (e is ItemChangeEventArgs args)
            {
                if (args.changList != null && args.changList.Count > 0)
                {
                    bool isSurvivorItem = false;
                    for (var i = 0; i < args.changList.Count; i++)
                    {
                        itemid itemId = args.changList[i];
                        var itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemId);
                        if (itemId == itemid.itemid_1010001 || itemConfig.item_subtype == itemsubtype.itemsubtype_survivorfragment)
                        {
                            isSurvivorItem = true;
                            break;
                        }
                    }

                    if (isSurvivorItem)
                    {
                        bool survivorIsCanUp = CheckAllSurvivorIsCanUp();
                        if (survivorIsCanUp)
                        {
                            GameEntry.LogicData.BuildingData.UpdataBuildingSurvivorCanUp(true);
                        }
                    }
                }
            }
        }

        public bool CheckRedDotByQuality(quality curQuality)
        {
            for (int i = 0; i < SurvivorMoudleList.Count; i++)
            {
                SurvivorMoudle survivorMoudle = SurvivorMoudleList[i];
                if (survivorMoudle.Quality == curQuality && survivorMoudle.GetCanIncreaseStar())
                {
                    return true;
                }
            }

            return false;
        }
        
        public void OnSurvivorChange(RoleSurvivor roleSurvivor)
        {
            bool isAdd = false;
            for (var i = 0; i < roleSurvivor.Survivors.Count; i++)
            {
                Survivor.Survivor survivor = roleSurvivor.Survivors[i];
                bool exists = m_SurvivorList.Exists(s => s.Id == survivor.Code);
                if (!exists)
                {
                    if (!isAdd)
                    {
                        isAdd = true;
                    }

                    m_SurvivorList.Add(new SurvivorMoudle(survivor));
                }
                else
                {
                    m_SurvivorList[i].UpdateInfo(survivor);
                }
            }

            if (isAdd)
            {
                GameEntry.LogicData.BuildingData.UpdataAllBuildingSurvivorDispatchState();
            }
        }

        public SurvivorMoudle GetSurvivorModuleById(uint sId)
        {
            if (sId != 0)
            {
                foreach (var survivorMoudle in m_SurvivorList)
                {
                    if (survivorMoudle.Id == sId)
                    {
                        return survivorMoudle;
                    }
                }
            }

            return null;
        }

        public List<uint> GetSurvivorIdListByBuildId(uint buildId)
        {
            List<uint> findList = new List<uint>();
            foreach (var survivorMoudle in m_SurvivorList)
            {
                if (survivorMoudle.BuildingId == buildId)
                {
                    findList.Add(survivorMoudle.Id);
                }
            }

            return findList;
        }

        public int GetAllSurviviorCount()
        {
            List<survivor_list> survivorCfg = Game.GameEntry.LDLTable.GetTable<survivor_list>();
            return survivorCfg.Count;
        }
        
        public int GetTotalSurviviorCfgCountByType(quality curQuality)
        {
            List<survivor_list> survivorCfg = Game.GameEntry.LDLTable.GetTable<survivor_list>();
            int count = 0;
            foreach (survivor_list cfg in survivorCfg)
            {
                if (cfg.quality == curQuality)
                {
                    count += 1;
                }
            }

            return count;
        }
        
        public int GetSurviviorMoudleCountByType(quality curQuality)
        {
            int count = 0;
            foreach (var survivorMoudle in m_SurvivorList)
            {
                if (survivorMoudle.Quality == curQuality)
                {
                    count += 1;
                }
            }

            return count;
        }

        public int GetTotalSurvivorPower()
        {
            int totalPower = 0;
            foreach (var survivorMoudle in m_SurvivorList)
            {
                totalPower += survivorMoudle.Power;
            }

            return totalPower;
        }

        public bool CheckAllSurvivorIsCanUp()
        {
            foreach (var survivorMoudle in m_SurvivorList)
            {
                if (survivorMoudle.GetCanIncreaseStar())
                {
                    return true;
                }
            }

            return false;
        }
        
        // 是否可升星
        public bool GetCanUpStarById(int survivorId)
        {
            survivor_list survivorCfg = GameEntry.LDLTable.GetTableById<survivor_list>(survivorId);
            survivor_star survivorStarConfigNext = GetSurvivorStarConfig(survivorCfg.profession,
                1,survivorCfg.quality);
            if (survivorStarConfigNext == null)
            {
                return false;
            }
            return true;
        }

        public SurvivorMoudle FindSurvivorMoudleCanDispatch(buildtype buildtype)
        {
            foreach (var survivorMoudle in m_SurvivorList)
            {
                survivor_config config = GetSurvivorConfigById(survivorMoudle.SurvivorCfg.profession_id);
                if (config != null)
                {
                    if (survivorMoudle.BuildingId == 0 && config.build_type == buildtype)
                    {
                        return survivorMoudle;
                    }
                }
            }
            return null;
        }
        
        public SurvivorMoudle FindSurvivorMoudleHaveBetter(buildtype buildtype,uint sId)
        {
            SurvivorMoudle selectMoudle = GetSurvivorModuleById(sId);
            foreach (var survivorMoudle in m_SurvivorList)
            {
                survivor_config config = GetSurvivorConfigById(survivorMoudle.SurvivorCfg.profession_id);
                if (config != null)
                {
                    if (survivorMoudle.BuildingId == 0 && config.build_type == buildtype && survivorMoudle.Quality > selectMoudle.Quality)
                    {
                        return survivorMoudle;
                    }
                }
            }
            return null;
        }

        public survivor_config GetSurvivorConfigById(int configId)
        {
            survivor_config survivorConfig = GameEntry.LDLTable.GetTableById<survivor_config>(configId);
            return survivorConfig;
        }
        
        public string GetQualityBg(quality curQuality)
        {
            var t = curQuality;
            return curQuality switch
            {
                quality.quality_white => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_1bg-1.png",
                quality.quality_green => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_2bg.png",
                quality.quality_blue => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_3bg.png",
                quality.quality_purple => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_1bg.png",
                quality.quality_orange => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_5bg.png",
                _ => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_1bg-1.png",
            };
        }       
        
        public string GetHeadQualityBg(quality curQuality)
        {
            var t = curQuality;
            return curQuality switch
            {
                quality.quality_white => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_1.png",
                quality.quality_green => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_2.png",
                quality.quality_blue => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_3.png",
                quality.quality_purple => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_4.png",
                quality.quality_orange => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_5.png",
                _ => "Sprite/ui_jianhzu_xingcunzhe/jianzhu_xingcunzhe_kapian_1.png",
            };
        }

        public survivor_star GetSurvivorStarConfig(survivor_profession profession, int star, quality curQuality)
        {
            List <survivor_star> survivorStarCfg = GameEntry.LDLTable.GetTable<survivor_star>();
            for (int i = 0; i < survivorStarCfg.Count; i++)
            {
                survivor_star survivorStar = survivorStarCfg[i];
                if (survivorStar.profession == profession && survivorStar.star_stage == star && survivorStar.quality == curQuality)
                {
                    return survivorStar;
                }
            }

            return null;
        }
        
        public List<survivor_star> GetSurvivorStarAttrListConfig(survivor_profession profession, quality curQuality)
        {
            List <survivor_star> survivorStarCfg = GameEntry.LDLTable.GetTable<survivor_star>();
            List<survivor_star> survivorStars = new List<survivor_star>();
            for (int i = 0; i < survivorStarCfg.Count; i++)
            {
                survivor_star survivorStar = survivorStarCfg[i];
                if (survivorStar.profession == profession && survivorStar.quality == curQuality && survivorStar.show_attributes != null)
                {
                    survivorStars.Add(survivorStar);
                }
            }

            return survivorStars;
        }
        
        #endregion

        #region 协议相关


        /// <summary>
        /// 召唤幸存者
        /// </summary>
        /// <param name="id"></param>
        public void SurvivorSummonReq(uint id)
        {
            SurvivorSummonReq survivorSummonReq = new SurvivorSummonReq();
            survivorSummonReq.Code = id;

            GameEntry.LDLNet.Send(Protocol.MessageID.SurvivorSummon, survivorSummonReq, (message) =>
            {
                SurvivorSummonResp survivorSummonResp = (SurvivorSummonResp)message;
                if (survivorSummonResp != null)
                {
                    AddOrUpdateSurvivor(survivorSummonResp.Survivor);
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingSurvivorForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingSurvivorForm,new RefreshUIDispatchParams(UISurvivorRefreshType.ActiveRefresh));
                    }
                }
            });
        }
        
        /// <summary>
        /// 添加更新幸存者
        /// </summary>
        /// <param name="survivor"></param>
        public void AddOrUpdateSurvivor(Survivor.Survivor survivor)
        {
            var survivorMoudle = GetSurvivorModuleById(survivor.Code);
            if (survivorMoudle == null)
            {
                //创建
                survivorMoudle = new SurvivorMoudle(survivor);
                m_SurvivorList.Add(survivorMoudle);
            }
            else
            {
                survivorMoudle.UpdateInfo(survivor);
            }
        }

        /// <summary>
        /// 幸存者升星
        /// </summary>
        /// <param name="id"></param>
        public void SurvivorStarUpReq(uint id,Action callBack = null)
        {
            SurvivorStarUpReq survivorStarUpReq = new SurvivorStarUpReq();
            survivorStarUpReq.Code = id;
            GameEntry.LDLNet.Send(Protocol.MessageID.SurvivorStarUp, survivorStarUpReq, (message) =>
            {
                SurvivorStarUpResp survivorStarUpResp = (SurvivorStarUpResp)message;
                if (survivorStarUpResp != null)
                {
                    var survivorMoudle = GetSurvivorModuleById(survivorStarUpResp.Code);
                    if (survivorMoudle != null)
                    {
                        survivorMoudle.UpdateInfo(survivorStarUpResp.StarStage);
                        if (survivorMoudle.Quality == quality.quality_orange)
                        {
                            RedPointManager.Instance.Dirty(EnumRed.Survivor_UpStar_Orange.ToString());
                        }
                        else
                        {
                            RedPointManager.Instance.Dirty(EnumRed.Survivor_UpStar_Purple.ToString());
                        }
                    }

                    callBack?.Invoke();
                    bool survivorIsCanUp = CheckAllSurvivorIsCanUp();
                    GameEntry.LogicData.BuildingData.UpdataBuildingSurvivorCanUp(survivorIsCanUp);
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingSurvivorForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingSurvivorForm,new RefreshUIDispatchParams(UISurvivorRefreshType.UpStarRefresh));
                    }
                }
            });
        }

        /// <summary>
        /// 幸存者 建筑派遣
        /// </summary>
        /// <param name="survivor"></param>
        public void SurvivorDispatchReq(Survivor.Survivor survivor,Action callBack)
        {
            SurvivorDispatchReq survivorDispatchReq = new SurvivorDispatchReq();
            survivorDispatchReq.Survivor = survivor;
            uint changeBuildingId = survivor.BuildNo;
            GameEntry.LDLNet.Send(Protocol.MessageID.SurvivorDispatch, survivorDispatchReq, (message) =>
            {
                SurvivorDispatchResp survivorStarUpResp = (SurvivorDispatchResp)message;
                if (survivorStarUpResp != null)
                {
                    foreach (Survivor.Survivor serverInfo in survivorStarUpResp.Survivor)
                    {
                        AddOrUpdateSurvivor(serverInfo);
                    }

                    List<uint> idListByBuildId = GetSurvivorIdListByBuildId(changeBuildingId);
                    GameEntry.LogicData.BuildingData.BuildingUpdateSurvivorList((int)changeBuildingId,idListByBuildId);
                    BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(changeBuildingId);
                    buildingModule.OnSurvivorChange(SurvivorChangeState.Remove);
                    callBack?.Invoke();
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingDispatchForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingDispatchForm);
                    }
                }
            });
        }

        /// <summary>
        /// 幸存者 快速派遣
        /// 建造快速派遣,发送建筑code 实现逻辑:当前建筑派遣的幸存者与未派遣的幸存者进行排序
        /// 人才大厅快速派遣,发送buildNo 为0 实现逻辑:全部相同类型进行对比
        /// </summary>
        /// <param name="buildId"></param>
        /// <param name="callBack">回调</param>
        public void SurvivorFastDispatchReq(uint buildId = 0,Action callBack = null)
        {
            var survivorFastDispatchReq = new SurvivorFastDispatchReq();
            survivorFastDispatchReq.BuildNo = buildId;
            
            GameEntry.LDLNet.Send(Protocol.MessageID.SurvivorFastDispatch, survivorFastDispatchReq, (message) =>
            {
                SurvivorFastDispatchResp survivorStarUpResp = (SurvivorFastDispatchResp)message;
                if (survivorStarUpResp != null)
                {
                    if (survivorStarUpResp.Survivor.Count == 0)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(1100526),
                        });
                        return;
                    }
                    foreach (Survivor.Survivor serverInfo in survivorStarUpResp.Survivor)
                    {
                        AddOrUpdateSurvivor(serverInfo);
                    }

                    if (buildId != 0)
                    {
                        // 更新单个建筑
                        List<uint> idList = GetSurvivorIdListByBuildId(buildId);
                        GameEntry.LogicData.BuildingData.BuildingUpdateSurvivorList((int)buildId,idList);
                        BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(buildId);
                        GameEntry.LogicData.BuildingData.UpdataAllBuildingSurvivorDispatchState(buildingModule.GetBuildingType());
                    }
                    else
                    {
                        GameEntry.LogicData.BuildingData.AllBuildingUpdateSurvivorList();
                        GameEntry.LogicData.BuildingData.UpdataAllBuildingSurvivorDispatchState();
                    }
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingDispatchForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingDispatchForm);
                    }
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingSurvivorForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingSurvivorForm,new RefreshUIDispatchParams(UISurvivorRefreshType.DispatchRefresh));
                    }
                    callBack?.Invoke();
                }
            });
        }

        #endregion
    }
}
using System;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitHundredForm : UGuiFormEx
    {
        private class CardInfo
        {
            public GameObject obj;
            public Transform card;
            public GameObject front;
            public GameObject back;
            public GameObject node;
            public GameObject effect;
            public bool isPlaying; //是否正在播放动画
        }

        private class DataNode
        {
            // public recruit_drops data;
            // public int count;
            public int quality;
            public int typeFlag; //英雄碎片>装备箱>技能勋章>英雄经验>强化石
            public int itemID;
            public int isWholeHero; //是否是完整英雄
            public int sumCount;
        }

        private List<CardInfo> mainList;

        private Sequence sequence1;
        private Sequence sequence2;
        private Sequence sequence3;

        private int recruitId;
        private recruittype recruitType;
        private recruitnumtype recruitNumType;
        private List<Recruit.RecruitReward> rewardList = new List<Recruit.RecruitReward>();

        private List<DataNode> heroDataList = new List<DataNode>();
        private List<DataNode> survivorDataList = new List<DataNode>();
        private List<DataNode> normalHeroDataList = new List<DataNode>();
        private List<DataNode> propDataList = new List<DataNode>();

        private List<int> switchList = new List<int>();
        private List<int> getManyList = new List<int>(); //抽到首个英雄之后，又抽到多个该英雄

        private RecruitData RecruitManager => GameEntry.LogicData.RecruitData;

        //英雄百抽
        private bool IsHeroRecruit => recruitType == recruittype.recruittype_hero;

        //幸存者百抽
        private bool IsSurvivorRecruit => recruitType == recruittype.recruittype_survivor;


        private GameObject ziseEffect;

        private GameObject huangEffect;


        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
            mainList = new List<CardInfo>();
            m_goPrefab.SetActive(false);

            var ziseEffectPath = $"Assets/ResPackage/Effect/Prefab/battle_kapai_zise2.prefab";
            var huangEffectPath = $"Assets/ResPackage/Effect/Prefab/battle_kapai_huang1.prefab";

            LoadEffectObj(ziseEffectPath, (asset) => { ziseEffect = asset as GameObject; });
            LoadEffectObj(huangEffectPath, (asset) => { huangEffect = asset as GameObject; });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_goMask.SetActive(false);
            var result = PlayerPrefs.GetInt("HundredRecruitSkip", 0);
            m_togSkip.isOn = result == 1;
            m_togSkip.onValueChanged.AddListener(isOn =>
            {
                var curValue = isOn ? 1 : 0;
                var lastValue = PlayerPrefs.GetInt("HundredRecruitSkip", 0);
                if (curValue != lastValue)
                {
                    PlayerPrefs.SetInt("HundredRecruitSkip", curValue);
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UIRecruitForm, 3);
                }
            });

            var param = userData as RecruitParams.RecruitResult;
            recruitId = param.recruitId;
            recruitType = param.recruitType;
            recruitNumType = param.numType;
            rewardList = param.rewardList;

            RecruitManager.SetTopDiamondView(m_goDiamond);
            RecruitManager.SetRecruitTicketView(recruitType, m_goTopItem);

            m_txtDesc.text = "";
            if (IsHeroRecruit)
            {
                m_txtDesc.text = ToolScriptExtend.GetLang(1100523);
            }

            if (IsSurvivorRecruit)
            {
                m_txtDesc.text = ToolScriptExtend.GetLang(1100524);
            }

            var isSkipAnim = PlayerPrefs.GetInt("HundredRecruitSkip", 0);
            if (isSkipAnim == 1)
            {
                DisplayResultDialog();
            }
            else
            {
                FrontAnimation();
                DisplayResultDialog();
            }

            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            KillSequence(sequence1);
            KillSequence(sequence2);
            KillSequence(sequence3);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            switchList.Clear();
            getManyList.Clear();
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);
            SetParticleSystemSortingOrder(m_goEffect, Depth);

            var canvas = m_goTitle.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.sortingOrder = Depth + 100;
            }
        }

        //道具数量更新
        void OnItemChange(object sender, GameEventArgs e)
        {
            if (e is ItemChangeEventArgs args)
            {
            }

            //道具数量更新
            RecruitManager.SetTopDiamondView(m_goDiamond, true);
            RecruitManager.SetRecruitTicketView(recruitType, m_goTopItem, true);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var msgId = (int)userData;
            if (msgId == 1)
            {
            }
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        #region 英雄百抽

        private void RecruitHundredHero()
        {
            heroDataList.Clear();
            propDataList.Clear();
            mainList.Clear();
            var mainRoot = m_goMain.transform;
            var otherRoot = m_goOther.transform;
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);

            ToolScriptExtend.ClearAllChild(mainRoot);
            ToolScriptExtend.ClearAllChild(otherRoot);

            var firstDic = new Dictionary<int, int>();
            var secondDic = new Dictionary<int, int>();
            var thirdDic = new Dictionary<int, int>();

            // StringBuilder sb = new StringBuilder();//数据测试查看

            var list = new List<int>();
            getManyList.Clear();
            foreach (var reward in rewardList)
            {
                var id = (int)reward.RecruitDropId;
                // sb.Append($"{id}  {reward.IsConvertPatch}\n");//数据测试查看

                var config = GameEntry.LDLTable.GetTableById<recruit_drops>(id);
                if (config == null) continue;

                var rewardId = (int)config.drop_reward.item_id;
                var itemConfig = ToolScriptExtend.GetItemConfig(rewardId);
                switch (config.recruit_display_type)
                {
                    case recruitdisplaytype.recruitdisplaytype_1:
                        if (!switchList.Contains(rewardId))
                        {
                            if (reward.IsConvertPatch)
                            {
                                switchList.Add(rewardId);
                            }
                            else
                            {
                                if (itemConfig != null && itemConfig.item_type == itemtype.itemtype_hero)
                                    GameEntry.LogicData.HeroData.SetHeroNew((itemid)rewardId, true);
                            }
                        }

                        AddDicValue(firstDic, id, 1);
                        break;
                    case recruitdisplaytype.recruitdisplaytype_2:
                        if (!switchList.Contains(rewardId))
                        {
                            if (reward.IsConvertPatch)
                            {
                                switchList.Add(rewardId);
                            }
                            else
                            {
                                if (itemConfig != null && itemConfig.item_type == itemtype.itemtype_hero)
                                    GameEntry.LogicData.HeroData.SetHeroNew((itemid)rewardId, true);
                            }
                        }

                        AddDicValue(secondDic, id, 1);
                        break;
                    case recruitdisplaytype.recruitdisplaytype_3:
                        AddDicValue(thirdDic, id, 1);
                        break;
                }

                if (itemConfig != null && itemConfig.item_type == itemtype.itemtype_hero)
                {
                    if (!reward.IsConvertPatch)
                    {
                        list.Add(rewardId);
                    }
                    else
                    {
                        if (list.Contains(rewardId))
                        {
                            getManyList.Add(rewardId);
                        }
                    }
                }
            }
            // Debug.LogError(sb.ToString());//数据测试查看
            //
            // ColorLog.PowerfulJsonDebug(getManyList,"getManyList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(switchList,"switchList");//数据测试查看
            //
            // ColorLog.PowerfulJsonDebug(firstDic,"firstDic");//数据测试查看
            // ColorLog.PowerfulJsonDebug(secondDic,"secondDic");//数据测试查看
            // ColorLog.PowerfulJsonDebug(thirdDic,"thirdDic");//数据测试查看

            FormatDicInfoToList(firstDic, heroDataList);
            FormatDicInfoToList(secondDic, normalHeroDataList);
            FormatDicInfoToList(thirdDic, propDataList);

            //排序
            heroDataList.Sort(CompareLogic);
            normalHeroDataList.Sort(CompareLogic);
            propDataList.Sort(ComparePropLogic);

            // ColorLog.PowerfulJsonDebug(heroDataList,"heroDataList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(normalHeroDataList,"normalHeroDataList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(propDataList,"propDataList");//数据测试查看

            DisplayAnim(heroDataList, normalHeroDataList, propDataList);
        }

        #endregion

        #region 幸存者百抽

        private void RecruitHundredSurvivor()
        {
            survivorDataList.Clear();
            propDataList.Clear();
            mainList.Clear();
            var mainRoot = m_goMain.transform;
            var otherRoot = m_goOther.transform;
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);

            ToolScriptExtend.ClearAllChild(mainRoot);
            ToolScriptExtend.ClearAllChild(otherRoot);

            var firstDic = new Dictionary<int, int>();
            var secondDic = new Dictionary<int, int>();
            var thirdDic = new Dictionary<int, int>();

            // StringBuilder sb = new StringBuilder();//数据测试查看

            var list = new List<int>();
            getManyList.Clear();
            foreach (var reward in rewardList)
            {
                var id = (int)reward.RecruitDropId;
                // sb.Append($"{id}  {reward.IsConvertPatch}\n");//数据测试查看

                var config = GameEntry.LDLTable.GetTableById<recruit_drops>(id);
                if (config == null) continue;

                var rewardId = (int)config.drop_reward.item_id;
                var itemConfig = ToolScriptExtend.GetItemConfig(rewardId);
                switch (config.recruit_display_type)
                {
                    case recruitdisplaytype.recruitdisplaytype_1:
                        if (!switchList.Contains(rewardId))
                        {
                            if (reward.IsConvertPatch)
                            {
                                switchList.Add(rewardId);
                            }
                        }

                        AddDicValue(firstDic, id, 1);
                        break;
                    case recruitdisplaytype.recruitdisplaytype_2:
                        if (!switchList.Contains(rewardId))
                        {
                            if (reward.IsConvertPatch)
                            {
                                switchList.Add(rewardId);
                            }
                        }

                        AddDicValue(secondDic, id, 1);
                        break;
                    case recruitdisplaytype.recruitdisplaytype_3:
                        AddDicValue(thirdDic, id, 1);
                        break;
                }

                if (itemConfig != null && itemConfig.item_type == itemtype.itemtype_survivor)
                {
                    if (!reward.IsConvertPatch)
                    {
                        list.Add(rewardId);
                    }
                    else
                    {
                        if (list.Contains(rewardId))
                        {
                            getManyList.Add(rewardId);
                        }
                    }
                }
            }
            // Debug.LogError(sb.ToString());//数据测试查看
            //
            // ColorLog.PowerfulJsonDebug(getManyList,"getManyList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(switchList,"switchList");//数据测试查看
            //
            // ColorLog.PowerfulJsonDebug(firstDic,"firstDic");//数据测试查看
            // ColorLog.PowerfulJsonDebug(secondDic,"secondDic");//数据测试查看
            // ColorLog.PowerfulJsonDebug(thirdDic,"thirdDic");//数据测试查看

            FormatDicInfoToList(firstDic, survivorDataList);
            FormatDicInfoToList(secondDic, normalHeroDataList);
            FormatDicInfoToList(thirdDic, propDataList);

            //排序
            survivorDataList.Sort(CompareLogic);
            normalHeroDataList.Sort(CompareLogic);
            propDataList.Sort(ComparePropLogic);

            // ColorLog.PowerfulJsonDebug(heroDataList,"heroDataList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(normalHeroDataList,"normalHeroDataList");//数据测试查看
            // ColorLog.PowerfulJsonDebug(propDataList,"propDataList");//数据测试查看
            DisplayAnim(survivorDataList, normalHeroDataList, propDataList);
        }

        #endregion

        #region 公共逻辑

        //播放前摇动画
        private void FrontAnimation()
        {
            Debug.Log("播放前摇动画.....");
        }

        //公共的列表展示动画
        private void DisplayAnim(List<DataNode> mainList, List<DataNode> normalList, List<DataNode> propList)
        {
            m_goMask.SetActive(true);
            
            var mainCount = mainList.Count;
            var normalCount = normalList.Count;
            var propCount = propList.Count;
            var mainRoot = m_goMain.transform;
            var otherRoot = m_goOther.transform;

            var mainRowCount = GetRowCount(3, mainCount);
            var normalRowCount = GetRowCount(3, mainCount);

            var sumHeight = CalculateContentHeight(mainCount, normalCount, propCount);
            List<Transform> nodeList = new List<Transform>();
            var cardCount = 0;
            KillSequence(sequence1);
            sequence1 = DOTween.Sequence();
            sequence1.AppendCallback(() =>
            {
                KillSequence(sequence2);
                sequence2 = DOTween.Sequence();

                if (mainCount > 0)
                {
                    for (var i = 0; i < mainCount; i++)
                    {
                        var config = mainList[i];
                        var obj = Instantiate(GetTemplateObj(false), mainRoot);
                        var cardInfo = GetSingleCardInfo(obj.transform);
                        SetCardInfoView(config, cardInfo, true);
                        var anim = cardInfo.card.GetComponent<Animation>();
                        anim.Stop();
                        cardCount++;
                        var index = cardCount;
                        sequence2.AppendInterval(0.5f);
                        sequence2.AppendCallback(() =>
                        {
                            anim["UIRecruitHundredForm_CardFlip"].speed = 1.5f;
                            anim.Play("UIRecruitHundredForm_CardFlip");
                            var hasEffect = cardInfo.effect.transform.childCount > 0;
                            if (hasEffect)
                            {
                                var temp = DOTween.Sequence();
                                temp.AppendInterval(0.4f);
                                temp.AppendCallback(() => { cardInfo.effect.gameObject.SetActive(hasEffect); });
                            }

                            if (mainCount + normalCount > 6)
                            {
                                if (index >= 6)
                                {
                                    if ((index + 2) % 3 == 0)
                                    {
                                        var num = index == 6 ? 2 : 1;
                                        MoveContentSectionAnim(365 * num, 1f);
                                    }
                                }
                            }
                        });
                    }

                    sequence2.AppendInterval(mainRowCount * 0.1f);
                }

                if (normalCount > 0)
                {
                    for (var i = 0; i < normalCount; i++)
                    {
                        cardCount++;
                        var index = cardCount;
                        var index2 = i + 1;
                        var config = normalList[i];
                        var obj = Instantiate(GetTemplateObj(true), mainRoot);
                        var cardInfo = GetSingleCardInfo(obj.transform);
                        SetCardInfoView(config, cardInfo, false);
                        ResetCardView(cardInfo);
                        var anim = cardInfo.card.GetComponent<Animation>();
                        anim.enabled = false;
                        sequence2.AppendInterval(0.2f);
                        sequence2.AppendCallback(() =>
                        {
                            SingleFlipAnim(cardInfo, null);

                            if (mainCount + normalCount > 6)
                            {
                                if (index >= 6)
                                {
                                    if ((index + 2) % 3 == 0)
                                    {
                                        var num = index == 6 ? 2 : 1;
                                        MoveContentSectionAnim(365 * num, 1f);
                                    }
                                }
                                else
                                {
                                    if (index > 3 && (index + 2) % 3 == 0)
                                    {
                                        MoveContentSectionAnim(365, 1f);
                                    }
                                }
                            }
                        });
                    }
                }
                
                // sequence2.AppendInterval(0.1f * normalCount);
                sequence2.AppendCallback(() => { ShowPropListAnim(sumHeight, propList, otherRoot); });
            });
        }
        
        private void ShowPropListAnim(float sumHeight, List<DataNode> propList, Transform otherRoot)
        {
            var finalHeight = sumHeight - m_scrollviewList.viewport.rect.height;
            if (finalHeight > 0)
            {
                var mainRect = m_goMain.GetComponent<RectTransform>();
                var mainHeight = mainRect.rect.height;
                
                if (IsHeroRecruit)
                {
                    m_scrollviewList.content.DOAnchorPos(new Vector2(0, finalHeight), 0.5f).SetDelay(0.5f).OnComplete(() =>
                    {
                        m_scrollviewList.content.DOAnchorPos(new Vector2(0, 0), 1f).SetDelay(0.8f).OnComplete(() =>
                        {
                            m_goMask.SetActive(false);
                        });
                    });
                }
                else if (IsSurvivorRecruit)
                {
                    m_scrollviewList.content.DOAnchorPos(new Vector2(0, mainHeight), mainHeight * 0.0005f).OnComplete(() =>
                    {
                        m_scrollviewList.content.DOAnchorPos(new Vector2(0, finalHeight), 0.5f).OnComplete(() =>
                        {
                            m_scrollviewList.content.DOAnchorPos(new Vector2(0, 0), 1f).SetDelay(0.8f).OnComplete(() =>
                            {
                                m_goMask.SetActive(false);
                            });
                        });
                    });
                }
                
                DOVirtual.DelayedCall(0.5f, () =>
                {
                    ShowPropList(propList, otherRoot);
                });
            }
        }

        private void ShowPropList(List<DataNode> propList, Transform otherRoot)
        {
            var propCount = propList.Count;
            if (propCount <= 0) return;
            List<Transform> nodeList = new List<Transform>();
            foreach (var config in propList)
            {
                var obj = Instantiate(m_goReward, otherRoot);
                SetPropItemInfo(config, obj);
                var target = obj.transform.Find("node");
                target.localScale = Vector3.zero;
                nodeList.Add(target);
                            
                KillSequence(sequence3);
                sequence3 = DOTween.Sequence();
                    
                for (var i = 0; i < nodeList.Count; i++)
                {
                    var node = nodeList[i];
                    var effect = node.Find("effect");
                    effect.gameObject.SetActive(false);
                    sequence3.AppendInterval(0.04f);
                    sequence3.AppendCallback(() =>
                    {
                        node.DOScale(1, 0.2f);
                        effect.gameObject.SetActive(true);
                    });
                }
                
            }
        }
        
        private void AddDicValue(Dictionary<int, int> dic, int id, int value)
        {
            //英雄类型
            if (dic.TryGetValue(id, out var count))
            {
                dic[id] = count + value;
            }
            else
            {
                dic.TryAdd(id, value);
            }
        }

        private void FormatDicInfoToList(Dictionary<int, int> dic, List<DataNode> list)
        {
            list.Clear();

            var filter = new Dictionary<int, int>(); //初始配表id不同，但奖励相同的情况
            foreach (var info in dic)
            {
                if (ToolScriptExtend.GetConfigById<recruit_drops>(info.Key, out var config))
                {
                    var id = (int)config.drop_reward.item_id;
                    var count = (int)config.drop_reward.num;
                    if (!filter.ContainsKey(id))
                    {
                        filter[id] = 0;
                    }

                    filter[id] += count * info.Value;
                }
            }

            foreach (var info in filter)
            {
                var id = info.Key;
                var count = info.Value;
                if (ToolScriptExtend.GetConfigById<item_config>(id, out var itemConfig))
                {
                    var flag = GetCompareFlag(id);
                    if (getManyList.Contains(id))
                    {
                        //特殊处理 ：当首次抽到一个英雄时显示整卡，但是百抽中又抽到了该英雄，就将之后抽的英雄转成碎片
                        list.Add(new DataNode()
                        {
                            quality = (int)itemConfig.quality,
                            typeFlag = flag,
                            itemID = id,
                            isWholeHero = 1,
                            sumCount = 1,
                        });

                        list.Add(new DataNode()
                        {
                            quality = (int)itemConfig.quality,
                            typeFlag = flag,
                            itemID = id,
                            isWholeHero = 0,
                            sumCount = count - 1,
                        });
                    }
                    else
                    {
                        list.Add(new DataNode()
                        {
                            quality = (int)itemConfig.quality,
                            typeFlag = flag,
                            itemID = id,
                            isWholeHero = IsWholeHero(itemConfig.item_type, (itemid)id),
                            sumCount = count,
                        });
                    }
                }
            }
        }

        //获取比较参考值
        private int GetCompareFlag(int id)
        {
            var flag = 100;
            if (!ToolScriptExtend.GetConfigById<item_config>(id, out var itemConfig)) return flag;
            //英雄碎片>装备箱>技能勋章>英雄经验>强化石
            // 英雄整卡：道具类型=2   子类型=无
            // 英雄碎片：道具类型=1   子类型=107
            // 装备宝箱：道具类型=1   子类型=104
            // 技能勋章：道具类型=1   子类型=101
            // 英雄经验：道具类型=1   子类型=101
            // 装备强化石：道具类型=1   子类型=101
            if (itemConfig.item_type == itemtype.itemtype_hero)
            {
                flag = 0;
            }
            else if (itemConfig.item_type == itemtype.itemtype_item)
            {
                if (itemConfig.item_subtype == itemsubtype.itemsubtype_herofragment)
                {
                    flag = 1;
                }
                else if (itemConfig.item_subtype == itemsubtype.itemsubtype_randomchest)
                {
                    flag = 2;
                }
                else if (itemConfig.item_subtype == itemsubtype.itemsubtype_normal)
                {
                    if (itemConfig.id == itemid.itemid_10)
                    {
                        flag = 3;
                    }
                    else if (itemConfig.id == itemid.itemid_5)
                    {
                        flag = 4;
                    }
                    else if (itemConfig.id == itemid.itemid_1010031)
                    {
                        flag = 5;
                    }
                }
            }

            return flag;
        }

        //是否是完整英雄 1:是 0：不是,转英雄碎片 -1：非英雄相关
        private int IsWholeHero(itemtype item_type, itemid rewardId)
        {
            if (recruitType == recruittype.recruittype_hero)
            {
                if (item_type == itemtype.itemtype_hero)
                {
                    // //判断是否拥有该英雄，如果拥有转成10个英雄碎片
                    var needSwitch = switchList.Contains((int)rewardId);
                    return needSwitch ? 0 : 1;
                }
            }
            else if (recruitType == recruittype.recruittype_survivor)
            {
                if (item_type == itemtype.itemtype_survivor)
                {
                    // //判断是否拥有该英雄，如果拥有转成10个英雄碎片
                    var needSwitch = switchList.Contains((int)rewardId);
                    return needSwitch ? 0 : 1;
                }
            }

            return -1;
        }

        private void DisplayResultDialog()
        {
            if (recruitType == recruittype.recruittype_hero)
            {
                RecruitHundredHero();
            }
            else if (recruitType == recruittype.recruittype_survivor)
            {
                RecruitHundredSurvivor();
            }
        }


        //英雄列表排序比较规则
        private int CompareLogic(DataNode a, DataNode b)
        {
            //品质>类型（完整英雄，10碎片）>id
            if (b.quality != a.quality) return b.quality - a.quality;
            if (b.isWholeHero != a.isWholeHero) return b.isWholeHero - a.isWholeHero;
            if (a.sumCount != b.sumCount) return b.sumCount - a.sumCount;
            // if (a.typeFlag != b.typeFlag) return a.typeFlag - b.typeFlag;
            return a.itemID - b.itemID;
        }

        //英雄列表排序比较规则
        private int ComparePropLogic(DataNode a, DataNode b)
        {
            // 道具类型(英雄碎片>装备箱>技能勋章>英雄经验>强化石)>品质>数量>道具id
            if (a.typeFlag != b.typeFlag) return a.typeFlag - b.typeFlag;
            if (a.quality != b.quality) return b.quality - a.quality;
            if (a.sumCount != b.sumCount) return b.sumCount - a.sumCount;
            return a.itemID - b.itemID;
        }

        //单张卡翻转动画
        private void SingleFlipAnim(CardInfo cardInfo, UnityAction callback)
        {
            cardInfo.card.eulerAngles = Vector3.zero;
            cardInfo.card.localScale = Vector3.one;

            float duration = 0.6f;
            cardInfo.card.DOBlendableScaleBy(new Vector3(0.1f, 0.2f, 0), duration / 2).OnComplete(() =>
            {
                cardInfo.card.DOBlendableScaleBy(new Vector3(-0.1f, -0.2f, 0), duration / 2);
            });
            bool hasCheckEffect = false;
            cardInfo.card.DOBlendableLocalRotateBy(new Vector3(0, 180, 0), duration).OnUpdate(() =>
            {
                cardInfo.isPlaying = true;
                var value = cardInfo.card.eulerAngles.y;
                var frontActive = cardInfo.front.activeInHierarchy;
                var backActive = cardInfo.back.activeInHierarchy;
                var nodeActive = cardInfo.node.activeInHierarchy;
                var rotateValue = Mathf.Abs(value);
                if (rotateValue <= 90)
                {
                    if (frontActive)
                    {
                        cardInfo.front.SetActive(false);
                    }

                    if (!backActive)
                    {
                        cardInfo.back.SetActive(true);
                    }

                    if (nodeActive)
                    {
                        cardInfo.node.SetActive(false);
                    }
                }
                else
                {
                    if (!frontActive)
                    {
                        cardInfo.front.SetActive(true);
                    }

                    if (backActive)
                    {
                        cardInfo.back.SetActive(false);
                    }

                    if (!nodeActive)
                    {
                        cardInfo.node.SetActive(true);
                    }

                    if (!hasCheckEffect && rotateValue >= 170)
                    {
                        if (cardInfo.effect.transform.childCount > 0)
                        {
                            DOVirtual.DelayedCall(0.1f, () =>
                            {
                                cardInfo.effect.SetActive(true);
                                cardInfo.effect.transform.GetChild(0).gameObject.SetActive(true);
                            });
                        }

                        hasCheckEffect = true;
                    }
                }
            }).OnComplete(() =>
            {
                cardInfo.isPlaying = false;
                callback?.Invoke();
            });
        }


        //重置卡牌显示效果
        private void ResetCardView(CardInfo cardInfo)
        {
            cardInfo.front.SetActive(false);
            cardInfo.node.SetActive(false);
            cardInfo.back.SetActive(true);
            cardInfo.card.eulerAngles = Vector3.zero;
            cardInfo.obj.transform.localScale = Vector3.one;
        }

        private CardInfo GetSingleCardInfo(Transform root)
        {
            var cardInfo = new CardInfo();
            var card = root.Find("card");
            cardInfo.obj = root.gameObject;
            cardInfo.card = card;
            cardInfo.front = card.Find("front").gameObject;
            cardInfo.back = card.Find("back").gameObject;
            cardInfo.node = card.Find("front/node").gameObject;
            cardInfo.effect = card.Find("front/node/effect").gameObject;
            return cardInfo;
        }

        private void KillSequence(Sequence sequence)
        {
            if (sequence != null && sequence.IsActive())
            {
                sequence.Pause();
                sequence.Kill();
            }
        }

        private void SetPropItemInfo(DataNode dataNode, GameObject obj)
        {
            var itemId = dataNode.itemID;
            var config = GameEntry.LDLTable.GetTableById<item_config>(itemId);
            if (config == null) return;
            var root = obj.transform;
            var bg = root.Find("node/quality").GetComponent<UIImage>();
            var icon = root.Find("node/icon").GetComponent<UIImage>();
            var txtCount = root.Find("node/txt").GetComponent<UIText>();

            bg.SetImage(ToolScriptExtend.GetQualityBg(config.quality));
            icon.SetImage(ToolScriptExtend.GetItemIcon(itemId));
            txtCount.text = ToolScriptExtend.FormatNumberWithUnit(dataNode.sumCount);

            SetParticleSystemSortingOrder(obj, Depth);
        }

        //根据传入数据控制卡牌显示效果
        private void SetCardInfoView(DataNode dataNode, CardInfo cardInfo, bool isShowEffect)
        {
            if (recruitType == recruittype.recruittype_hero)
            {
                ShowHeroCardInfo(dataNode, cardInfo, isShowEffect);
            }
            else if (recruitType == recruittype.recruittype_survivor)
            {
                ShowSurvivorCardInfo(dataNode, cardInfo, isShowEffect);
            }
        }

        //英雄卡牌信息展示
        private void ShowHeroCardInfo(DataNode dataNode, CardInfo cardInfo, bool isShowEffect)
        {
            var rewardId = (itemid)dataNode.itemID;
            var rewardCount = dataNode.sumCount;

            RecruitManager.GetItemData((int)rewardId, (data) =>
            {
                bool needSwitch = false;
                if (dataNode.isWholeHero == 0)
                {
                    if (ToolScriptExtend.GetConfigById<hero_config>((int)rewardId, out var heroData))
                    {
                        rewardId = heroData.piece;
                        rewardCount *= 10;
                        needSwitch = true;
                    }
                }

                var bg = cardInfo.front.GetComponent<UIImage>();
                bg.SetImage(RecruitManager.GetCardQualityBg(data.quality));
                var nodeRoot = cardInfo.node.transform;
                var nameTxt = nodeRoot.Find("name").GetComponent<UIText>();
                var icon = nodeRoot.Find("icon").GetComponent<UIImage>();
                var countTxt = nodeRoot.Find("count").GetComponent<UIText>();
                var border = nodeRoot.Find("border").GetComponent<UIImage>();

                var hero = nodeRoot.Find("hero");
                var icon_1 = nodeRoot.Find("hero/icon_1").GetComponent<UIImage>();
                var attr1 = nodeRoot.Find("attr1").GetComponent<UIImage>();
                var attr2 = nodeRoot.Find("attr2").GetComponent<UIImage>();
                var newFlag = nodeRoot.Find("new").GetComponent<UIImage>();

                border.SetImage(RecruitManager.GetCardQualityBorder(data.quality));
                var isWholeHeroShow = data.item_type == itemtype.itemtype_hero && !needSwitch;
                icon.gameObject.SetActive(!isWholeHeroShow);
                countTxt.gameObject.SetActive(!isWholeHeroShow);
                icon_1.gameObject.SetActive(isWholeHeroShow);
                attr1.gameObject.SetActive(isWholeHeroShow);
                attr2.gameObject.SetActive(isWholeHeroShow);
                // newFlag.gameObject.SetActive(isWholeHeroShow);
                hero.gameObject.SetActive(isWholeHeroShow);

                if (isWholeHeroShow)
                {
                    //特殊处理完整英雄
                    if (ToolScriptExtend.GetConfigById<hero_config>((int)rewardId, out var heroData))
                    {
                        icon_1.SetImage(heroData.hero_head);
                        attr1.SetImage(GameEntry.LogicData.HeroData.GetServicesImgPath(heroData.services));
                        attr2.SetImage(GameEntry.LogicData.HeroData.GetPositionImgPath(heroData.position));
                    }
                }
                else
                {
                    countTxt.text = "x" + ToolScriptExtend.FormatNumberWithUnit(rewardCount);
                    icon.SetImage(ToolScriptExtend.GetItemIcon(rewardId));
                }

                nameTxt.text = ToolScriptExtend.GetLang(data.name);

                if (ColorUtility.TryParseHtmlString(RecruitManager.GetHexByQuality(data.quality), out var color))
                {
                    nameTxt.color = color;
                }

                cardInfo.effect.SetActive(false);
                var effectRoot = cardInfo.effect.transform;
                if (cardInfo.effect.transform.childCount > 0)
                {
                    ToolScriptExtend.ClearAllChild(effectRoot);
                }

                if (data.quality == quality.quality_purple || data.quality == quality.quality_orange)
                {
                    CreateEffect(effectRoot, data.quality, isShowEffect);
                }
            });
        }

        //幸存者卡牌信息展示
        private void ShowSurvivorCardInfo(DataNode dataNode, CardInfo cardInfo, bool isShowEffect)
        {
            var rewardId = (itemid)dataNode.itemID;
            var rewardCount = dataNode.sumCount;

            RecruitManager.GetItemData((int)rewardId, (data) =>
            {
                bool needSwitch = false;

                if (dataNode.isWholeHero == 0)
                {
                    var survivorData = Game.GameEntry.LDLTable.GetTableById<survivor_list>(rewardId);
                    if (survivorData != null)
                    {
                        rewardId = survivorData.peace;
                        rewardCount *= 10;
                        needSwitch = true;
                    }
                }

                var bg = cardInfo.front.GetComponent<UIImage>();
                bg.SetImage(RecruitManager.GetCardQualityBg(data.quality));
                var nodeRoot = cardInfo.node.transform;
                var nameTxt = nodeRoot.Find("name").GetComponent<UIText>();
                var icon = nodeRoot.Find("icon").GetComponent<UIImage>();
                var countTxt = nodeRoot.Find("count").GetComponent<UIText>();
                var border = nodeRoot.Find("border").GetComponent<UIImage>();

                var hero = nodeRoot.Find("hero");
                var icon_1 = nodeRoot.Find("hero/icon_1").GetComponent<UIImage>();
                var newFlag = nodeRoot.Find("new").GetComponent<UIImage>();

                border.SetImage(RecruitManager.GetCardQualityBorder(data.quality));
                var isWholeHeroShow = data.item_type == itemtype.itemtype_survivor && !needSwitch;
                icon.gameObject.SetActive(!isWholeHeroShow);
                countTxt.gameObject.SetActive(!isWholeHeroShow);
                icon_1.gameObject.SetActive(isWholeHeroShow);
                // newFlag.gameObject.SetActive(isWholeHeroShow);
                hero.gameObject.SetActive(isWholeHeroShow);

                if (isWholeHeroShow)
                {
                    var survivorData = Game.GameEntry.LDLTable.GetTableById<survivor_list>(rewardId);
                    if (survivorData != null)
                    {
                        icon_1.SetImage(survivorData.picture);
                        nameTxt.text = ToolScriptExtend.GetLang(survivorData.name);
                    }
                }
                else
                {
                    countTxt.text = "x" + ToolScriptExtend.FormatNumberWithUnit(rewardCount);
                    icon.SetImage(ToolScriptExtend.GetItemIcon(rewardId));
                    nameTxt.text = ToolScriptExtend.GetLang(data.name);
                }

                if (ColorUtility.TryParseHtmlString(RecruitManager.GetHexByQuality(data.quality), out var color))
                {
                    nameTxt.color = color;
                }

                cardInfo.effect.SetActive(false);
                var effectRoot = cardInfo.effect.transform;
                if (cardInfo.effect.transform.childCount > 0)
                {
                    ToolScriptExtend.ClearAllChild(effectRoot);
                }

                if (data.quality == quality.quality_purple || data.quality == quality.quality_orange)
                {
                    CreateEffect(effectRoot, data.quality, isShowEffect);
                }
            });
        }

        //生成闪光特效
        private void CreateEffect(Transform root, quality quality, bool isShow)
        {
            if (quality == quality.quality_purple)
            {
                if (ziseEffect != null)
                {
                    var obj = Instantiate(ziseEffect, root);
                    obj.SetActive(isShow);
                }
            }
            else if (quality == quality.quality_orange)
            {
                if (huangEffect != null)
                {
                    var obj = Instantiate(huangEffect, root);
                    obj.SetActive(isShow);
                }
            }

            SetParticleSystemSortingOrder(root.gameObject, Depth);
        }

        private GameObject GetTemplateObj(bool isNormal)
        {
            if (recruitType == recruittype.recruittype_hero)
            {
                return isNormal ? m_goNormalSingleCard : m_goSingleCard;
            }
            else if (recruitType == recruittype.recruittype_survivor)
            {
                return isNormal ? m_goNormalSurvivorCard : m_goSurvivorCard;
            }

            return null;
        }

        private float CalculateContentHeight(int goodCount, int normalCount, int propCount)
        {
            var contentLayout = m_goContent.GetComponent<VerticalLayoutGroup>();
            //计算m_goMain高度
            var mainLayout = m_goMain.GetComponent<GridLayoutGroup>();
            var sumCardCount = goodCount + normalCount;
            var mainRowCount = GetRowCount(mainLayout.constraintCount, sumCardCount);
            var mainSumHeight = mainLayout.padding.top + mainLayout.padding.bottom +
                                mainRowCount * mainLayout.cellSize.y + (mainRowCount - 1) * mainLayout.spacing.y;

            var mainRect = m_goMain.GetComponent<RectTransform>();
            mainRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, mainSumHeight);

            //计算m_goOther高度
            var otherLayout = m_goOther.GetComponent<GridLayoutGroup>();
            var otherRowCount = GetRowCount(otherLayout.constraintCount, propCount);
            var otherSumHeight = otherLayout.padding.top + otherLayout.padding.bottom +
                                 otherRowCount * otherLayout.cellSize.y + (otherRowCount - 1) * otherLayout.spacing.y;

            var otherRect = m_goOther.GetComponent<RectTransform>();
            otherRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, otherSumHeight);

            var contentSumHeight = contentLayout.padding.top + contentLayout.padding.bottom + contentLayout.spacing +
                                   mainSumHeight + otherSumHeight;

            m_scrollviewList.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, contentSumHeight);
            return contentSumHeight;
        }

        //计算行数
        private int GetRowCount(int configCount, int sumCount)
        {
            var constraintCount = configCount;
            var value1 = sumCount / constraintCount;
            var value2 = sumCount % constraintCount;
            var value3 = value2 > 0 ? 1 : 0;
            return value1 + value3;
        }

        private void MoveContentSectionAnim(float height, float duration)
        {
            var contentRect = m_scrollviewList.content;
            var posY = contentRect.anchoredPosition.y;
            contentRect.DOAnchorPos(new Vector2(0, posY + height), duration);
        }

        private void LoadEffectObj(string path, Action<object> callback = null)
        {
            Game.GameEntry.Resource.LoadAsset(path, typeof(GameObject),
                new GameFramework.Resource.LoadAssetCallbacks(
                    (assetName, asset, duration, userData) => { callback?.Invoke((GameObject)asset); }));
        }

        #endregion
    }
}
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace Game.Hotfix
{
    public class WmpElementDrawer : WmpDrawerBase<WmpElementUiHud>
    {
        private Dictionary<Vector2Int, List<WmpElementUiHud>> m_ShowListDic = new Dictionary<Vector2Int, List<WmpElementUiHud>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();
        
        private WorldMapComponent m_WorldMapComponent;
        public void Init(WorldMapComponent worldMapComponent)
        {
            m_WorldMapComponent = worldMapComponent;
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty += OnGridDataDirty;
        }

        public void UnInit()
        {
            RemoveAll();            
            
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty -= OnGridDataDirty;
            m_WorldMapComponent = null;
        }

        private void OnGridDataDirty(HashSet<Vector2Int> dirtyData)
        {
            foreach (var t in dirtyData)
            {
                Remove(t);
            }
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                WorldMapLOD.Level4, out List<Vector2Int> showListNew);
            
            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
            
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    //monster
                    var hudList = new List<WmpElementUiHud>();
                    var listMonster = gridData.MonsterData;
                    if (listMonster != null)
                    {
                        for (int i = 0; i < listMonster.Count; i++)
                        {
                            GridMonsterData data = listMonster[i];
                            var item = SpawnItem();
                            item.SetData(data.ElementId, data.PosX, data.PosY);
                            hudList.Add(item);
                        }
                    }
                    //forts
                    var listForts = gridData.FortsData;
                    if (listForts != null)
                    {
                        for (int i = 0; i < listForts.Count; i++)
                        {
                            GridFortsData data = listForts[i];
                            var item = SpawnItem();
                            item.SetData(data.ElementId, data.PosX, data.PosY);
                            hudList.Add(item);
                        }
                    }
                    
                    //mine
                   var mList = gridData.MineData;
                   if (mList != null)
                   {
                       for (int i = 0; i < mList.Count; i++)
                       {
                           GridMineData data = mList[i];
                           var item = SpawnItem();
                           item.SetData(data.ElementId, data.PosX, data.PosY);
                           hudList.Add(item);
                       }
                    
                   }
                   m_ShowListDic.Add(grid, hudList);
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var hudList))
                {
                    for (int i = 0; i < hudList.Count; i++)
                    {
                        WmpElementUiHud hud = hudList[i];
                        DespawnItem(hud);
                    }
            
                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private void RemoveAll()
        {
            m_ShowList.Clear();
            foreach (var item in m_ShowListDic)
            {
                foreach (WmpElementUiHud uiHud in item.Value)
                {
                    DespawnItem(uiHud);
                }
            }

            m_ShowListDic.Clear();

        }
        
    }
}
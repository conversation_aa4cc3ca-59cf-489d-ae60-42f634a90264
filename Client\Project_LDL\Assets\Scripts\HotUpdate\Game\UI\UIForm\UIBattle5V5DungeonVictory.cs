using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBattle5V5DungeonVictory : UGuiFormEx
    {
        private BattleFiled m_BattleFiled;
        private Battle5v5Component m_Battle5V5Component;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    GameEntry.LogicData.Battle5v5Data.DungeonFightSettle(param.DungeonId);
                    m_txtCheckPoint.text = "关卡 " + param.DungeonId;
                }
            }
            else if (GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.TradeTruck)
            {
                m_txtCheckPoint.text = string.Empty;
                if (GameEntry.TradeTruckData.TradeVanRobResp != null)
                {
                    RefreshReward(GameEntry.TradeTruckData.TradeVanRobResp.Article.ToList());
                }
            }
            
            m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            m_BattleFiled = m_Battle5V5Component.BattleFiled;

            ColorLog.Pink("ReportId:" + m_BattleFiled.RecordCtrl.Report.ReportId);
            
            RefreshHero();

            m_btnReplay.gameObject.SetActive(GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.Debug);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        private void OnBtnContinueClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        private void OnBtnDetailClick()
        {
            var param = new UIBattle5V5DungeonStatsFormParam();
            param.Report = m_BattleFiled.RecordCtrl.Report;
            param.IsReplay = false;
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonStatsForm, param);
        }

        private void OnBtnReplayClick()
        {
            if (m_BattleFiled.ReplayRecord())
            {
                Close();    
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = "未找到战斗记录！回放失败",
                });
            }
        }

        void HideDefault()
        {
            m_transHeroItem.gameObject.SetActive(false);
        }

        void RefreshReward(List<Article.Article> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                }
                else
                {
                    Transform item = Instantiate(m_transRewardItem, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.75f);
                    });
                }
            }
        }

        void RefreshHero()
        {
            var attacker = m_BattleFiled.RecordCtrl.Report.Attacker;

            foreach (Transform item in m_transContentHero)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < attacker.Heroes.Count; i++)
            {
                var hero = attacker.Heroes[i];
                HeroModule heroModule = new((itemid)hero.Code)
                {
                    level = (int)hero.Level,
                    starLv = (int)hero.StarStage,
                    IsActive = true,
                };

                UIHeroItem item;
                if (i < m_transContentHero.childCount)
                {
                    item = m_transContentHero.GetChild(i).GetComponent<UIHeroItem>();
                }
                else
                {
                    Transform obj = Instantiate(m_transHeroItem, m_transContentHero);
                    item = obj.GetComponent<UIHeroItem>();
                }

                item.Refresh(heroModule);
                item.SetTeamIndex(null);
                item.gameObject.SetActive(true);
            }
        }
    }
}

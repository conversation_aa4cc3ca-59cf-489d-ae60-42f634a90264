using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Fight;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Team;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;
using Action = System.Action;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public partial class UIBattle5v5Choose : UGuiFormEx
    {
        public int SelectType => selectType;
        private int selectType = -1;
        private List<Transform> itemList = new();

        private bool m_IsDebug;
        private UIBattle5v5ChooseDebug m_UIBattle5V5ChooseDebug;
        private EnumBattleSide m_CurOpSide = EnumBattleSide.Left;

        private Dictionary<EnumBattlePos, UIBattle5v5ChooseHUD> m_HudsList;

        private EnumBattle5v5Type m_CurBattleType;
        
        private int m_CurTeamTypeIndex;
        private List<TeamType> m_TeamTypes;
        List<Dictionary<EnumBattlePos, int>> m_TeamData;

        private BattleFiled m_BattleFiled;
        private Battle5v5Component m_Battle5V5Component;

        private bool m_TeamDataDirty = false;
        private bool m_RelationDirty = false;
        
        private Tweener m_RelationTweener;

        private EnumBattleRelation m_CurRelation = EnumBattleRelation.None;
        
        private UIBattle5v5ChooseTeamSwitch m_TeamSwitch;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_TeamSwitch = m_goTeamSwitch.GetComponent<UIBattle5v5ChooseTeamSwitch>();
            
            int childCount = m_transBtnList.childCount;
            var heroData = GameEntry.LogicData.HeroData;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);

                if (i > 0)
                {
                    var icon = trans.Find("bg/icon").GetComponent<Image>();
                    var selecSp = trans.Find("selectBg/selecSp").GetComponent<Image>();
                    icon.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                    selecSp.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                }

                int index = i;
                var rectTrans = trans.GetComponent<RectTransform>();
                // rectTrans.sizeDelta = new Vector2(245, 91);

                var btn = trans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() => { OnSelectBtn(index); });
            }
        }

        private void LateUpdate()
        {
            if (m_RelationDirty)
            {
                m_RelationDirty = false;
                TryResetRelationUI();
            }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            m_TeamDataDirty = false;

            m_CurTeamTypeIndex = 0;
            
            m_CurBattleType = GameEntry.LogicData.Battle5v5Data.CurBattleType;
            m_IsDebug = m_CurBattleType == EnumBattle5v5Type.Debug;

            InitData();
            
            InitDebug();

            InitTouch();

            m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            m_BattleFiled = m_Battle5V5Component.BattleFiled;
            m_BattleFiled.TeamCtrl.ShowGround = true;

            InitHud();

            if (selectType != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }

            RefreshAirportAttr();


            TryResetRelationUI(true);
            
            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroCreate, OnHeroCreate);
            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroDelete, OnHeroDelete);
            
            InitUI();
            
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            m_BattleFiled.TeamCtrl.ShowGround = false;
            m_BattleFiled.RemoveEventListener(BattleFiledEvent.OnHeroCreate, OnHeroCreate);
            m_BattleFiled.RemoveEventListener(BattleFiledEvent.OnHeroDelete, OnHeroDelete);
            
            m_TeamSwitch.OnClose();
            
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            UnInitDebug();
            UnInitHud();
            UnInitTouch();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void InitData()
        {
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.Test);
            }else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.Dungeon);
            }else if (m_CurBattleType == EnumBattle5v5Type.TradeTruck)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.TradeVanAttack1);
                m_TeamTypes.Add(TeamType.TradeVanAttack2);
                m_TeamTypes.Add(TeamType.TradeVanAttack3);
                m_TeamTypes.Add(TeamType.TradeVanAttack4);
            }else if(m_CurBattleType == EnumBattle5v5Type.NoviceTraining)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.ArenaNoviceAttack);
            }else if(m_CurBattleType == EnumBattle5v5Type.PeakRank)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.ArenaPeakAttack);
            }
            
            m_TeamData = new List<Dictionary<EnumBattlePos, int>>();
            for (int i = 0; i < m_TeamTypes.Count; i++)
            {
                var teamDic = GameEntry.LogicData.TeamData.GetTeamDic(m_TeamTypes[i]);
                m_TeamData.Add(teamDic);
            }
        }
        
        private void InitUI()
        {
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                m_txtStageNum.text = "测试战斗";

                m_goTeamSwitch.SetActive(false);
                SwitchTeamIndex(m_CurTeamTypeIndex, true);
            }
            else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    m_txtStageNum.text = ToolScriptExtend.GetLangFormat(70000001, param.DungeonId.ToString());
                }

                m_goTeamSwitch.SetActive(false);
                SwitchTeamIndex(m_CurTeamTypeIndex, true);
            }
            else if (m_CurBattleType == EnumBattle5v5Type.TradeTruck)
            {
                m_goTeamSwitch.SetActive(true);
                m_TeamSwitch.OnOpen(this);
                m_TeamSwitch.SwitchTo(m_CurTeamTypeIndex);
                SwitchTeamIndex(m_CurTeamTypeIndex, true);
            }
            else if (m_CurBattleType == EnumBattle5v5Type.NoviceTraining)
            {
                m_goTeamSwitch.SetActive(false);
                SwitchTeamIndex(m_CurTeamTypeIndex, true);
            }
            else if (m_CurBattleType == EnumBattle5v5Type.PeakRank)
            {
                m_goTeamSwitch.SetActive(false);
                SwitchTeamIndex(m_CurTeamTypeIndex, true);
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);
        }

        public void SwitchTeamIndex(int index,bool force = false)
        {
            if (m_CurTeamTypeIndex != index || force)
            {
                m_CurTeamTypeIndex = index;
                m_BattleFiled.TeamCtrl.RemoveAllBySide(m_CurOpSide);
                
                var heroData = m_TeamData[m_CurTeamTypeIndex];
                foreach (var item in heroData)
                {
                    m_BattleFiled.TeamCtrl.CreateHero(item.Key, item.Value);
                }

                OnUpdateInfo();
            }
        }
        
        private void UpdatePower(EnumBattleSide side)
        {
            if (side == EnumBattleSide.Left)
            {
                double power = 0;
                if (m_CurBattleType == EnumBattle5v5Type.Debug)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }else if (m_CurBattleType == EnumBattle5v5Type.TradeTruck)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if(m_CurBattleType == EnumBattle5v5Type.NoviceTraining)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if(m_CurBattleType == EnumBattle5v5Type.PeakRank)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }

                m_txtLeftBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
            }
            else if (side == EnumBattleSide.Right)
            {
                double power = 0;
                if (m_CurBattleType == EnumBattle5v5Type.Debug)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
                {
                    var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                    if (param != null)
                    {
                        power = param.MonsterGroupTotalPower;
                    }
                } else if (m_CurBattleType == EnumBattle5v5Type.TradeTruck)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if (m_CurBattleType == EnumBattle5v5Type.NoviceTraining)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                } else if (m_CurBattleType == EnumBattle5v5Type.PeakRank)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }

                m_txtRightBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
            }
        }

        private void OnBtnPowerLeftClick()
        {
            m_goPower.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnPowerRightClick()
        {
        }

        private void OnBtnLeftRelationClick()
        {
            var list = m_BattleFiled.TeamCtrl.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            
            var relationItem = m_goRelation.GetComponent<UIRelationItem>();
            relationItem?.SetRelation(list, relation);
            
            m_goRelation.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnRightRelationClick()
        {
        }

        private void OnBtnExitClick()
        {
            TrySyncTeam(() =>
            {
                var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
                if (tCurrentProcedure is Procedure5v5Battle procedure5V5Battle)
                {
                    procedure5V5Battle.GoBackToMain();
                }   
            });
        }

        private void OnBtnBattleClick()
        {
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                TrySyncTeam(null);
                int level = 1;
                int star = 1;
                int hpTimes = 1;
                int.TryParse(m_UIBattle5V5ChooseDebug.inputLevel.text, out level);
                int.TryParse(m_UIBattle5V5ChooseDebug.inputStar.text, out star);
                int.TryParse(m_UIBattle5V5ChooseDebug.inputHpTimes.text, out hpTimes);
                bool ignoreDefender = m_UIBattle5V5ChooseDebug.toggleIgnoreDefender.isOn;

                Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;

                var attacker = m_BattleFiled.TeamCtrl.GetDebugTeam(EnumBattleSide.Left);
                var defender = m_BattleFiled.TeamCtrl.GetDebugTeam(EnumBattleSide.Right);
                battle5v5data.BattleDebugBeginReq(attacker, defender, level, star, hpTimes, ignoreDefender, (battleDebugResp) =>
                {
                    if (battleDebugResp != null && !string.IsNullOrEmpty(battleDebugResp.ReportId))
                    {
                        battle5v5data.DoBattleRecordReadReq(battleDebugResp.ReportId, (battleRecordReadResp) =>
                        {
                            if (battleRecordReadResp != null)
                            {
                                Report message =
                                    Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                if (m_BattleFiled.PlayRecord(message))
                                {
                                    Close();
                                }
                            }
                        });
                    }
                });
            }
            else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var dungeonId = 0;
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    dungeonId = param.DungeonId;
                }

                TrySyncTeam(() =>
                {
                    Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;
                    battle5v5data.DungeonFightBeginReq(dungeonId, (dungeonFightResp) =>
                    {
                        if (dungeonFightResp != null && !string.IsNullOrEmpty(dungeonFightResp.ReportId))
                        {
                            battle5v5data.DoBattleRecordReadReq(dungeonFightResp.ReportId, (battleRecordReadResp) =>
                            {
                                if (battleRecordReadResp != null)
                                {
                                    Report message =
                                        Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                    if (m_BattleFiled.PlayRecord(message))
                                    {
                                        Close();
                                    }
                                }
                            });
                        }
                    });
                });
            }
            else if (m_CurBattleType == EnumBattle5v5Type.TradeTruck)
            {
                TrySyncTeam(() =>
                {
                    // 获取货车掠夺参数
                    var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamTradeTruck>();
                    if (param != null)
                    {
                        // 执行货车掠夺请求
                        PerformTruckRobBattle();
                    }
                    else
                    {
                        Debug.LogError("Battle5v5ParamTradeTruck not found");
                    }
                });
            }
            else if (m_CurBattleType == EnumBattle5v5Type.NoviceTraining)
            {
                TrySyncTeam(() =>
                {
                    // 获取新兵训练参数
                    var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamNoviceTraining>();
                    if (param != null)
                    {
                        // 执行新兵挑战请求
                        PerformNoviceTrainingBattle(param.roleId, param.rank);
                    }
                    else
                    {
                        Debug.LogError("Battle5v5ParamNoviceTraining not found");
                    }
                });
            }
            else if (m_CurBattleType == EnumBattle5v5Type.PeakRank)
            {
               TrySyncTeam(() =>
                {
                    // 获取新兵训练参数
                    var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamPeakRank>();
                    if (param != null)
                    {
                        // 执行巅峰挑战请求
                        PerformPeakRankBattle(param.roleId, param.rank);
                    }
                    else
                    {
                        Debug.LogError("Battle5v5ParamNoviceTraining not found");
                    }
                }); 
            }
           
        }

        private void OnBtnAirSupportClick()
        {
            // m_goAirSupport.SetActive(true);
            // m_btnMask.gameObject.SetActive(true);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUavTip,m_btnAirSupport.gameObject);
        }

        private void OnBtnUnknowClick()
        {
        }

        private void OnBtnMaskClick()
        {
            m_goPower.SetActive(false);
            m_goRelation.SetActive(false);
            m_goAirSupport.SetActive(false);
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
        }

        private void OnBtnMaskAirSupportClick()
        {
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
        }
        //新兵挑战
        private void PerformNoviceTrainingBattle(uint roleId,uint rank)
        {
            GameEntry.LogicData.NoviceTrainingData.Challenge((int)rank, (int)roleId, (resp) => 
            {
                if (resp != null)
                {
                    // 这里可以添加挑战成功后的逻辑，如跳转到战斗场景等
                    //RefreshCompetitionList();
                    //请求战报
                    if (resp != null && !string.IsNullOrEmpty(resp.VideoId))
                    {
                        //播放战斗回放
                        Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;
                        battle5v5data.DoBattleRecordReadReq(resp.VideoId, (battleRecordReadResp) =>
                        {
                            if (battleRecordReadResp != null)
                            {
                                Report message = Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                if (m_BattleFiled.PlayRecord(message))
                                {
                                    // 战斗回放完成
                                    Close();
                                }
                            }
                        });
                    }
                    else
                    {
                        Debug.LogError("未返回战报ID");
                    }
                }
                else
                {
                    Debug.LogError("[ArenaChallenge] 挑战失败");
                }
            });
        }
        //巅峰挑战
        private void PerformPeakRankBattle(ulong roleId, uint rank)
        {
            GameEntry.LogicData.PeakRankData.Challenge(rank, roleId, (resp) =>
            {
                if (resp != null)
                {
                    if (resp != null && !string.IsNullOrEmpty(resp.VideoId))
                    {
                        //播放战斗回放
                        Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;
                        battle5v5data.DoBattleRecordReadReq(resp.VideoId, (battleRecordReadResp) =>
                        {
                            if (battleRecordReadResp != null)
                            {
                                Report message = Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                if (m_BattleFiled.PlayRecord(message))
                                {
                                    // 战斗回放完成
                                    Close();
                                }
                            }
                        });
                    }
                    else
                    {
                        Debug.LogError("未返回战报ID");
                    }
                }
            });
        }       
        /// <summary>
        /// 执行货车掠夺战斗
        /// </summary>
        private void PerformTruckRobBattle()
        {
            // 获取货车掠夺参数
            var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamTradeTruck>();
            if (param?.DefHeros == null || param.DefHeros.Count == 0)
            {
                Debug.LogError("未找到货车掠夺的防守英雄数据");
                return;
            }

            // 获取攻击队伍类型
            var attackTeamType = GetCurrentAttackTeamType();

            // 从参数中获取货车ID（需要从UITradeTruckPlunderForm传递过来）
            long truckId = GetTruckIdFromParam();
            if (truckId == 0)
            {
                Debug.LogError("未找到货车ID");
                return;
            }

            // 请求货车掠夺
            GameEntry.TradeTruckData.RequestTruckRob(truckId, attackTeamType, (result) =>
            {
                if (result != null && !string.IsNullOrEmpty(result.FightReportId))
                {
                    // 掠夺成功，播放战斗回放
                    Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;
                    battle5v5data.DoBattleRecordReadReq(result.FightReportId, (battleRecordReadResp) =>
                    {
                        if (battleRecordReadResp != null)
                        {
                            Report message = Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                            if (m_BattleFiled.PlayRecord(message))
                            {
                                // 战斗回放完成
                                Close();
                            }
                        }
                    });
                }
                else
                {
                    Debug.LogError("货车掠夺失败或未返回战报ID");
                }
            });
        }

        /// <summary>
        /// 获取当前攻击队伍类型
        /// </summary>
        private TeamType GetCurrentAttackTeamType()
        {
            if (m_CurTeamTypeIndex >= 0 && m_CurTeamTypeIndex < m_TeamTypes.Count)
            {
                return m_TeamTypes[m_CurTeamTypeIndex];
            }
            return TeamType.TradeVanAttack1; // 默认返回第一个攻击队伍
        }

        /// <summary>
        /// 从参数中获取货车ID
        /// </summary>
        private long GetTruckIdFromParam()
        {
            var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamTradeTruck>();
            return param?.TruckId ?? 0;
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnSelectBtn(int index)
        {
            if (selectType == index)
                return;

            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                // rectTrans.sizeDelta = new Vector2(260, 91);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            if (selectType > -1)
            {
                trans = m_transBtnList.GetChild(selectType);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    // rectTrans.sizeDelta = new Vector2(245, 91);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }

            selectType = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            List<HeroModule> heroList = GetHeroList();

            Transform rootTrans = m_scrollview.content;
            ToolScriptExtend.RecycleOrCreate(m_transHeroItem.gameObject, rootTrans, heroList.Count);
            for (int i = 0; i < heroList.Count; i++)
            {
                var item = rootTrans.GetChild(i);
                OnUpdateItem(item, heroList, i);
            }
        }

        private List<HeroModule> GetHeroList()
        {
            List<HeroModule> heroList;
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true);
            }
            else
            {
                heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true,
                    (heroModule) => heroModule.IsActive);
            }

            return heroList;
        }

        private void OnUpdateItem(Transform item, List<HeroModule> heroList, int index)
        {
            var heroVo = heroList[index];
            OnUpdateItem(item, heroVo);
        }

        private void OnUpdateItem(Transform item, HeroModule heroVo)
        {
            var heroItem = item.GetComponent<UIHeroItem>();
            heroItem.Refresh(heroVo);

            bool inSelected = IsInBattle((int)heroVo.id, m_CurOpSide);
            heroItem.SetSelected(inSelected);
            if(!inSelected)
                heroItem.SetTeamIndex(GetTeamIndex((int)heroVo.id,m_CurOpSide));
            else
                heroItem.SetTeamIndex(null);

            heroItem.RemoveAllClickListeners();
            heroItem.AddClickListener(() =>
            {
                var teamCtrl = m_BattleFiled.TeamCtrl;
                int heroId = (int)heroVo.id;
                if (teamCtrl.IsInBattle(heroId, m_CurOpSide,out var pos))
                {
                    RemoveHero(pos, heroId, m_CurOpSide);
                    OnUpdateItem(item, heroVo);
                }
                else
                {
                    var emptyPos = teamCtrl.GetEmptyPosition(m_CurOpSide);
                    if (emptyPos != null)
                    {
                        var heroCurTeam = GetTeamIndex(heroId,m_CurOpSide);
                        if (heroCurTeam != null)
                        {
                            var heroModule = GameEntry.LogicData.HeroData.GetHeroModule((Config.itemid)heroId);
                            
                            GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                            {
                                Title = ToolScriptExtend.GetLang(1100147),
                                Content = ToolScriptExtend.GetLangFormat(1326,heroModule.Name,(heroCurTeam+1).ToString()),
                                ConfirmText = ToolScriptExtend.GetLang(1100144),
                                CancelText = ToolScriptExtend.GetLang(1100143),
                                OnClickConfirm = (data) =>
                                {
                                    //将其从别的队伍中移除
                                    var tempTeamDic = m_TeamData[heroCurTeam.Value];
                                    foreach (var item in tempTeamDic)
                                    {
                                        if (item.Value == heroId)
                                        {
                                            tempTeamDic.Remove(item.Key);
                                            break;
                                        }
                                    }

                                    CreateHero(emptyPos.Value, heroId, m_CurOpSide);
                                    OnUpdateItem(item, heroVo);
                                },
                            });
                        }
                        else
                        {
                            CreateHero(emptyPos.Value, heroId, m_CurOpSide);
                            OnUpdateItem(item, heroVo);    
                        }
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(1327),
                        });
                    }
                }
            });
        }

        private int? GetTeamIndex(int heroId, EnumBattleSide side)
        {
            if (side == EnumBattleSide.Right)
                return null;
            
            for (int i = 0; i < m_TeamData.Count; i++)
            {
                var teamDic = m_TeamData[i];
                if (teamDic.ContainsValue(heroId))
                {
                    return i;
                }
            }
            return null;
        }
        
        private bool IsInBattle(int heroId, EnumBattleSide side)
        {
            if (side == EnumBattleSide.Left)
            {
                var heroData = m_TeamData[m_CurTeamTypeIndex];
                if (heroData.ContainsValue(heroId))
                {
                    return true;
                }
                return false;
            }
            return m_BattleFiled.TeamCtrl.IsInBattle(heroId, side,out _);
        }

        private void RefreshAirportAttr()
        {
            foreach (Transform item in m_goAirSupportAttr.transform)
            {
                UIButton btn = item.GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    btn.AnchorUIToButton(m_goPopupAirSupportAttr.transform, new Vector2(0, 50f));
                    m_goPopupAirSupportAttr.SetActive(true);
                    m_btnMaskAirSupport.gameObject.SetActive(true);
                });
            }
        }

        private void TrySyncTeam(Action callBack)
        {
            if (!m_TeamDataDirty)
            {
                callBack?.Invoke();
                return;
            }

            m_TeamDataDirty = false;
        
            List<FormationTeam> formationTeams = new List<FormationTeam>(); 
            for (int i = 0; i < m_TeamTypes.Count; i++)
            {
                FormationTeam formationTeam = new FormationTeam();
                formationTeam.TeamType = m_TeamTypes[i];
                var teamDic = m_TeamData[i];
                if (teamDic != null && teamDic.Count > 0)
                {
                    
                    foreach (var item in teamDic)
                    {
                        FormationHero formationHero = new FormationHero();
                        formationHero.Pos = (int)item.Key;
                        formationHero.HeroId = (itemid)item.Value;
                        
                        formationTeam.Heroes.Add(formationHero);
                    }

                }
                formationTeams.Add(formationTeam);
            }

            if (formationTeams.Count > 0)
            {
                GameEntry.LogicData.TeamData.TeamModify(formationTeams,callBack);
            }else{
                callBack?.Invoke();
            }
        }

        private void CreateHero(EnumBattlePos pos,int heroId,EnumBattleSide side)
        {
            //英雄数据移动
            if (side == EnumBattleSide.Left)
            {
                var teamDic = m_TeamData[m_CurTeamTypeIndex];
                teamDic[pos] = heroId;
            }
            
            m_BattleFiled.TeamCtrl.CreateHero(pos, heroId);
            
            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        private void RemoveHero(EnumBattlePos pos,int heroId,EnumBattleSide side)
        {
            //英雄数据移动
            if (side == EnumBattleSide.Left)
            {
                var teamDic = m_TeamData[m_CurTeamTypeIndex];
                teamDic.Remove(pos);
            }

            m_BattleFiled.TeamCtrl.RemoveHero(heroId, side);
            
            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }
        
        private void RemoveHero(BattleHero hero,EnumBattleSide side)
        {
            RemoveHero(hero.BattlePos, hero.HeroId, side);
        }
        

        #region touch 相关逻辑

        [CanBeNull] private BattleHero m_OpDragBattleHero;
        private EnumBattlePos? m_OpCurPos; //当前手指所在区域

        private void InitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped += OnTapped;
            controller.dragged += OnDragged;
            controller.released += OnReleased;
            controller.pressed += OnPressed;
        }

        private void UnInitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped -= OnTapped;
            controller.dragged -= OnDragged;
            controller.released -= OnReleased;
            controller.pressed -= OnPressed;
        }

        private void OnTapped(PointerActionInfo pointer)
        {
            if (pointer.startedOverUI) return;
            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);
            if (pos != null)
            {
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    RemoveHero(hero, m_CurOpSide);
                    OnUpdateInfo();
                }
            }
        }

        private void OnDragged(PointerActionInfo pointer)
        {
            if (m_OpDragBattleHero == null)
                return;

            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            m_OpDragBattleHero.SetPosition(worldPos);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);
            if (pos == null)
            {
                //没有在任何区域内部
                MoveBackCache();
            }
            else
            {
                //移动到了新区域
                if (m_OpDragBattleHero.BattlePos == pos.Value)
                {
                    //新区域是我自己
                    MoveBackCache();
                }
                else
                {
                    if (m_OpCurPos == pos.Value)
                    {
                        //已经互换 什么也不做
                    }
                    else
                    {
                        MoveBackCache();
                        MoveAndCache(pos.Value);
                    }
                }
            }
        }

        private void OnReleased(PointerActionInfo pointer)
        {
            if (m_OpCurPos != null && m_OpDragBattleHero != null)
            {
                var teamCtrl = m_BattleFiled.TeamCtrl;
                int heroIdA = m_OpDragBattleHero.HeroId;
                EnumBattlePos posA = m_OpDragBattleHero.BattlePos;
                int? heroIdB = null;
                EnumBattlePos posB = m_OpCurPos.Value;

                //删除原有英雄
                if (m_OpDragBattleHero != null)
                    RemoveHero(m_OpDragBattleHero, m_CurOpSide);
                //删除目标位置英雄
                var heroTarget = teamCtrl.GetBattleHero(m_OpCurPos.Value);
                if (heroTarget != null)
                {
                    heroIdB = heroTarget.HeroId;
                    RemoveHero(heroTarget, m_CurOpSide);
                }

                //创建新英雄
                CreateHero(posB, heroIdA, m_CurOpSide);
                if (heroIdB != null)
                    CreateHero(posA, heroIdB.Value, m_CurOpSide);

                m_OpCurPos = null;
                m_OpDragBattleHero = null;
            }
            else
            {
                if (m_OpDragBattleHero != null)
                    m_OpDragBattleHero.MoveBack();
                m_OpDragBattleHero = null;

                MoveBackCache();
            }
        }

        private void OnPressed(PointerActionInfo pointer)
        {
            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);

            if (pos != null)
            {
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_OpDragBattleHero = hero;
                }
            }
        }

        private void MoveBackCache()
        {
            if (m_OpCurPos != null)
            {
                //将之前的英雄归位
                var tempHero = m_BattleFiled.TeamCtrl.GetBattleHero(m_OpCurPos.Value);
                if (tempHero != null)
                    tempHero.MoveBack();
                m_OpCurPos = null;
            }
        }

        private void MoveAndCache(EnumBattlePos pos)
        {
            if (m_OpDragBattleHero == null)
                return;

            var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos);
            if (hero != null)
            {
                hero.MoveTo(m_OpDragBattleHero.BattlePos);
            }

            m_OpCurPos = pos;
        }

        #endregion

        #region 战斗单位头部Hud

        public void InitHud()
        {
            m_HudsList = new Dictionary<EnumBattlePos, UIBattle5v5ChooseHUD>();

            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                var hud = CreateHud(pos);
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos);
                if (hero != null)
                    hud.Show();
                else
                    hud.Hide();
            }
        }

        private void OnHeroDelete(object obj)
        {
            if (obj is EnumBattlePos pos)
            {
                if (m_HudsList.TryGetValue(pos, out UIBattle5v5ChooseHUD choose))
                {
                    choose.Hide();
                }
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);

            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        private void OnHeroCreate(object obj)
        {
            if (obj is EnumBattlePos pos)
            {
                if (m_HudsList.TryGetValue(pos, out UIBattle5v5ChooseHUD choose))
                {
                    choose.Show();
                }
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);

            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        public void UnInitHud()
        {
            foreach (var item in m_HudsList)
            {
                Destroy(item.Value.gameObject);
            }

            m_HudsList.Clear();
            m_HudsList = null;
        }

        private UIBattle5v5ChooseHUD CreateHud(EnumBattlePos pos)
        {
            var newItem = Instantiate(m_transHudItem);
            newItem.transform.parent = m_transHudParent;
            newItem.transform.localPosition = Vector3.one;
            newItem.transform.localScale = Vector3.one;

            var hud = newItem.gameObject.GetOrAddComponent<UIBattle5v5ChooseHUD>();
            hud.Init(m_BattleFiled, pos);
            m_HudsList.Add(pos, hud);
            return hud;
        }

        #endregion

        #region debug 相关

        private void InitDebug()
        {
            m_UIBattle5V5ChooseDebug = m_transDebug.GetComponent<UIBattle5v5ChooseDebug>();
            m_transDebug.gameObject.SetActive(m_IsDebug);
            if (!m_IsDebug)
                return;
            m_UIBattle5V5ChooseDebug.toggle.onValueChanged.AddListener(OnDebugToggleValueChange);
        }

        private void OnDebugToggleValueChange(bool value)
        {
            if (value)
                m_CurOpSide = EnumBattleSide.Right;
            else
                m_CurOpSide = EnumBattleSide.Left;
            OnUpdateInfo();
        }

        private void UnInitDebug()
        {
            if (!m_IsDebug)
                return;
            m_UIBattle5V5ChooseDebug.toggle.onValueChanged.RemoveListener(OnDebugToggleValueChange);
        }

        #endregion

        #region 羁绊相关

        private void TryResetRelationUI(bool force = false)
        {
            var list = m_BattleFiled.TeamCtrl.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            if (m_CurRelation!=relation || force)
            {
                m_CurRelation = relation;
                SetRelationIcon();
            }
        }

        private void SetRelationIcon()
        {
            if(m_RelationTweener!=null && m_RelationTweener.IsPlaying())
                m_RelationTweener.Kill();
            m_btnLeftRelation.transform.localScale = Vector3.one;
            m_RelationTweener = m_btnLeftRelation.transform.DOPunchScale(Vector3.one * 0.5f, 0.3f);
            
            if (m_CurRelation == EnumBattleRelation.None)
            {
                m_imgLeftRelationA.gameObject.SetActive(false);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            } 
            else if (m_CurRelation == EnumBattleRelation.Relation3_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
            else if (m_CurRelation == EnumBattleRelation.Relation3_2)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(true);
                m_imgLeftRelationC.SetImageGray(true);
            }else if (m_CurRelation == EnumBattleRelation.Relation4_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }else if (m_CurRelation == EnumBattleRelation.Relation5_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
        }
        
        #endregion
    }
}
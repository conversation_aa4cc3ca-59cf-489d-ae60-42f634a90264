using System.Collections.Generic;
using System.Linq;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Serialization;

namespace Game.Hotfix
{
    public class WmpMyTownDrawer : WmpDrawerBase<WmpMyTownUiHud>
    {
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        private WmpMyTownUiHud m_Hud;
        private Vector2Int? m_MyTownPos;

        private ulong? m_PlayerId = null;

        public void Init(WorldMapComponent worldMapComponent)
        {
            m_PlayerId = GameEntry.LogicData.UserData.uuid;

            m_WorldMapComponent = worldMapComponent;
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            GameEntry.Event?.Subscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);
        }

        private void OnOpModeChangeEvent(object sender, GameEventArgs e)
        {
            if(e is OnOpModeChangeEventArgs args)
            {
                if (args.newOp == MapOpType.TOWN_MOVE_MODE)
                {
                    OnCameraMove();
                }
                else
                {
                    RemoveAll();
                    m_ShowList.Clear();
                    OnCameraMove();
                }
            }
        }

        public void UnInit()
        {
            RemoveAll();

            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent = null;
            GameEntry.Event?.Unsubscribe(OnOpModeChangeEventArgs.EventId, OnOpModeChangeEvent);
        }

        private void OnCameraMove()
        {
            if (m_WorldMapComponent.GetMapOpType() == MapOpType.TOWN_MOVE_MODE)
            {
                if (CameraComponent.ZoomLevel >= WorldMapLOD.Level1)
                {
                    //显示
                    if (m_Hud == null)
                    {
                        var data = GameEntry.LogicData.WorldMapData.GetMyTownData();
                        CreateHud(data, new Vector2Int((int)data.PosX, (int)data.PosY));
                    }
                }
                else
                {
                    //隐藏
                    if (m_Hud != null)
                        RemoveAll();
                }
            }
            else
            {
                var curLevel = CameraComponent.ZoomLevel;
                if (curLevel >= WorldMapLOD.Level5) return;

                m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                    WorldMapLOD.Level4, out List<Vector2Int> showListNew);

                List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
                List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

                foreach (var t in remove)
                {
                    Remove(t);
                }

                foreach (var t in add)
                {
                    Add(t);
                }
            }
            
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                var list = gridData.TownData;
                if (list != null)
                {
                    for (int i = 0; i < list.Count; i++)
                    {
                        GridTownData data = list[i];
                        if (data.RoleId == m_PlayerId)
                        {
                            CreateHud(data,grid);
                        }
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_MyTownPos == grid)
                {
                    if (m_Hud != null)
                    {
                        DespawnItem(m_Hud);
                    }

                    m_Hud = null;
                    m_MyTownPos = null;
                }
            }
        }

        private void RemoveAll()
        {
            if (m_Hud != null)
            {
                DespawnItem(m_Hud);
            }

            m_Hud = null;
            m_MyTownPos = null;

            m_ShowList.Clear();
        }

        private WmpMyTownUiHud CreateHud(GridTownData data,Vector2Int grid)
        {
            var item = SpawnItem();
            item.SetData(data);
            m_Hud = item;
            m_MyTownPos = grid;
            return item;
        }
    }
}
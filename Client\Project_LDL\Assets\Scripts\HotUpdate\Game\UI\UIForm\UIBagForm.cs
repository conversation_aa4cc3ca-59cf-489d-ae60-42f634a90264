
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Game.Hotfix.Config;
using System;
using GameFramework.Event;
namespace Game.Hotfix
{
    public partial class UIBagForm : UGuiFormEx
    { 
        //List<ItemModule> bagItemList = new List<ItemModule>();
        List<UIToggle> toggList;
        public int curItemPos = 0;
        public int itemId;
        public int lineItemCount = 4;
        public int curType = 1;//ItemType.Special; 
        public UIItemModule curItem;
        public UIItemModule lastItem;
        //public Dictionary <int,List<ItemModule>> UIItemModuleDic = new Dictionary<int, List<ItemModule>>();
        public List<ItemModule> curShowModuleList;
        private List<item_config> item_Configs = new List<item_config>();
        public int useCount = 1;
        int selectedEquipmentID = 0;
        int selectedIndex = -1;
        int curTableViewIndex = 0;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
            toggList = new List<UIToggle>{m_togSpecial,m_togResource,m_togTimeAdd,m_togHero,m_togEquiment,m_togGift};
            foreach (var item in toggList)
            {
            //   if(item.isOn)
            //   {
            //     curType = item.toggleType;
            //   }
                item.onValueChanged.AddListener((isOn)=>{
                    if (isOn)
                    {
                        curType = item.toggleType;
                        curItem?.IsSelect(false);
                        lastItem?.IsSelect(false);
                        lastItem = null;
                        curItem = null;
                        ClearPanel();
                        curShowModuleList?.Clear();
                        clickTab();
                        m_TableViewV.GetItemCount = () => { return GetTabCount();};
                        if (m_TableViewV.itemPrototype == null)
                        {
                            m_TableViewV.InitTableViewByIndex(0, 12);
                        }
                        else{
                            m_TableViewV.ReloadData();
                        }
                    }
                });
                m_scrollviewDes.onValueChanged.AddListener((pos)=>{
                    RefreshArrow();
                });
            }

            GameObject obj = transform.Find("bagLine").gameObject;
            m_TableViewV.GetItemCount = () => { return GetTabCount(); };
            m_TableViewV.GetItemGo = () =>
            {
                return obj;
            };
               m_TableViewV.UpdateItemCell = UpdateBagLine;
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0, 12);
            }
            else{
                m_TableViewV.ReloadData();
            }
            InitConfig();
            clickTab();      
        }
        public void RefreshArrow(){
            //Debug.LogError(m_scrollviewDes.verticalNormalizedPosition);
            RectTransform rect = m_txtItemDes.transform.GetComponent<RectTransform>();
            if(rect.rect.height > 200 && m_scrollviewDes.verticalNormalizedPosition < 0.1f )
            {
                m_goHaveMoreDes.SetActive(false);
            }else{
                if(rect.rect.height < 200){
                     m_goHaveMoreDes.SetActive(false);
                }else
                {
                     m_goHaveMoreDes.SetActive(true);
                }       
            }
        }
        public void clickTab(){
            foreach (var item in toggList)
            {
                if(curType==item.toggleType)
                {
                    item.isOn = true;
                    break;
                }
            }
            m_goHaveItem.SetActive(GetTabCount()>0);
            m_goNoItem.SetActive(GetTabCount()<=0);
        }
        public int GetTabCount(){
            float line = (float)GameEntry.LogicData.BagData.GetDataByTab(curType).Count/4;
            return (int)Math.Ceiling(line);
            //return 0;
        }

        public void UpdateBagLine(int index, GameObject obj)
        {
            curTableViewIndex = index;
            var BagData = GameEntry.LogicData.BagData;
            curShowModuleList = BagData.GetDataByTab(curType);
           for (int i = 0; i < 4 ; i++)
           {    
                int data_index = index*4+i;
                if (obj.transform.childCount <= i && data_index <= curShowModuleList.Count-1)
                {
                    BagManager.CreatItem(obj.transform,curShowModuleList[data_index].ItemId,curShowModuleList[data_index].Count,(UIItemModule)=>{
                        
                        UIItemModule.itemModule = curShowModuleList[data_index];
                        UIItemModule.RefreshInBag();

                        if (selectedIndex >= 0)
                        {
                            if (selectedIndex == data_index)
                            {
                                lastItem = curItem;
                                SetCurItem(UIItemModule);
                                lastItem?.IsSelect(false);
                                curItem?.IsSelect(true);
                            }
                            else
                            {
                                UIItemModule?.IsSelect(false);
                            }
                        }

                        UIItemModule.SetClick(()=>{
                                lastItem = curItem;
                                // Debug.LogError(curShowModuleList.Count);
                                // Debug.LogError(data_index);
                                UIItemModule.itemModule = curShowModuleList[data_index];         
                                SetCurItem(UIItemModule);                        
                                lastItem?.IsSelect(false); 
                                curItem?.IsSelect(true);
                                selectedIndex = data_index;
                                //curItem.OpenTxtTips();
                                RefreshPanel();               
                            });                            
                            if(curItem == null)
                            {
                                SetCurItem(UIItemModule);
                                lastItem = curItem;                        
                                curItem?.IsSelect(true);
                                selectedIndex = data_index;
                                RefreshPanel();
                            }
                        //UIItemModule.itemObj.SetActive(true);
                    });
                }
                else
                {
                    
                    if(i+1 > obj.transform.childCount){
                        return;
                    }
                    UIItemModule item;
                    item = obj.transform.GetChild(i).GetComponent<UIItemModule>();
                    if( curShowModuleList.Count > 0 && data_index <= curShowModuleList.Count-1){
                        item.itemModule = curShowModuleList[data_index];  
                        item.SetData(curShowModuleList[data_index].ItemId,curShowModuleList[data_index].Count);
                        item.DisplayInfo();
                        item.itemObj.SetActive(true);
                        item.RefreshInBag();
                        if(curItem == null)
                        {
                            SetCurItem(item);
                            curItem?.IsSelect(true);
                            selectedIndex = data_index;
                            RefreshPanel();
                        }

                        if (selectedIndex >= 0)
                        {
                            if (selectedIndex == data_index)
                            {
                                lastItem = curItem;
                                SetCurItem(item);
                                lastItem?.IsSelect(false);
                                curItem?.IsSelect(true);
                            }
                            else
                            {
                                item?.IsSelect(false);
                            }
                        }

                        item.SetClick(()=>{
                            lastItem = curItem;
                            // Debug.LogError(curShowModuleList.Count);
                            // Debug.LogError(data_index);
                            item.itemModule = curShowModuleList[data_index];         
                            SetCurItem(item);                        
                            lastItem?.IsSelect(false); 
                            curItem?.IsSelect(true);
                            selectedIndex = data_index;
                            RefreshPanel();               
                        });         
                    }
                    else
                    {
                        item.itemObj.SetActive(false);
                    }
                    
                    //itemModuleDic[index][i] = item;
                }                                                                                                                                  
            }
           //slider                   
        }
        public void OnRefreshTopRes(){
            var BagData = GameEntry.LogicData.BagData;
            m_txtFood.text = ToolScriptExtend.FormatNumberWithUnit(BagData.GetAmountById(itemid.itemid_2)).ToString();
            m_txtIO.text = ToolScriptExtend.FormatNumberWithUnit(BagData.GetAmountById(itemid.itemid_3)).ToString();
            m_txtCoin.text = ToolScriptExtend.FormatNumberWithUnit(BagData.GetAmountById(itemid.itemid_4)).ToString();
        }
        public void SetCurItem(UIItemModule item){
            curItem = item;
            SliderChange(m_slider.value);
        }
        public void SliderChange(float a){
            if (curItem.itemModule.Count == 1)
            {
                m_slider.interactable = false;
                return;
            }
            else
            {
                m_slider.interactable = true;
            }
            if (a < 1 / curItem.itemModule.Count)
            {
                return;
            }
            useCount = (int)Math.Floor(curItem.itemModule.Count*m_slider.value+0.5);
            useCount = Math.Max(1,useCount);
            m_slider.value = (float)useCount/curItem.itemModule.Count;      
            m_txtUseNum.text = useCount.ToString();
            m_txtUseGet.text = curItem.itemModule.ITM_GetUseTotalNum(useCount);
            m_btnAdd.SetButtonGray(useCount>=curItem.itemModule.Count);
            m_btnReduce.SetButtonGray(useCount<=1);
        }
        public void ClearPanel(){
            m_txtItemName.text = "";
            m_txtItemDes.text = "";
            m_goSlider.SetActive(false);
            m_btnDetail.gameObject.SetActive(false);
            m_btnUse.gameObject.SetActive(false);
            m_transBasic.gameObject.SetActive(false);
            m_txtPower.gameObject.SetActive(false);
            selectedEquipmentID = 0;
            selectedIndex = -1;
            curTableViewIndex = 0;
        }
        public void RefreshPanel()
        {
            if(curItem?.itemModule.Count <=0){
                curItem = null;
            }
            if(curItem == null){
                ClearPanel();
                return;
            }
            RefreshArrow();
            item_config item_Configs = curItem.itemModule.GetItemConfig();
            //if(item_config.IsUse)
            //if item_Configs.IsUse
            m_txtItemName.text = ToolScriptExtend.GetLang(item_Configs.name);
            m_txtItemDes.text = curItem.itemModule.GetItemDes();

            m_txtItemDes.gameObject.SetActive(true);
            m_transBasic.gameObject.SetActive(false);
            m_txtPower.gameObject.SetActive(false);

            //m_btnUse.onClick.RemoveAllListeners();
            m_btnUse.gameObject.SetActive(item_Configs.is_use != isuse.isuse_none);
            if(item_Configs.is_use == isuse.isuse_none)
            {

            }else if (item_Configs.is_use == isuse.isuse_common)
            {
                m_txtBtn.text = ToolScriptExtend.GetLang(1100001);
            }else if (item_Configs.is_use == isuse.isuse_strengthen)
            {
                RefreshEquipment();
            }else if (item_Configs.is_use == isuse.isuse_customize)
            {
                m_txtBtn.text = ToolScriptExtend.GetLang(711376);
            }

            m_btnDetail.gameObject.SetActive(item_Configs.i_button != ibutton.ibutton_none);
            m_btnDetail.onClick.RemoveAllListeners();
            if(item_Configs.i_button == ibutton.ibutton_probability){
                m_btnDetail.onClick.AddListener(() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIItemBoxProForm,curItem.itemModule.ItemId);                  
                    //Debug.Log("弹出概率公示弹窗  ");
                });
            }else if(item_Configs.i_button == ibutton.ibutton_survivor)
            {
                 m_btnDetail.onClick.AddListener(() =>
                {
                     Debug.Log("弹出幸存者预览弹窗   ");
                });
            }

            m_goSlider.SetActive(item_Configs.max_use > 1);
                // case IsUse.IsUseNone
                // case IsUse.IsUseCommon
                // case IsUse.IsUseStrengthen
                // case IsUse.IsUseCustomize

            if(item_Configs.max_use > 1){
                RefreshSlider();
            }
            m_slider.onValueChanged.RemoveAllListeners();
            m_slider.onValueChanged.AddListener(SliderChange);
            OnRefreshTopRes();

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_txtItemDes.transform.GetComponent<RectTransform>());
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_goDesContent.transform.GetComponent<RectTransform>());
        }

        public void RefreshSlider(){
            item_config item_Configs = curItem.itemModule.GetItemConfig();
            if (item_Configs.slider_state ==sliderstate.sliderstate_max){
                useCount = (int)curItem.itemModule.Count;
            }else{
                useCount = 1;
            }
            if(curItem.itemModule.Count>0){
                m_slider.value = useCount/curItem.itemModule.Count;
                m_txtUseNum.text = useCount.ToString();
                m_txtUseGet.text = curItem.itemModule.ITM_GetUseTotalNum(useCount);
            }
        }
        public void InitConfig()
        {
            item_Configs = Game.GameEntry.LDLTable.GetTable<item_config>();
        }

        public void GetConfig(int itemId)
        {

        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            //curItem = null;
            // if(curItem == null)
            // {
            //     SetCurItem(UIItemModule);
            //     lastItem = curItem;                        
            //     curItem?.IsSelect(true);
            //     RefreshPanel();
            // }
            foreach (var item in toggList)
            {
                item.isOn = true;
                break;
            }
            GameEntry.Event.Subscribe(BagChangeEventArgs.EventId, OnBagChangeUpdate);
            GameEntry.Event.Subscribe(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch);
            OnRefreshTopRes();
        }

        public void OnBagChangeUpdate(object sender, GameEventArgs e)
        {
            UpdateEquipmentSelectedIndex();
            m_TableViewV.ReloadData();
            //OnRefreshTopRes();
            RefreshPanel();
           // throw new NotImplementedException();
        }


        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            //curItem?.IsSelect(false);
            //lastItem?.IsSelect(false);
            //curShowModuleList?.Clear();
            GameEntry.Event.Unsubscribe(BagChangeEventArgs.EventId, OnBagChangeUpdate);
            GameEntry.Event.Unsubscribe(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch);
            GameEntry.EquipmentData.InitSort = false;
            //UIItemModuleDic.Clear();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnEmailClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIEmailForm);
        }
        
        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnUseClick()
        {
            item_config item_Configs = curItem.itemModule.GetItemConfig();
            if(item_Configs.is_use == isuse.isuse_none)
            {

            }else if (item_Configs.is_use == isuse.isuse_common)
            {
                //m_txtBtn.text = "使用";
                if(item_Configs.item_subtype== itemsubtype.itemsubtype_choosechest)
                {
                    curItem.itemModule.SetChooseCount(useCount);
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIChooseBoxForm,curItem.itemModule); 
                }
                else
                {                
                    List<ItemModule> list = new List<ItemModule>();
                    ItemModule itemModule = new ItemModule();
                    itemModule = curItem.itemModule;
                    itemModule.Count = useCount;
                    list.Add(itemModule);
                    var BagData = GameEntry.LogicData.BagData;
                    BagData.OnReqItemUse(list);
                    Debug.Log("OnBtnUseClick"); 
                }
            }else if (item_Configs.is_use == isuse.isuse_strengthen)
            {
                if (0 <= selectedIndex && selectedIndex < curShowModuleList.Count)
                {
                    if (curShowModuleList[selectedIndex] is EquipmentModule equipment)
                    {
                        if (equipment.CanPromote)
                        {
                            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(2401);
                            if (buildingModule == null || buildingModule.LEVEL < 20)
                            {
                                Close();
                                // 跳转到装备工厂，指引升级
                                if (buildingModule == null) return;
                                GameEntry.LogicData.BuildingData.FindBuildingAndOpenMenu(buildingModule.GetBuildingType());
                            }
                            else
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIEquipmentPromoteForm, new EquipmentParams()
                                {
                                    ID = equipment.id,
                                    HeroID = equipment.target_id,
                                    Part = equipment.Part,
                                    InBag = true
                                });
                            }
                        }
                        else
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIEquipmentDetailForm, new EquipmentParams()
                            {
                                ID = equipment.id,
                                HeroID = equipment.target_id,
                                Part = equipment.Part,
                                InBag = true
                            });
                        }
                    }
                }
            }else if (item_Configs.is_use == isuse.isuse_customize)
            {
                m_txtBtn.text = ToolScriptExtend.GetLang(1044);
            }

        }

        private void OnBtnCountClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBagStatistics);
        }

        private void OnBtnDetailClick(){
            Debug.Log("OnBtnDetailClick");
        }
        private void OnBtnFoodClick(){
            ItemModule itemModule = new ItemModule(itemid.itemid_2);
            GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(0));
        }
        private void OnBtnIOClick(){
            //GameEntry.UI.OpenUIForm(EnumUIForm.UITaskForm);
            ItemModule itemModule = new ItemModule(itemid.itemid_3);
            GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(0));
        }
        private void OnBtnCoinClick(){
            ItemModule itemModule = new ItemModule(itemid.itemid_4);
            GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(0));
        }
        private void OnBtnAddClick(){
            useCount++;
            useCount = Math.Min(useCount,(int)curItem.itemModule.Count);
            m_txtUseNum.text = useCount.ToString();
            m_txtUseGet.text = curItem.itemModule.ITM_GetUseTotalNum(useCount);
            m_slider.value = (float)useCount/curItem.itemModule.Count;
        }
        private void OnBtnReduceClick(){
            useCount--;
            useCount = Math.Max(1,useCount);
            m_txtUseNum.text = useCount.ToString();
            m_txtUseGet.text = curItem.itemModule.ITM_GetUseTotalNum(useCount);
            m_slider.value = (float)useCount/curItem.itemModule.Count;
        }

        void RefreshEquipment()
        {
            if (curItem != null)
            {
                if (curItem.itemModule is EquipmentModule equipment)
                {
                    if (equipment.CanPromote)
                    {
                        BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(2401);
                        if (buildingModule == null || buildingModule.LEVEL < 20)
                        {
                            m_txtBtn.text = ToolScriptExtend.GetLang(1046);
                        }
                        else
                        {
                            m_txtBtn.text = ToolScriptExtend.GetLang(1047);
                        }
                    }
                    else
                    {
                        m_txtBtn.text = ToolScriptExtend.GetLang(1044);
                    }

                    if (equipment.target_id != 0)
                    {
                        HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule(equipment.target_id);
                        OnUpdateItem(m_transHeroItem, heroModule);
                        m_goHeroItem.SetActive(true);
                    }
                    else
                    {
                        m_goHeroItem.SetActive(false);
                    }

                    m_txtItemDes.gameObject.SetActive(false);
                    m_transBasic.gameObject.SetActive(true);
                    m_txtPower.gameObject.SetActive(true);

                    m_txtPower.text = equipment.Power.ToString();

                    foreach (Transform child in m_transBasic)
                    {
                        child.gameObject.SetActive(false);
                    }

                    List<equip_attributes> attributes = GameEntry.EquipmentData.GetBasicProperty(equipment.code, equipment.equipment_level);
                    for (int i = 0; i < attributes.Count; i++)
                    {
                        if (i >= m_transBasic.childCount) break;

                        equip_attributes attr = attributes[i];

                        GameObject item = m_transBasic.GetChild(i).gameObject;
                        item.SetActive(true);

                        UIText txtName = item.transform.Find("txtName").GetComponent<UIText>();
                        txtName.text = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);
                        UIText txtValue = item.transform.Find("txtValue").GetComponent<UIText>();
                        txtValue.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                    
                        float width = txtName.preferredWidth;
                        if (width >= 240)
                        {
                            width = 240;
                        }
                        txtName.GetComponent<LayoutElement>().preferredWidth = width;
                    }
                }
            }
        }

        void OnUpdateItem(Transform item, HeroModule heroModule)
        {
            var btn = item.GetComponent<Button>();
            var heroBg = item.Find("heroBg").GetComponent<UIImage>();
            var heroSp = item.Find("heroMask/heroSp").GetComponent<UIImage>();
            var positionSp = item.Find("positionSp").GetComponent<UIImage>();
            var teamBg = item.Find("teamBg").gameObject;
            var teamTxt = item.Find("teamBg/teamTxt").GetComponent<UIText>();
            var servicesSp = item.Find("servicesSp").GetComponent<UIImage>();
            var levelTxt = item.Find("levelTxt").GetComponent<UIText>();
            var starBg = item.Find("starBg").GetComponent<UIImage>();
            var starObj = item.Find("starBg/starObj");
            var chipTxt = item.Find("starBg/chipTxt").GetComponent<UIText>();
            var maskBg = item.Find("maskBg").gameObject;
            var redImg = item.Find("redImg").gameObject;
            var starImg = item.Find("starImg").gameObject;

            var heroData = GameEntry.LogicData.HeroData;
            var heroVo = heroModule;

            heroBg.SetImage(GetItemBgPath(heroVo.Quality));
            heroSp.SetImage(heroVo.HeroHead);
            positionSp.SetImage(heroData.GetPositionImgPath(heroVo.Position), true);
            servicesSp.SetImage(heroData.GetServicesImgPath(heroVo.Services), true);
            starBg.SetImage(GetStarBgPath(heroVo.Quality));

            teamTxt.text = heroVo.TeamId + "";
            teamBg.SetActive(heroVo.TeamId > 0);

            var isActive = heroVo.IsActive;
            if (isActive)
            {
                var starNum = heroVo.StarNum;
                var starOrder = heroVo.StarOrder;
                var count = starObj.childCount;
                for (int i = 0; i < count; i++)
                {
                    var starSp = starObj.GetChild(i).GetComponent<UIImage>();
                    string pathStr;
                    if (i < starNum) { pathStr = "Sprite/ui_hero/hero_icon_star5.png"; }
                    else if (i < starNum + 1 && starOrder > 0) { pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder); }
                    else { pathStr = "Sprite/ui_hero/hero_icon_star0.png"; }
                    starSp.SetImage(pathStr);
                }
                levelTxt.text = string.Format("Lv.{0}", heroVo.level);
                chipTxt.text = "";
            }
            else
            {
                var count = GameEntry.LogicData.BagData.GetAmountById(heroVo.Piece);
                chipTxt.text = string.Format("{0}/{1}", count, heroVo.Combind);
                levelTxt.text = "";
            }
            starObj.gameObject.SetActive(isActive);
            maskBg.SetActive(!isActive && !heroVo.IsCombind);
            // redImg.SetActive(heroData.GetSingleHeroRed(heroVo.id));
            starImg.SetActive(heroVo.GetHeroStarRed());
        }

        public string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di2_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di2_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
            };
        }

        public string GetStarBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di1_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di1_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
            };
        }

        void OnEquipmentSwitch(object sender, GameEventArgs e)
        {
            if (e is EquipmentSwitchEventArgs args)
            {
                selectedEquipmentID = args.EquipmentID;

                for (int i = 0; i < curShowModuleList.Count; i++)
                {
                    if (curShowModuleList[i] is EquipmentModule equipmentModule && equipmentModule.id == selectedEquipmentID)
                    {
                        selectedIndex = i;

                        int row = i / lineItemCount;
                        int rowMax = curShowModuleList.Count / lineItemCount;
                        int indexMin = Mathf.Min(row, curTableViewIndex);

                        if (indexMin < rowMax - 3)
                        {
                            m_TableViewV.scrollByItemIndex(row);
                        }
                        
                        m_TableViewV.refresh();
                        break;
                    }
                }
            }
        }
        
        void UpdateEquipmentSelectedIndex()
        {
            for (int i = 0; i < curShowModuleList.Count; i++)
            {
                if (curShowModuleList[i] is EquipmentModule equipmentModule && equipmentModule.id == GameEntry.EquipmentData.CurDetailEquipmentID)
                {
                    selectedIndex = i;
                    int row = i / lineItemCount;
                    int rowMax = curShowModuleList.Count / lineItemCount;
                    int indexMin = Mathf.Min(row, curTableViewIndex);

                    if (indexMin < rowMax - 3)
                    {
                        m_TableViewV.scrollByItemIndex(row);
                    }
                    break;
                }
            }
        }
    }
}

using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTruckDetailForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnCollect;
        [SerializeField] private UIButton m_btnShare;

        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtContent;
        [SerializeField] private UIText m_txtPos;
        [SerializeField] private UIText m_txtPower;
        [SerializeField] private UIText m_txtSubTitle;

        [SerializeField] private UIImage m_imgTruckIcon;
        [SerializeField] private UIImage m_imgQuality;

        [SerializeField] private Transform m_transContent;
        [SerializeField] private GameObject m_goTitle;
        [SerializeField] private GameObject m_goBubble;
        [SerializeField] private GameObject m_goTruckInfo;
        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Transform m_transContentHero;
        [SerializeField] private Transform m_transContentReward;
        [SerializeField] private Transform m_transReward;
        [SerializeField] private GameObject m_goSubTitle;
        [SerializeField] private Transform m_transRobList;
        [SerializeField] private Transform m_transBattle;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnCollect.onClick.AddListener(OnBtnCollectClick);
            m_btnShare.onClick.AddListener(OnBtnShareClick);
        }
    }
}

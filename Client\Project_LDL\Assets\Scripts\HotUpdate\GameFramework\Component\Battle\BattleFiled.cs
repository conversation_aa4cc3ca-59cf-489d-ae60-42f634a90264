using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Battle;
using GameFramework;
using UnityEngine;

namespace Game.Hotfix
{
    #region 事件定义
    public enum BattleFiledEvent
    {
        OnHeroCreate,
        OnHeroDelete,
        OnHeroAttrChange,
        OnHeroDie,
        OnHeroMainSkillCast,
    }

    public class BattleFiledOnHeroAttrChangeParam:IReference
    {
        public ChangeType ChangeType;
        public long Value;
        public EnumBattlePos BattlePos;
        public int Mask;
        public void Clear()
        {
        }
    }
    #endregion
    
    public class BattleFiled
    {
        public bool IsDebug = false;
        public Dictionary<EnumBattlePos,ColoredBgPanel> ChoosePositions => m_ChoosePositions;
        
        private BattleEffectCtrl m_BattleEffectCtrl;
        private BattleTeamCtrl m_BattleTeamCtrl;
        private BattleBulletCtrl m_BattleBulletCtrl;
        private BattleRecordCtrl m_BattleRecordCtrl;

        public BattleEffectCtrl EffectCtrl => m_BattleEffectCtrl;

        public BattleTeamCtrl TeamCtrl => m_BattleTeamCtrl;

        public BattleBulletCtrl BulletCtrl => m_BattleBulletCtrl;


        public BattleRecordCtrl RecordCtrl => m_BattleRecordCtrl;

        private Dictionary<EnumBattlePos,ColoredBgPanel> m_ChoosePositions;
        private Dictionary<EnumBattlePos,Transform> m_BattlePositions;
        private MonoBehaviour m_CoroutineHandler;
        
        private EventDispatch<BattleFiledEvent> m_EventDispatch = new EventDispatch<BattleFiledEvent>();

        public void Init(Dictionary<EnumBattlePos,ColoredBgPanel> choosePositions,Dictionary<EnumBattlePos,Transform> battlePositions,MonoBehaviour coroutineHandler)
        {
            m_ChoosePositions = choosePositions;
            m_BattlePositions = battlePositions;
            m_CoroutineHandler = coroutineHandler;
            
            m_BattleEffectCtrl = new BattleEffectCtrl();
            EffectCtrl.Init(this);

            m_BattleTeamCtrl = new BattleTeamCtrl();
            TeamCtrl.Init(this);

            m_BattleBulletCtrl = new BattleBulletCtrl();
            BulletCtrl.Init(this);

            m_BattleRecordCtrl = new BattleRecordCtrl();
            RecordCtrl.Init(this);

        }

        public void OnUpdate(float dt)
        {
            m_BattleEffectCtrl.OnTick(dt);
            m_BattleTeamCtrl.OnTick(dt);
            m_BattleBulletCtrl.OnTick(dt);
            m_BattleRecordCtrl.OnTick(dt);
            
        }

        public void UnInit()
        {
            m_BattleEffectCtrl.UnInit();
            m_BattleTeamCtrl.UnInit();
            m_BattleBulletCtrl.UnInit();
            m_BattleRecordCtrl.UnInit();
        }

        public Vector3 GetTeamPosByUid(EnumBattlePos pos)
        {
            if (m_BattlePositions.TryGetValue(pos, out var coloredBgPanel))
            {
                return coloredBgPanel.transform.position;
            }
            return Vector3.back;
        }
        
        public Transform GetBattlePos(EnumBattlePos pos)
        {
            if (m_BattlePositions.TryGetValue(pos, out var coloredBgPanel))
            {
                return coloredBgPanel.transform;
            }
            return null;
        }
        
        public Transform GetChoosePos(EnumBattlePos pos)
        {
            if (m_ChoosePositions.TryGetValue(pos, out var coloredBgPanel))
            {
                return coloredBgPanel.transform;
            }
            return null;
        }
        
        private void RemoveAll()
        {
            m_BattleEffectCtrl.RemoveAll();
            m_BattleTeamCtrl.RemoveAll();
            m_BattleBulletCtrl.RemoveAll();
            m_BattleRecordCtrl.RemoveAll();
        }
        
        #region 播放战斗

        public void SkipBattle()
        {
            m_BattleRecordCtrl.Pause();
            var res = m_BattleRecordCtrl.GetBattleResult();
            PlayRecordFinish(res);
        }
        
        public bool ReplayRecord()
        {
            var report = m_BattleRecordCtrl.Report;
            if (report != null)
            {
                PlayRecord(report);
                return true;
            }

            return false;
        }
        
        public bool PlayRecord(Report report)
        {
            RemoveAll();
            
            m_BattleRecordCtrl.Load(report);

            float battleDelay; 
            if (m_CoroutineHandler != null)
            {
                battleDelay = BattleDefine.BATTLE_WAIT_TIME;
                m_CoroutineHandler.StartCoroutine(PlayRecordProcess(battleDelay));
            }
            else
            {
                battleDelay = 0;
                m_BattleTeamCtrl.ResetAllSkillCD();
                m_BattleRecordCtrl.Run();
            }
            
            if (!GameEntry.UI.HasUIForm(EnumUIForm.UIBattle5V5Fight))
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5Fight,new UIBattle5V5FightParam(battleDelay));
            }
            else
            {
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIBattle5V5Fight,new UIBattle5V5FightParam(battleDelay));    
            }
            return true;
        }

        private IEnumerator PlayRecordProcess(float battleDelay)
        {
            //移动相机
            GameEntry.Battle5v5.MoveCameraToBattle(battleDelay);
            //所有队伍向前移动
            yield return new WaitForSeconds(battleDelay);
            m_BattleTeamCtrl.ResetAllSkillCD();
            m_BattleRecordCtrl.Run();
        }
        
        public void PlayRecordFinish(BattleResult finishResult)
        {
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIBattle5V5Fight))
            {
                var fightUI = GameEntry.UI.GetUIForm(EnumUIForm.UIBattle5V5Fight);
                if (fightUI != null && fightUI is UIBattle5V5Fight fightUIform)
                {
                    fightUIform?.OnBattleRecordFinish();
                }
            }

            string timeKey = "PlayRecordFinish";
            Timers.Instance.Remove(timeKey);
            Timers.Instance.Add(timeKey,3f, (param) =>
            {
                GameEntry.UI.CloseUIForm(EnumUIForm.UIBattle5V5Fight);

                // 根据战斗类型打开对应的胜利或失败界面
                var battleType = GameEntry.LogicData.Battle5v5Data.CurBattleType;

                if (finishResult == BattleResult.AttackerWin)
                {
                    // 胜利界面
                    switch (battleType)
                    {
                        case EnumBattle5v5Type.Dungeon:
                            // 关卡挑战胜利界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonVictory);
                            break;
                        case EnumBattle5v5Type.TradeTruck:
                            // 货车掠夺胜利界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5TradeTruckVictoryForm);
                            break;
                        case EnumBattle5v5Type.Replay:
                        {
                            var p = new UIBattle5V5DungeonStatsFormParam();
                            p.Report = RecordCtrl.Report;
                            p.IsReplay = true;
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonStatsForm, p);
                            break;
                        }
                        default:
                            // 默认使用关卡挑战胜利界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonVictory);
                            break;
                    }
                }
                else
                {
                    // 失败界面
                    switch (battleType)
                    {
                        case EnumBattle5v5Type.Dungeon:
                            // 关卡挑战失败界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonDefeat);
                            break;
                        case EnumBattle5v5Type.TradeTruck:
                            // 货车掠夺失败界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonDefeat);
                            break;
                        case EnumBattle5v5Type.Replay:
                            var p = new UIBattle5V5DungeonStatsFormParam();
                            p.Report = RecordCtrl.Report;
                            p.IsReplay = true;
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonStatsForm, p);
                            break;
                        default:
                            // 默认使用关卡挑战失败界面
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonDefeat);
                            break;
                    }
                }
            });
        }
        

        #endregion
        
        #region 设置地面相关逻辑

        public void ShowGround(bool show)
        {
            foreach (var item in m_ChoosePositions)
            {
                item.Value.ShowAll(show);
            }
        }

        public void SetColor(EnumBattlePos pos,Color? color)
        {
            if (m_ChoosePositions.TryGetValue(pos, out var coloredBgPanel))
            {
                coloredBgPanel.SetColor(color);
            }
        }

        #endregion
        
        #region 事件

        public void AddEventListener(BattleFiledEvent eventType, EventCallBack eventHandler)
        {
            m_EventDispatch.RegisterEvent(eventType, eventHandler);
        }

        public void RemoveEventListener(BattleFiledEvent eventType, EventCallBack eventHanlder)
        {
            m_EventDispatch.UnRegisterEvent(eventType, eventHanlder);
        }

        public void SendEvent(BattleFiledEvent eventType, object obj)
        {
            m_EventDispatch.PostEvent(eventType, obj);
        }

        #endregion
    }
}
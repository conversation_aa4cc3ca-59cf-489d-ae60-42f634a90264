using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ED_WorldMapTown:EntityData
    {
        public GridTownData GridTownData;
        public int? ParentId;
        public ED_WorldMapTown(int entityId,GridTownData data,int? parentId) : base(entityId)
        {
            GridTownData = data;
            ParentId = parentId;
        }
    }
    
    public class EL_WorldMapTown : EL_WorldMapDisplay
    {
        public GridTownData GridTownData => m_GridTownData;
        
        private GridTownData m_GridTownData;

        private uint? m_HudId;
        
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            if (userData is ED_WorldMapTown param)
            {
                m_GridTownData = param.GridTownData;
                if (param.ParentId != null)
                {
                    Game.GameEntry.Entity.AttachEntity(this.Entity.Id, param.ParentId.Value);
                }

                if(m_HudId!=null)
                    GameEntry.HUD.HideHUD(m_HudId.Value);
                m_HudId = GameEntry.HUD.ShowHUD(this, EnumHUD.HUDWorldMapTown, false, m_GridTownData);
            }
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
            
            if(m_HudId!=null)
                GameEntry.HUD.HideHUD(m_HudId.Value);
            m_HudId = null;
        }

        protected override void OnAttachTo(EntityLogic parentEntity, Transform parentTransform, object userData)
        {
            base.OnAttachTo(parentEntity, parentTransform, userData);
            CachedTransform.localPosition = Vector3.zero;
        }

        public override void OnClick()
        {
            base.OnClick();
            GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapTownMenu, this);
        }
    }
}
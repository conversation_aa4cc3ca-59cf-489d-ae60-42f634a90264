using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpMonsterDrawer : IWMCmp
    {
        
        private Dictionary<Vector2Int, List<int>> m_ShowListDic = new Dictionary<Vector2Int, List<int>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        public WMCmpMonsterDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty += OnGridDataDirty;
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent.OnGridDataDirty -= OnGridDataDirty;
        }

        private void OnGridDataDirty(HashSet<Vector2Int> dirtyData)
        {
            foreach (var t in dirtyData)
            {
                Remove(t);
            }
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                WorldMapLOD.Level1, out List<Vector2Int> showListNew);

            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    var list = gridData.MonsterData;
                    if (list != null)
                    {
                        var idList = new List<int>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            GridMonsterData data = list[i];
                            int elementId = data.ElementId;
                            var path = GetPathById(elementId);
                            
                            ED_WorldMapElement param =
                                new ED_WorldMapElement(Game.GameEntry.Entity.GenerateSerialId(), elementId);
                            param.Position = new Vector3(data.PosX, 0, data.PosY);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(path, param, typeof(EL_WorldMapElement));
                            idList.Add(id);
                        }
                    
                        m_ShowListDic.Add(grid, idList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var idList))
                {
                    for (int i = 0; i < idList.Count; i++)
                    {
                        var id = idList[i];
                        GameEntry.Entity.HideEntity(id);
                    }

                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private string GetPathById(int id)
        {
            var rowData = GameEntry.LDLTable.GetTableById<map_element>(id);
            if (rowData != null)
            {
                var config = GameEntry.LDLTable.GetTableById<map_buildpre>(rowData.model);
                return config?.pre;
            }
            return string.Empty;
        }
    }
}
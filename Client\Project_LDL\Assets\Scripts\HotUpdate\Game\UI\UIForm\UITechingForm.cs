using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UITechingForm : UGuiFormEx
    {
        public tech_config config;
        public int techGroup = 0;
        public tech_config nextConfig;
        public bool isMax = false;
        private BuildingModule _curBuildingModule;
        public TechQueue curQueue;
        public int curLevel = 0;
        public bool isCanHelp = false;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            var param = userData as TechDetail;
            techGroup = param.techGroup;
            _curBuildingModule = param._curBuildingModule;
            //改
            TechQueue queue1 = GameEntry.LogicData.TechData.GetTechQueueLine1();
            TechQueue queue2 = GameEntry.LogicData.TechData.GetTechQueueLine2();
            TechQueue queue3 = GameEntry.LogicData.TechData.GetTechQueueLine3();

            if (queue1 != null && queue1.curTechGroup == techGroup)
            {
                curQueue = queue1;
            }
            else if (queue2 != null && queue2.curTechGroup == techGroup)
            {
                curQueue = queue2;
            }
            else if (queue3 != null && queue3.curTechGroup == techGroup)
            {
                curQueue = queue3;
            }
            if (techGroup > 0)
            {
                curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techGroup);
                config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techGroup, curLevel);
                if (config.next_id == 0)
                {
                    isMax = true;
                }
                else
                {
                    nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                }
                OnRefreshPanel();
                OnRefreshSlider();
                OnRefreshBtnState();
            }
        }
        public void OnRefreshBtnState()
        {
            isCanHelp = curQueue != null && curQueue.Help == 0 && GameEntry.LogicData.UnionData.IsJoinUnion();

            m_btnHelp.gameObject.SetActive(false);
            m_btnAdd.gameObject.SetActive(false);
            m_btnFinish.gameObject.SetActive(false);
            if (curQueue != null)
            {
                if (curQueue.IsFinish())
                {
                    m_btnAdd.gameObject.SetActive(false);
                    m_btnHelp.gameObject.SetActive(false);
                    m_btnFinish.gameObject.SetActive(true);
                }
                else
                {
                    m_btnAdd.gameObject.SetActive(!isCanHelp);
                    m_btnHelp.gameObject.SetActive(isCanHelp);
                    m_btnFinish.gameObject.SetActive(false);
                }
            }
        }
        public void OnRefreshSlider()
        {
            
            if (curQueue != null)
            {
                float remainTime = curQueue.GetRemainTime();
                float totalTime = curQueue.GetTotalTime();
                if (remainTime <= 0)
                {
                    Timers.Instance.Remove("UITechingForm");
                    m_slider.value = 1f;
                    m_txtTime.text = ToolScriptExtend.GetLang(1100130);
                    OnRefreshBtnState();
                }
                else
                {
                    m_slider.value = 1 - (remainTime / totalTime);
                    m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
                }

                Timers.Instance.Add("UITechingForm", 1f, (param) =>
               {
                   if (curQueue == null)
                   {
                       Timers.Instance.Remove("UITechingForm");
                       return;
                   }    
                   if (m_slider != null)
                   {
                       float remainTime = curQueue.GetRemainTime();
                       float totalTime = curQueue.GetTotalTime();
                       if (remainTime <= 0)
                       {
                           Timers.Instance.Remove("UITechingForm");
                           m_slider.value = 1f;
                           m_txtTime.text = ToolScriptExtend.GetLang(1100130);
                           OnRefreshBtnState();
                       }
                       else
                       {
                           m_slider.value = 1 - (remainTime / totalTime);
                           m_txtTime.text = TimeHelper.FormatGameTimeWithDays((int)remainTime);
                       }
                   }
               }, -1);
            }
            else
            {
                if (isMax)
                {
                    m_slider.value = 1f;
                }
            }
            m_goMax.SetActive(isMax);
            m_goSlider.SetActive(!isMax);
            m_txtNext.gameObject.SetActive(!isMax);
            m_txtNextValue.gameObject.SetActive(!isMax);
        }
         public void OnRefreshPanel()
        {
            if (config == null) return;
            m_txtDes.text = ToolScriptExtend.GetLang(config.tech_desc);
            m_txtName.text = ToolScriptExtend.GetLang(config.tech_title);
            m_imgIcon.SetImage(config.tech_icon);
            m_txtCurrent.text = ToolScriptExtend.GetLang(80100009);
            m_txtNext.text = ToolScriptExtend.GetLang(80100010);
            m_txtName.text = ToolScriptExtend.GetLang(config.tech_title);


            if (config.tech_attributes.Count > 0)
            {
                // m_txtCurrentValue.text = ToolScriptExtend.GetNameByAttrbuteType(config.tech_attributes[0].attributes_type)
                // + ToolScriptExtend.GetAttrLang(config.tech_attributes[0].value);
                float additionValue = (float)config.tech_attributes[0].value / 10000;
                m_txtCurrentValue.text = additionValue * 100 + "%";
            }
            else
            {
                m_txtCurrentValue.text = "0";
            }


            if (config.next_id != 0)
            {
                tech_config nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                float additionValue = (float)nextConfig.tech_attributes[0].value / 10000;
                m_txtNextValue.text = additionValue * 100 + "%";
            }
            else
            {
                //Max
            }

        }
        protected override void OnClose(bool isShutdown, object userData)
        {
            Timers.Instance.Remove("UITechingForm");
            isMax = false;
            curQueue = null;
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnDeatialClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITechLevelBuffForm, techGroup);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
        private void OnBtnFinishClick()
        {
            if (curQueue != null)
            {
                if (curQueue.IsFinish())
                {
                    GameEntry.LogicData.TechData.CompeleteTech(curQueue);
                    Close();
                }
            }
        }
        private void OnBtnAddClick()
        {
            if (curQueue == null) return;
            itemsubtype itemSubType = itemsubtype.itemsubtype_researchspeedup;
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSpeedUpForm, new OpenSpeedUpParam(_curBuildingModule, itemSubType,itemid.itemid_nil,(int)curQueue.BindBuildNo));
        }

        private void OnBtnHelpClick()
        {
            if (curQueue == null) return;

            GameEntry.LogicData.BuildingData.BuildQueueHelpReq(curQueue.BindBuildNo, curQueue.QueueUid, (resp) =>
            {
                var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(curQueue.BindBuildNo);
                buildingModule?.OnUnionHelpChange(Build.QueueType.BuildTech, false);

                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(80400010)
                });

                OnRefreshBtnState();
                GameEntry.Event.Fire(PrivilegeChangeEventArgs.EventId, PrivilegeChangeEventArgs.Create());
            });
        }
    }
}

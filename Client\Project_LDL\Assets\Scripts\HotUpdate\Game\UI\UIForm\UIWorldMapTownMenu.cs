using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIWorldMapTownMenu : UIWorldMapMenuBase
    {
        [SerializeField] private WorldMapMenuItem worldMapMenuItem;
        
        private EL_WorldMapTown m_EntityLogic;
        private GridTownData m_GridTownData;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            if (userData is EL_WorldMapTown worldMapElement)
            {
                m_EntityLogic = worldMapElement;
                m_GridTownData = m_EntityLogic.GridTownData;
            }
            
            ResetUI();
        }

        protected override void OnRefocus(object userData)
        {
            base.OnRefresh(userData);
            if (userData is EL_WorldMapTown worldMapElement)
            {
                if (m_EntityLogic == worldMapElement) return;
                m_EntityLogic = worldMapElement;
                m_GridTownData = m_EntityLogic.GridTownData;
            }
            
            ResetUI();
        }

        private void ResetUI()
        {
            List<int> list = new List<int>();
            GridTownData gridTownData = m_EntityLogic.GridTownData;
            
            m_goLike.SetActive(false);
            if(IsMe())
            {
                m_goLike.SetActive(false);
                list.Add(2001);
                list.Add(2003);
                list.Add(2004);
                
            }else if (IsFriend())
            {
                m_goLike.SetActive(true);
                list.Add(2006);
            }
            else
            {
                m_goLike.SetActive(true);
                list.Add(2007);
                list.Add(2008);
                list.Add(2009);
            }
            
            ResetMenuUI(m_EntityLogic, worldMapMenuItem, list, OnMenuBtnClick, m_rectMove, m_rectMenu);
        }
        
        protected void OnMenuBtnClick(build_menubutton cfg)
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
            Close();
        }

        private bool IsMe()
        {
            if (m_GridTownData.RoleId == GameEntry.LogicData.UserData.uuid)
                return true;
            return false;
        }

        private bool IsFriend()
        {
            return false;
        }
        
        private void OnBtnShareClick()
        {

        }

        private void OnBtnFavoriteClick()
        {

        }

        private void OnBtnInfoClick()
        {
            
        }
        
        private void OnBtnLikeClick()
        {
            
        }
    }
}

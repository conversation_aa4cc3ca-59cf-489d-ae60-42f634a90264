using System;
using System.Collections;
using System.Collections.Generic;
using Build;
using Coffee.UIEffects;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{

    public partial class UIMainFaceForm : UGuiFormEx, IUIResIconLocator
    {
        private bool m_ResetTopResourceUIAble = false;
        private Dictionary<itemid, ResNode> ResNodeDic;
        private RechargeScoreCtrl rechargeScoreCtrl;
        public RechargeScoreCtrl RechargeScoreCtrl => rechargeScoreCtrl;

        private bool m_IsWorldMap;

        #region 私有子类
        private class ResNode
        {
            public itemid id;
            public UIImage icon;
            public UIText num;
            public UIButton btn;
            public GameObject obj;
        }

        #endregion


        #region 生命周期
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            m_goPrefab.SetActive(false);
            //道具飞行动画界面需要常驻
            GameEntry.UI.OpenUIForm(EnumUIForm.UIResFlyForm);
            rechargeScoreCtrl = m_goExtend.transform.Find("RechargeScoreCtrl").GetComponent<RechargeScoreCtrl>();
            SwitchWorldBtn(true);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            m_IsWorldMap = Game.GameEntry.Procedure.CurrentProcedure is ProcedureWorldMap;

            if (ResNodeDic == null)
            {
                ResNodeDic = new Dictionary<itemid, ResNode>();
            }
            else
            {
                ResNodeDic.Clear();
            }

            ResNodeDic?.Clear();
            FlyResManager.SetLocator(this);

            ResetTopResourceUI();
            ResetBuildQueueUI();
            RefreshTaskInfo();
            RefreshPlayerInfo();
            RefreshTruckEntry();
            int redCount = GetBuildingRed();
            m_imgRedBuilding.gameObject.SetActive(redCount > 0);
            m_txtBuildRed.text = redCount.ToString();
            m_txtTechLine.text = $"{GameEntry.LogicData.TechData.GetTechingCount()}/{GameEntry.LogicData.TechData.GetTechLineCount()}";
            Game.GameEntry.Event.Subscribe(OnBuildLongPressToMoveArgs.EventId, OnBuildLongPressToMove);
            Game.GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Subscribe(OnNewBuildingCreateEventArgs.EventId, OnBuildingCreate);
            GameEntry.Event.Subscribe(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent);
            GameEntry.Event.Subscribe(TaskChangeEventArgs.EventId, OnTaskChangeUpdate);
            GameEntry.Event.Subscribe(RedPointEventArgs.EventId, OnRedPointUpdate);
            GameEntry.Event.Subscribe(MallChangeEventArgs.EventId, OnMallChangeFunc);//商城
            GameEntry.Event.Subscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);//vip
            GameEntry.Event.Subscribe(ChaoZhiDotEventArgs.EventId, OnChaoZhiDotChangeFunc);//超值活动
            GameEntry.Event.Subscribe(TechChangeEventArgs.EventId, OnTechChangeUpdate);
            GameEntry.Event.Subscribe(PrivilegeChangeEventArgs.EventId, OnPrivilegeChange);
            GameEntry.Event.Subscribe(PioneerChangeEventArgs.EventId, OnPioneerChangeFunc);//先锋战令
            GameEntry.Event.Subscribe(SpecialEventArgs.EventId, OnSpecialEventChangeFunc);//特殊事件
            GameEntry.Event.Subscribe(TrainPassengerEventArgs.EventId, OnTrainPassengerEventArgs);

            EnterRequest();
            OnInitRedPoint();
            CheckEntryUnlock(true);
            CheckVipInfo();

            InitMainPageView();


            //区分沙盘和主城
            m_btnBuilding.gameObject.SetActive(!m_IsWorldMap);
            m_btnWorker.gameObject.SetActive(!m_IsWorldMap);
            m_goLeftTopCity.gameObject.SetActive(!m_IsWorldMap);
            m_goLeftTopWorld.gameObject.SetActive(m_IsWorldMap);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            Game.GameEntry.Event.Unsubscribe(OnBuildLongPressToMoveArgs.EventId, OnBuildLongPressToMove);
            Game.GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            Game.GameEntry.Event.Unsubscribe(OnNewBuildingCreateEventArgs.EventId, OnBuildingCreate);
            GameEntry.Event.Unsubscribe(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent);
            GameEntry.Event.Unsubscribe(TaskChangeEventArgs.EventId, OnTaskChangeUpdate);
            GameEntry.Event.Unsubscribe(RedPointEventArgs.EventId, OnRedPointUpdate);
            GameEntry.Event.Unsubscribe(MallChangeEventArgs.EventId, OnMallChangeFunc);
            GameEntry.Event.Unsubscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);
            GameEntry.Event.Unsubscribe(ChaoZhiDotEventArgs.EventId, OnChaoZhiDotChangeFunc);
            GameEntry.Event.Unsubscribe(TechChangeEventArgs.EventId, OnTechChangeUpdate);
            GameEntry.Event.Unsubscribe(PrivilegeChangeEventArgs.EventId, OnPrivilegeChange);
            GameEntry.Event.Unsubscribe(PioneerChangeEventArgs.EventId, OnPioneerChangeFunc);
            GameEntry.Event.Unsubscribe(SpecialEventArgs.EventId, OnSpecialEventChangeFunc);
            GameEntry.Event.Unsubscribe(TrainPassengerEventArgs.EventId, OnTrainPassengerEventArgs);

            FlyResManager.RemoveLocator(this);
            StopCoroutine("ScrollTaskTextHorizontal");
            ResNodeDic?.Clear();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            UiMainfaceParams refreshParams = userData as UiMainfaceParams;
            if (refreshParams == null)
            {
                return;
            }

            if (refreshParams.m_RefreshType == MainFaceRefreshType.BuildQueueRefresh)
            {
                ResetBuildQueueUI();
            }


        }

        //界面初始化
        private void InitMainPageView()
        {
            m_txtBattlePower.text = ToolScriptExtend.FormatNumberWithSeparator(GameEntry.LogicData.RoleData.Power);
            m_txtEnergy.text = ToolScriptExtend.FormatNumberWithSeparator(1208);

        }

        private void OnItemChange(object sender, GameEventArgs e)
        {
            //TODO 进一步筛选 物品列表
            m_ResetTopResourceUIAble = true;
        }

        private void OnBuildingCreate(object sender, GameEventArgs e)
        {
            var ne = (OnNewBuildingCreateEventArgs)e;
            if (ne != null)
            {
                RefreshBuildingRed();
            }
        }
        private void OnTaskChangeUpdate(object sender, GameEventArgs e)
        {
            if (e is TaskChangeEventArgs args)
            {
                RefreshTaskInfo();
            }
        }
        private void OnBuildingLevelChangeEvent(object sender, GameEventArgs e)
        {
            if (e is OnBuildingLevelChangeEventArgs args)
            {
                RefreshBuildingRed();
                if (args.BuildingModule.GetBuildingType() == buildtype.buildtype_headquarters)
                {
                    RefreshPlayerInfo();
                    CheckEntryUnlock();
                }

            }
        }
        private void RefreshTaskInfo()
        {
            var Task = GameEntry.LogicData.TaskData.GetMainTask();
            var config = GameEntry.LogicData.TaskData.GetTaskMainById(Task.Id);
            if (config != null)
            {
                m_txtTask.text = ToolScriptExtend.GetLang(config.task_desc);

                // 根据任务状态显示不同节点
                bool mainRed = GameEntry.LogicData.TaskData.HasMainTask();
                bool dailyRed = GameEntry.LogicData.TaskData.HasAnyDailyTask();
                int dailyRedCount = GameEntry.LogicData.TaskData.GetReceivableDailyTaskCount();
                int dailyRedBoxRewardCount = GameEntry.LogicData.TaskData.GetDailyBoxRedCount();
                // 根据mainRed设置滑块值
                if (m_slider != null)
                    m_slider.value = mainRed ? 1f : 0f;
                // 显示或隐藏任务完成图标
                if (m_imgTaskComp != null)
                    m_imgTaskComp.gameObject.SetActive(mainRed);
                //显示完成特效
                if (m_goTaskEff != null)
                    m_goTaskEff.gameObject.SetActive(mainRed);
                // 显示或隐藏任务红点（可领取时显示）
                if (m_imgTaskRed != null)
                    m_imgTaskRed.gameObject.SetActive(dailyRed);

                // 更新每日任务可领取数量
                if (m_txtTaskRedCount != null)
                {
                    m_txtTaskRedCount.text = dailyRedCount + dailyRedBoxRewardCount > 0 ? (dailyRedCount + dailyRedBoxRewardCount).ToString() : "";
                    m_txtTaskRedCount.gameObject.SetActive(dailyRedCount + dailyRedBoxRewardCount > 0);
                }
                OnRefreshTaskWeight();
            }
        }
        private void RefreshBuildingRed()
        {
            int redCount = GetBuildingRed();
            m_imgRedBuilding.gameObject.SetActive(redCount > 0);
            m_txtBuildRed.text = redCount.ToString();
        }

        private int GetBuildingRed()
        {
            Array values = Enum.GetValues(typeof(buildclass));
            int totalRedNum = 0;
            foreach (buildclass value in values)
            {
                if (value != buildclass.buildclass_nil && value != buildclass.buildclass_noshow)
                {
                    List<build_config> buildConfigs = GameEntry.LogicData.BuildingData.GetUnLockBuildConfigListByType(value);
                    if (buildConfigs.Count > 0)
                    {
                        totalRedNum += buildConfigs.Count;
                    }
                }
            }

            return totalRedNum;
        }

        private void OnInitRedPoint()
        {
            var redState = RedPointManager.Instance.IsRed(EnumRed.Hero.ToString());
            m_goHeroRed.SetActive(redState);

            redState = RedPointManager.Instance.IsRed(EnumRed.Union.ToString());
            m_goUnionRed.SetActive(redState);
        }

        private void OnRedPointUpdate(object sender, GameEventArgs e)
        {
            RedPointEventArgs args = (RedPointEventArgs)e;
            Dictionary<string, bool> dic = args.RedPointDic;

            if (dic != null)
            {
                var redId = EnumRed.Hero.ToString();
                if (dic.ContainsKey(redId)) { m_goHeroRed.SetActive(RedPointManager.Instance.IsRed(redId)); }

                redId = EnumRed.Union.ToString();
                if (dic.ContainsKey(redId)) { m_goUnionRed.SetActive(RedPointManager.Instance.IsRed(redId)); }
            }
        }

        private void RefreshPlayerInfo()
        {
            int mainCityLevel = GameEntry.LogicData.BuildingData.GetMainCityLevel();
            m_txtLevel.text = mainCityLevel.ToString();
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (m_ResetTopResourceUIAble)
            {
                m_ResetTopResourceUIAble = false;
                ResetTopResourceUI();
            }
        }

        /// <summary>
        /// 获取资源的 UI位置
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="position"></param>
        /// <returns></returns>
        public bool GetUiPositionById(int itemId, out Vector3 position)
        {
            //TODO  临时写法
            if (itemId == 1)
            {
                //体力
                position = m_txtEnergy.transform.position;
                return true;
            }
            else if (itemId == 2)
            {
                //粮食
                // position = m_goResource.transform.Find("ResItem1").transform.position;
                position = ResNodeDic[itemid.itemid_2].obj.transform.position;
                return true;
            }
            else if (itemId == 3)
            {
                //铁矿
                // position = m_goResource.transform.Find("ResItem2").transform.position;
                position = ResNodeDic[itemid.itemid_3].obj.transform.position;
                return true;
            }
            else if (itemId == 4)
            {
                //金币
                // position = m_goResource.transform.Find("ResItem3").transform.position;
                position = ResNodeDic[itemid.itemid_4].obj.transform.position;
                return true;
            }
            else if (itemId == (int)itemid.itemid_5)
            {
                position = m_btnHero.transform.position;
                return true;
            }
            position = m_btnBag.transform.position;
            return true;
        }

        #endregion

        #region Base

        private void OnBuildLongPressToMove(object sender, GameEventArgs e)
        {
            if (e is OnBuildLongPressToMoveArgs args)
            {
                Vector2 tempV2;
                var parent = m_rectMoveNotice.parent.gameObject.GetComponent<RectTransform>();
                var camera = GameEntry.Camera.CityCamera;
                if (camera != null && args.WorldPosition != null)
                {
                    var screenPoint = RectTransformUtility.WorldToScreenPoint(camera, args.WorldPosition.Value);
                    RectTransformUtility.ScreenPointToLocalPointInRectangle(parent, screenPoint,
                        GameEntry.Camera.UICamera, out tempV2);
                    m_rectMoveNotice.anchoredPosition = tempV2;
                }
                m_rectMoveNotice.gameObject.SetActive(args.IsPressing);
            }
        }

        #endregion

        #region Top

        private void ResetTopResourceUI()
        {
            ResNodeDic ??= new Dictionary<itemid, ResNode>();
            var list = new List<itemid>() { itemid.itemid_2, itemid.itemid_3, itemid.itemid_4 };
            if (ResNodeDic.Count < list.Count)
            {
                ResNodeDic?.Clear();
                var root = m_goResource.transform;
                ToolScriptExtend.ClearAllChild(root);
                var gridLayout = m_goResource.GetComponent<GridLayoutGroup>();
                gridLayout.enabled = true;
                for (var i = 0; i < list.Count; i++)
                {
                    var flag = list[i];
                    var go = Instantiate(m_goResItem, root);
                    var resRoot = go.transform;
                    go.name = flag.ToString();
                    var icon = resRoot.Find("icon").GetComponent<UIImage>();
                    var num = resRoot.Find("num").GetComponent<UIText>();
                    var btn = resRoot.Find("btn").GetComponent<UIButton>();

                    ResNodeDic[flag] = new ResNode()
                    {
                        id = flag,
                        icon = icon,
                        num = num,
                        btn = btn,
                        obj = go,
                    };
                }
                LayoutRebuilder.ForceRebuildLayoutImmediate(root.GetComponent<RectTransform>());
                gridLayout.enabled = false;
            }

            foreach (var item in ResNodeDic)
            {
                var flag = item.Key;
                item.Value.icon.SetImage(ToolScriptExtend.GetItemIcon(flag));
                item.Value.num.text = GetResCount(flag);

                ToolScriptExtend.BindBtnLogic(item.Value.btn, () =>
                {
                    ItemModule itemModule = new(flag);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(0));
                });
            }

            //钻石数量更新
            m_txtRedDiamond.text = GetResCount(itemid.itemid_6);
        }

        private string GetResCount(itemid id)
        {
            var count = GameEntry.LogicData.BagData.GetAmountById(id);
            return ToolScriptExtend.FormatNumberWithUnit(count);
        }

        private void ResetBuildQueueUI()
        {
            int canUseBuildQueueCount = GameEntry.LogicData.QueueData.GetCanUseBuildQueueCount();
            int workingBuildQueueCount = GameEntry.LogicData.QueueData.GetWorkingBuildQueueCount();
            m_txtBuildQueue.text = $"{workingBuildQueueCount}/{canUseBuildQueueCount}";
        }

        //头像
        private void OnBtnBorderClick()
        {

            Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIPlayerInfoForm);

            // Debug.Log("头像点击！");
        }

        //体力
        private void OnBtnEnergyClick()
        {
            Debug.Log("体力检查！");
        }

        //商店
        private void OnBtnShopClick()
        {
            var mallManager = GameEntry.LogicData.MallData;
            var isUnlock = mallManager.IsMallUnlock();
            if (isUnlock)
            {
                mallManager.C2SShopLoadReq((resp) =>
                {
                    if (!mallManager.IsEmptyMall())
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIMallForm);
                    }
                    mallManager.RequestInitMsg();
                });
            }
        }

        #endregion

        #region Bottom
        //聊天
        private void OnBtnChatClick()
        {
            Debug.Log("聊天检查！");
        }

        //英雄背包
        private void OnBtnHeroClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroForm);
        }

        //世界地图
        private void OnBtnWorldClick()
        {
            var isInWorld = GameEntry.Procedure.CurrentProcedure is ProcedureWorldMap;
            if (isInWorld)
            {
                GameEntry.LogicData.WorldMapData.GoBackMainCity();
            }
            else
            {
                GameEntry.LogicData.WorldMapData.GotoWorldMap();
            }
            SwitchWorldBtn(isInWorld);
        }

        //世界和基地按钮切换显示
        private void SwitchWorldBtn(bool isShowWorld)
        {
            var node1 = m_btnWorld.transform.Find("node1");
            var node2 = m_btnWorld.transform.Find("node2");
            node1.gameObject.SetActive(isShowWorld);
            node2.gameObject.SetActive(!isShowWorld);
        }

        #endregion

        #region LeftTop
        //Vip
        private void OnBtnVIPClick()
        {
            var level = GameEntry.LogicData.VipData.GetVipLevel();
            GameEntry.UI.OpenUIForm(EnumUIForm.UIVipForm, level);
        }

        //建造队列
        private void OnBtnBuildQueueClick()
        {
            Debug.Log("建造队列检查！");
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingQueueForm);
        }

        //研究队列
        private void OnBtnStudyQueueClick()
        {
            Debug.Log("研究队列检查！");
            GameEntry.UI.OpenUIForm(EnumUIForm.UITechQueueForm);
        }

        //Buff1
        private void OnBtnBuff1Click()
        {
            Debug.Log("Buff1检查！");
        }
        //Buff2
        private void OnBtnBuff2Click()
        {
            Debug.Log("Buff2检查！");
        }
        //Buff3
        private void OnBtnBuff3Click()
        {
            Debug.Log("Buff3检查！");
        }
        //Buff4
        private void OnBtnBuff4Click()
        {
            Debug.Log("Buff4检查！");
        }
        //Buff5
        private void OnBtnBuff5Click()
        {
            Debug.Log("Buff5检查！");
        }
        #endregion

        #region LeftBottom

        //货车掠夺
        private void OnBtnTruckClick()
        {
            bool isShowTrainEntry = GameEntry.TradeTruckData.IsShowTrainEntry();
            if (isShowTrainEntry)
            {
                BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(7201);
                if (buildingModule != null)
                {
                    Vector3 buildPos = buildingModule.GetWorldCenterPosition();
                    GameEntry.Camera.LookAtPosition(buildPos);
                }
                else
                {
                    BuildingModule temp = GameEntry.CityMap?.trainStationTemp;
                    if (temp != null)
                    {
                        Vector3 buildPos = temp.GetWorldCenterPosition();
                        GameEntry.Camera.LookAtPosition(buildPos);
                    }
                }
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckForm);
            }
        }

        //秘密指挥所
        private void OnBtnSecretClick()
        {
            Debug.Log("秘密指挥所！");

            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattlePlanForm);
        }

        //建筑列表
        private void OnBtnWorkerClick()
        {
            Debug.Log("研究队列检查！");
        }

        //雷达
        private void OnBtnRadarClick()
        {
            Debug.Log("雷达检查！");
        }

        //研究队列
        private void OnBtnBuildingClick()
        {
            Debug.Log("研究队列检查！");
            GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm);
        }

        //任务
        private void OnBtnTaskClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITaskForm);
            Debug.Log("任务查看！");
        }

        //任务跳转
        private void OnBtnTurnToTaskClick()
        {
            GameEntry.LogicData.TaskData.ClaimOneCompletedMainTask(null, m_btnTurnToTask);
            //Debug.Log("跳转到指定任务界面！");
        }

        #endregion

        #region RightTop

        //超值活动
        private void OnBtnWorthyActivityClick()
        {
            GameEntry.LogicData.ChaoZhiData.CheckActivityEntryMsg(1, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIChaoZhiActivityForm);
            });
        }

        //特殊事件
        private void OnBtnSpecialEventClick()
        {
            GameEntry.LogicData.ChaoZhiData.CheckActivityEntryMsg(2, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UISpecialEventForm);
            });
        }

        //先锋
        private void OnBtnPioneerClick()
        {
            GameEntry.LogicData.ChaoZhiData.CheckActivityEntryMsg(3, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIPioneerForm);
            });
        }

        //同盟对决
        private void OnBtnLeagueBattleClick()
        {
            Debug.Log("同盟对决检查！");
        }

        //赛季
        private void OnBtnSeasonClick()
        {
            Debug.Log("赛季检查！");
            //TODO 临时把战斗放这里
            GameEntry.LogicData.Battle5v5Data.GoBattleDebug();
        }

        //限时活动
        private void OnBtnLimitGiftClick()
        {
            Debug.Log("限时活动检查！");
        }
        #endregion

        #region RightBottom

        private void OnBtnEquipmentClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIEquipmentFactoryForm);
        }

        private void OnBtnLeagueClick()
        {
            GameEntry.LogicData.UnionData.GoToUnion();
        }

        private void OnBtnLeagueHelpClick()
        {
            // Debug.Log("帮助盟友!!");

            // var queueData = GameEntry.LogicData.QueueData; 
            // var t = queueData.GetResourceQueue(901);
            // if (t == null)
            // {
            //     var queue = new Build.Queue();
            //     queue.QueueUid = (uint)Game.GameEntry.Entity.GenerateSerialId();
            //     queue.QueueType = 3;
            //     queue.BuildNo = (uint)901;
            //     queue.Help = 0;
            //     queue.CreateTime = (ulong)(TimeComponent.Now);
            //     queue.FinishTime = (ulong)(TimeComponent.Now + 5*10);
            //     queue.AccelerateTime = 0;
            //
            //     queue.Qa = new QueueArgs();
            //     queue.Qa.ResourceNo = 2;
            //     queue.Qa.ResourceNum = 1200;
            //
            //     queueData.AddQueue(queue);
            // }
            //
            // GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            // {
            //     Content = "网络连接已断开",
            // });

            var tempSceneName = "Temp";
            var tempScene = SceneManager.CreateScene(tempSceneName);

        }

        private void GetClassByClassName(string className)
        {

        }

        private void OnBtnEmailClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIMailForm);
        }

        private void OnBtnBagClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBagForm);
        }

        //客服提示
        private void OnBtnServiceMsgClick()
        {
            Debug.Log("客服提示！");
            SceneManager.UnloadSceneAsync("Temp");
        }
        #endregion

        //初始化协议请求
        private void EnterRequest()
        {
            //商城协议请求
            if (GameEntry.LogicData.MallData.IsMallUnlock())
            {
                GameEntry.LogicData.MallData.C2SShopLoadReq((resp) =>
                {
                    GameEntry.LogicData.MallData.RequestInitMsg();
                });
            }

            //超值活动协议请求
            if (GameEntry.LogicData.ChaoZhiData.IsChaoZhiActivityUnlock())
            {
                GameEntry.LogicData.ChaoZhiData.ReloadAllUnlockActivityMsg(1, (list) =>
                {
                    m_btnWorthyActivity.gameObject.SetActive(list.Count > 0);
                });
            }

            //先锋协议请求
            if (GameEntry.LogicData.ChaoZhiData.IsPioneerUnlock())
            {
                GameEntry.LogicData.ChaoZhiData.ReloadAllUnlockActivityMsg(3);
            }

            //特殊事件协议请求
            if (GameEntry.LogicData.SpecialEventData.IsSpecialEventUnlock())
            {
                GameEntry.LogicData.ChaoZhiData.ReloadAllUnlockActivityMsg(2);
            }
        }

        public void SetLocation(Transform target)
        {
            m_goLocation.transform.position = target.position;
        }

        public Vector2 GetLocationPos()
        {
            var rect = m_goLocation.GetComponent<RectTransform>();
            return rect.anchoredPosition;
        }

        //获取主界面资源飞行动画目标点位置
        public Vector2 GetResFlyPos(flightdestination des)
        {
            switch (des)
            {
                case flightdestination.flightdestination_food:
                    SetLocation(ResNodeDic[itemid.itemid_2].obj.transform);
                    break;
                case flightdestination.flightdestination_steel:
                    SetLocation(ResNodeDic[itemid.itemid_3].obj.transform);
                    break;
                case flightdestination.flightdestination_gold:
                    SetLocation(ResNodeDic[itemid.itemid_4].obj.transform);
                    break;
                case flightdestination.flightdestination_diamond:
                    SetLocation(m_imgRedDiamond.transform);
                    break;
                case flightdestination.flightdestination_alliance:
                    SetLocation(m_btnLeague.transform);
                    break;
                case flightdestination.flightdestination_bag:
                    SetLocation(m_btnBag.transform);
                    break;
                case flightdestination.flightdestination_hero:
                    SetLocation(m_imgHeroEntry.transform);
                    break;
                case flightdestination.flightdestination_vip:
                    SetLocation(m_btnVIP.transform);
                    break;
                default:
                    break;
            }
            return GetLocationPos();
        }

        //判断功能入口是否解锁
        //isInit:是否初始化时调用
        private void CheckEntryUnlock(bool isInit = false)
        {
            //商城入口解锁判断
            GameEntry.LogicData.MallData.CheckMallEntry(isInit, m_btnShop.gameObject);

            //vip入口解锁判断
            var isVipUnlock = GameEntry.LogicData.VipData.IsVipUnlock();
            m_btnVIP.transform.parent.gameObject.SetActive(isVipUnlock);
            if (isVipUnlock)
            {
                CheckVipInfo();
            }

            //超值活动入口解锁判断
            GameEntry.LogicData.ChaoZhiData.CheckChaoZhiEntry(isInit, m_btnWorthyActivity.gameObject);

            //先锋活动入口解锁判断
            GameEntry.LogicData.ChaoZhiData.CheckPioneerEntry(isInit, m_btnPioneer.gameObject);

            //特殊事件入口解锁判断
            GameEntry.LogicData.SpecialEventData.CheckSpecialEventEntry(isInit, m_btnSpecialEvent.gameObject);
        }
        // *scale = xxx/(350/80)
        private void OnRefreshTaskWeight()
        {
            // 获取文本组件及其内容
            //string taskText = m_txtTask.text;

            // 根据文本长度计算宽度
            float textWidth = Mathf.Max(m_txtTask.preferredWidth, 280f);
            float maxWidth = 535f;

            // 设置背景宽度（限制最大宽度）
            RectTransform taskBgRect = m_goTaskBg.GetComponent<RectTransform>();
            taskBgRect.sizeDelta = new Vector2(Mathf.Min(textWidth, maxWidth) + 120f, taskBgRect.sizeDelta.y);

            RectTransform scrollRect = m_scrollviewTask.GetComponent<RectTransform>();
            scrollRect.sizeDelta = new Vector2(Mathf.Min(textWidth, maxWidth) + 30f, taskBgRect.sizeDelta.y);

            RectTransform btnRect = m_btnTurnToTask.gameObject.GetComponent<RectTransform>();
            btnRect.sizeDelta = new Vector2(Mathf.Min(textWidth, maxWidth) + 100f, taskBgRect.sizeDelta.y);

            Transform saoguang = m_goTaskEff.transform.Find("saoguang/qianghua/xiaolizi55_1").GetComponent<Transform>();
            saoguang.localScale = new Vector3(1 * (taskBgRect.sizeDelta.x / 350), saoguang.localScale.y, saoguang.localScale.z);
            m_goGiftContent.gameObject.SetActive(false);
            m_goGiftContent.gameObject.SetActive(true);
            StopCoroutine("ScrollTaskTextHorizontal");
            // 如果文本长度超过最大宽度，创建协程平滑滚动显示完整内容
            if (textWidth > maxWidth)
            {
                // 停止之前可能存在的滚动协程

                StartCoroutine("ScrollTaskTextHorizontal");
            }
            else
            {

            }
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);
            SetParticleSystemSortingOrder(m_goTaskEff, Depth);
        }


        private IEnumerator ScrollTaskTextHorizontal()
        {
            while (true)
            {
                // 在最右侧暂停1秒
                yield return new WaitForSeconds(1f);

                // 从左向右平滑滚动，持续2秒
                float startTime = Time.time;
                float duration = 2f;
                // 确保开始位置在最左侧
                m_scrollviewTask.horizontalNormalizedPosition = 0f;

                // 在2秒内平滑滚动到最右侧
                while (Time.time - startTime < duration)
                {
                    float t = (Time.time - startTime) / duration;
                    m_scrollviewTask.horizontalNormalizedPosition = Mathf.Lerp(0f, 1f, t);
                    yield return null;
                }

                // 确保滚动到最右侧
                m_scrollviewTask.horizontalNormalizedPosition = 1f;
                // 在最右侧暂停1秒
                yield return new WaitForSeconds(1f);
                // 立即回到最左侧
                m_scrollviewTask.horizontalNormalizedPosition = 0f;
                // 等待一帧确保滚动重置
                yield return null;
            }
        }

        //商城改变
        private void OnMallChangeFunc(object sender, GameEventArgs e)
        {
            //商城入口红点更新
            var dotRoot = m_btnShop.transform.Find("redDot");
            var dotCount = RedPointManager.Instance.GetRedCount(EnumRed.Mall.ToString());
            ToolScriptExtend.SetNumRedDot(dotRoot, dotCount);
        }

        //vip信息更新
        private void OnVipChangeFunc(object sender, GameEventArgs e)
        {
            CheckVipInfo();
        }

        //检查vip信息
        private void CheckVipInfo()
        {
            var vipManager = GameEntry.LogicData.VipData;
            if (!vipManager.IsVipUnlock()) return;
            var isActive = vipManager.IsVipActive();
            var onBg = m_btnVIP.transform.Find("onBg");
            var offBg = m_btnVIP.transform.Find("offBg");
            onBg.gameObject.SetActive(isActive);
            offBg.gameObject.SetActive(!isActive);

            var level = vipManager.GetVipLevel();
            //vip红点
            var vipDot = m_btnVIP.transform.Find("dot");
            var dotCount = vipManager.GetVipRedDot();
            ToolScriptExtend.SetNumRedDot(vipDot, dotCount);

            var levelTxt = m_btnVIP.transform.Find("Text").GetComponent<UIText>();
            levelTxt.text = level.ToString();
            if (isActive)
            {
                //颜色码转Color
                bool isSuccess = ColorUtility.TryParseHtmlString("#FED819", out Color result);
                if (isSuccess)
                {
                    levelTxt.color = result;
                }
            }
            else
            {
                levelTxt.color = Color.white;
            }

            if (ColorUtility.TryParseHtmlString(isActive ? "#491402" : "#5E5E5E", out Color shadowColor))
            {
                Outline outline = levelTxt.GetComponent<Outline>();
                if (outline != null)
                {
                    outline.effectColor = shadowColor;
                }

                var shadow = levelTxt.GetComponent<UIShadow>();
                if (shadow != null)
                {
                    shadow.effectColor = shadowColor;
                }
            }
        }

        //超值活动红点改变
        private void OnChaoZhiDotChangeFunc(object sender, GameEventArgs e)
        {
            var list = GameEntry.LogicData.ChaoZhiData.GetActivityList(1, false);
            var isShow = list.Count > 0;
            m_btnWorthyActivity.gameObject.SetActive(isShow);
            if (isShow)
            {
                var dotRoot = m_btnWorthyActivity.transform.Find("redDot");
                var dotCount = GameEntry.LogicData.ChaoZhiData.CheckChaoZhiRedDot();
                ToolScriptExtend.SetNumRedDot(dotRoot, dotCount);
            }
        }
        void OnTechChangeUpdate(object sender, GameEventArgs e)
        {
            m_txtTechLine.text = $"{GameEntry.LogicData.TechData.GetTechingCount()}/{GameEntry.LogicData.TechData.GetTechLineCount()}";
        }
        void OnPrivilegeChange(object sender, GameEventArgs e)
        {
            m_txtTechLine.text = $"{GameEntry.LogicData.TechData.GetTechingCount()}/{GameEntry.LogicData.TechData.GetTechLineCount()}";
        }

        //先锋更新
        private void OnPioneerChangeFunc(object sender, GameEventArgs e)
        {
            var dotRoot = m_btnPioneer.transform.Find("redDot");
            var dotCount = GameEntry.LogicData.ChaoZhiData.CheckPioneerRedDot();
            ToolScriptExtend.SetNumRedDot(dotRoot, dotCount);
        }

        //特殊有事件更新
        private void OnSpecialEventChangeFunc(object sender, GameEventArgs e)
        {
            var list = GameEntry.LogicData.ChaoZhiData.GetActivityList(2, false);
            var isShow = list.Count > 0;
            m_btnSpecialEvent.gameObject.SetActive(isShow);
            if (isShow)
            {
                var dotRoot = m_btnSpecialEvent.transform.Find("redDot");
                var dotCount = GameEntry.LogicData.SpecialEventData.CheckSpecialEventRedDot();
                ToolScriptExtend.SetNumRedDot(dotRoot, dotCount);
            }
        }

        void OnTrainPassengerEventArgs(object sender, GameEventArgs e)
        {
            RefreshTruckEntry();
        }

        void RefreshTruckEntry()
        {
            // 货车和火车图标切换
            bool isShowTrainEntry = GameEntry.TradeTruckData.IsShowTrainEntry();
            m_imgTruck.gameObject.SetActive(!isShowTrainEntry);
            m_imgTrain.gameObject.SetActive(isShowTrainEntry);
        }
    }
}
